# Security Headers Configuration
add_header Strict-Transport-Security    "max-age=31536000; includeSubDomains; preload" always;
add_header X-Content-Type-Options       "nosniff" always;
add_header X-XSS-Protection            "1; mode=block" always;
add_header X-Frame-Options            "SAMEORIGIN" always;
add_header Referrer-Policy            "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.google-analytics.com *.googletagmanager.com; style-src 'self' 'unsafe-inline' fonts.googleapis.com; img-src 'self' data: *.google-analytics.com www.google-analytics.com; font-src 'self' fonts.gstatic.com; connect-src 'self' *.google-analytics.com; frame-ancestors 'self'" always;
add_header Permissions-Policy          "geolocation=(), microphone=(), camera=(), payment=()" always;
