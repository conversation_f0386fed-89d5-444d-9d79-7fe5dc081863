# Rate limiting configuration
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=60r/m;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=10r/m;
limit_conn_zone $binary_remote_addr zone=addr:10m;

# Bad bot protection
map $http_user_agent $limit_bots {
    default 0;
    ~*(google|bing|yandex|msnbot|applebot|slurp|duckduckbot|baiduspider) 1;
    ~*(facebookexternalhit|twitterbot|linkedinbot|slackbot|discordbot|telegrambot) 1;
    ~*(ahrefs|semrush|mj12bot|dotbot|rogerbot|seznambot|exabot|gigabot|petalbot) 1;
}

# GeoIP-based access control
geo $limit {
    default 1;
    # Add your whitelisted IPs here
    10.0.0.0/8 0;
    ***********/16 0;
    **********/12 0;
}
