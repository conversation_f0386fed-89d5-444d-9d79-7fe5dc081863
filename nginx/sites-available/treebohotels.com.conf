server {
    listen 80;
    listen [::]:80;
    server_name treebohotels.com www.treebohotels.com support.treebohotels.com;


    if ($host = support.treebohotels.com) {
        return 302 https://treebo.atlassian.net/servicedesk/customer/portal/5$request_uri;
    }

    root /var/www/html;
    index index.html index.htm;

    if ($host = treebohotels.com) {
        return 301 $scheme://www.treebohotels.com$request_uri;
    }

    root /var/www/treebohotels.com;

    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location /api/ {
        proxy_pass http://direct-production-backend;
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn addr 10;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        access_log off;
        try_files $uri =404;
    }

    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
