server {
    listen 80;
    listen [::]:80;
    server_name treebo.com www.treebo.com;
    
    root /var/www/html;
    index index.html index.htm;
    
    if ($host = treebo.com) {
        return 301 $scheme://www.treebo.com$request_uri;
    }
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://direct-production-backend;
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn addr 10;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        access_log off;
        try_files $uri =404;
    }
    
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
