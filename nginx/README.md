# Nginx Configuration for Treebo

This repository contains the Nginx configuration that mirrors the functionality of the HAProxy configuration for Treebo's infrastructure.

## Directory Structure

```
nginx/
├── conf.d/                  # Common configuration snippets
│   ├── proxy-params.conf     # Proxy settings
│   ├── rate-limiting.conf    # Rate limiting configuration
│   └── security-headers.conf # Security headers
├── sites-available/          # Available site configurations
│   ├── treebo.com.conf
│   ├── treebohotels.com.conf
│   └── hotelsuperhero.com.conf
├── sites-enabled/            # Symlinks to enabled sites
├── upstreams/                # Upstream server definitions
│   └── backends.conf
├── www/                      # Web root
├── logs/                     # Nginx logs
├── nginx.conf               # Main Nginx configuration
└── docker-compose.yml       # Docker Compose file
```

## Prerequisites

- Docker and Docker Compose

## Getting Started

1. **Start the Nginx container**
   Add the following to your `/etc/hosts` file:
   ```
   127.0.0.1    treebo.com www.treebo.com
   127.0.0.1    treebohotels.com www.treebohotels.com
   127.0.0.1    hotelsuperhero.com www.hotelsuperhero.com
   ```

3. **Start Nginx with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Verify Nginx is Running**
   ```bash
   docker ps
   docker logs treebo-nginx
   ```

## Features

- **HTTPS by Default**: All HTTP traffic is redirected to HTTPS
- **Security Headers**: Modern security headers for enhanced protection
- **Rate Limiting**: Protection against brute force and DDoS attacks
- **Gzip Compression**: Improved performance with gzip compression
- **HTTP/2 Support**: Faster page loads with HTTP/2
- **Docker Support**: Easy deployment with Docker Compose

## Configuration Details

### Main Nginx Configuration
- Worker processes set to auto
- Connection handling optimized for high traffic
- Custom log format matching HAProxy's format
- SSL/TLS configuration with modern ciphers

### Security Features
- HSTS (HTTP Strict Transport Security)
- X-Content-Type-Options
- X-XSS-Protection
- X-Frame-Options
- Content-Security-Policy
- Rate limiting and connection limiting

### Performance Optimizations
- Keepalive connections
- Gzip compression
- File descriptor caching
- TCP optimizations

## Maintenance

### Checking Nginx Configuration
```bash
docker exec -it treebo-nginx nginx -t
```

### Reloading Nginx
```bash
docker exec -it treebo-nginx nginx -s reload
```

### Viewing Logs
```bash
tail -f logs/access.log
tail -f logs/error.log
```

## Troubleshooting

### Common Issues
1. **Port Already in Use**
   Ensure no other service is using ports 80 or 443
   ```bash
   sudo lsof -i :80
   sudo lsof -i :443
   ```

2. **SSL Configuration**
   SSL termination is handled at the ALB level. No certificate configuration is needed in Nginx.

3. **Nginx Fails to Start**
   Check the error logs for specific issues
   ```bash
   docker logs treebo-nginx
   ```

## License

This configuration is proprietary and confidential information of Treebo Hotels.
