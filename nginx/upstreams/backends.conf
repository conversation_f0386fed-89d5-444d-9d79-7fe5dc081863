# Backend upstream definitions

# Direct production backend
upstream direct-production-backend {
    server direct-p-treebobackend.prod:5700;
    keepalive 32;
}

# Treebo Hotels backend
upstream direct-p-treebohotels {
    server direct-p-treebohotels.prod:80;
    keepalive 32;
}

# Tools backend
upstream direct-p-tools {
    server direct-p-tools.prod:80;
    keepalive 32;
}

# Corpbot backend
upstream direct-p-corpbot {
    server direct-p-corpbot.prod:80;
    keepalive 32;
}

# WWW2 backend
upstream direct-p-www2 {
    server direct-p-www2.prod:80;
    keepalive 32;
}

# Payments backend
upstream direct-payments {
    server direct-payments.prod:80;
    keepalive 32;
}

# Rewards backend
upstream direct-p-reward {
    server direct-p-reward.prod:80;
    keepalive 32;
}

# Wallet backend
upstream direct-p-wallet {
    server direct-p-wallet.prod:81;
    keepalive 32;
}

# External API Gateway
upstream direct-p-external-apigateway {
    server direct-p-external-apigateway.prod:80;
    keepalive 32;
}

# CRS Fraud backend
upstream crs-p-fraud {
    server crs-p-fraud.prod:80;
    keepalive 32;
}

# RMS PO backend
upstream rms-p-po {
    server rms-p-po.prod:80;
    keepalive 32;
}

# RMS ITS backend
upstream rms-p-its {
    server rms-p-its.prod:80;
    keepalive 32;
}

# B2B Prowl backend
upstream b2b-p-prowl {
    server b2b-p-prowl.prod:80;
    keepalive 32;
}

# Shared AuthZ backend
upstream shared-p-authz {
    server shared-p-authz.prod:80;
    keepalive 32;
}

# RMS Koppan backend
upstream rms-p-koopan {
    server rms-p-koopan.prod:80;
    keepalive 32;
}

# Cybertron backend
upstream crs-p-cybertron-ironhide-apigateway-superhero {
    server crs-p-cybertron-ironhide-apigateway-superhero.prod:80;
    keepalive 32;
}

# Tenants backend
upstream crs-p-tenants {
    server crs-p-tenants.prod:80;
    keepalive 32;
}

# Customer support backend
upstream direct-p-customer-support {
    server direct-p-customer-support.prod:80;
    keepalive 32;
}
