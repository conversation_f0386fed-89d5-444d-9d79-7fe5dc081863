user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 100000;
    multi_accept on;
    use epoll;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '{"timestamp":"$time_iso8601","frontend_name":"$server_name","backend_name":"$upstream_addr","client_ip":"$http_x_forwarded_for","http_status_code":$status,"bytes_read":$body_bytes_sent,"referer":"$http_referer","user_agent":"$http_user_agent","request_time":$request_time,"upstream_response_time":$upstream_response_time,"upstream_connect_time":$upstream_connect_time,"request":"$request","http_x_forwarded_proto":"$http_x_forwarded_proto"}';

    access_log /var/log/nginx/access.log main buffer=32k flush=5m;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 300s;
    keepalive_requests 1000;
    reset_timedout_connection on;
    server_tokens off;
    client_max_body_size 100m;
    client_body_timeout 2m;
    client_header_timeout 2m;
    send_timeout 5m;
    types_hash_max_size 2048;
    server_names_hash_bucket_size 64;
    server_names_hash_max_size 4096;
    
    limit_req_zone $binary_remote_addr zone=one:10m rate=60r/s;
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    limit_req_status 429;
    limit_conn_status 429;

    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript application/vnd.ms-fontobject application/x-font-ttf font/opentype image/svg+xml image/x-icon;

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/upstreams/*.conf;
    include /etc/nginx/custom/*.conf;
}
