# Values for treebo-tools-common cluster
global:
  cluster: "treebo-tools-common"
  environment: "production"
  # targetRevision: "tuning"
  annotations:
    notifications.argoproj.io/subscribe.on-sync-failed.slack: "devops-critical"
  labels:
    environment: "production"
    cluster: "tools-common"

applications:
  directories:
    - name: "ebs-csi-driver"
      path: "tools/ebs-csi-driver/overlays/treebo-tools-common"
      syncWave: 0
    - name: "external-secrets"
      path: "tools/external-secrets/overlays/treebo-tools-common"
      syncWave: 0
    - name: "metrics-server"
      path: "tools/metrics-server/base"
      syncWave: 0
    - name: "cert-manager"
      path: "tools/cert-manager/overlays/treebo-tools-common"
      syncWave: 0
    # - name: "argocd"
    #   path: "tools/argocd/overlays/treebo-tools-common"
    #   syncWave: 0
    - name: "aws-load-balancer-controller"
      path: "tools/aws-load-balancer-controller/overlays/treebo-tools-common"
      syncWave: 5
    - name: "nginx"
      path: "tools/nginx/overlays/treebo-tools-common"
      syncWave: 5
    - name: "external-dns"
      path: "tools/external-dns/overlays/treebo-tools-common"
      syncWave: 5
    - name: "prometheus"
      path: "tools/prometheus/overlays/treebo-tools-common"
      syncWave: 10
    - name: "tempo"
      path: "tools/tempo/overlays/treebo-tools-common"
      syncWave: 10
    - name: "loki"
      path: "tools/loki/overlays/treebo-tools-common"
      syncWave: 10
    - name: "mimir"
      path: "tools/mimir/overlays/treebo-tools-common"
      syncWave: 10
    - name: "pyroscope"
      path: "tools/pyroscope/overlays/treebo-tools-common"
      syncWave: 10
    - name: "grafana"
      path: "tools/grafana/overlays/treebo-tools-common"
      syncWave: 15
    - name: "typesense-ha"
      path: "tools/typesense-ha/overlays/treebo-tools-common"
      syncWave: 20
    - name: "typesense-dashboard"
      path: "tools/typesense-dashboard/overlays/treebo-tools-common"
      syncWave: 21
    # - name: "drone"
    #   path: "tools/drone/overlays/treebo-tools-common"
    #   syncWave: 20
    - name: "vaultwarden"
      path: "tools/vaultwarden/overlays/treebo-tools-common"
      syncWave: 20
    # - name: "flowise"
    #   path: "tools/flowise/overlays/treebo-tools-common"
    #   syncWave: 20
    - name: "apache-devlake"
      path: "tools/apache-devlake/overlays/treebo-tools-common"
      syncWave: 20
    # - name: "sonarqube-community"
    #   path: "tools/sonarqube-community/overlays/treebo-tools-common"
    #   syncWave: 20
    # - name: "infisical"
    #   path: "tools/infisical/overlays/treebo-tools-common"
    #   syncWave: 20
    # - name: "unleash"
    #   path: "tools/unleash/overlays/treebo-tools-common"
    #   syncWave: 20
    - name: "mariadb"
      path: "tools/mariadb/overlays/treebo-tools-common"
      syncWave: 25
    - name: "redis"
      path: "tools/redis/overlays/treebo-tools-common"
      syncWave: 25
    - name: "postgresql"
      path: "tools/postgresql/overlays/treebo-tools-common"
      syncWave: 25
    # - name: "strimzi-kafka-operator"
    #   path: "tools/strimzi-kafka-operator/overlays/treebo-tools-common"
    #   syncWave: 30
    # - name: "clickhouse-altinity-operator"
    #   path: "tools/clickhouse-altinity-operator/overlays/treebo-tools-common"
    #   syncWave: 30
    - name: "reloader"
      path: "tools/reloader/overlays/treebo-tools-common"
      syncWave: 35
    - name: woodpecker
      path: "tools/woodpecker/overlays/treebo-tools-common"
      syncWave: 35
    - name: "woodpecker-toleration-webhook"
      path: "tools/woodpecker-toleration-webhook/overlays/treebo-tools-common"
      syncWave: 35
    - name: promtail
      path: "tools/promtail/overlays/treebo-tools-common"
      syncWave: 40
    - name: opentelemetry-collector
      path: "tools/opentelemetry-collector/overlays/treebo-tools-common"
      syncWave: 40
    - name: velero
      path: "tools/velero/overlays/treebo-tools-common"
      syncWave: 40
    - name: keda
      path: "tools/keda/overlays/treebo-tools-common"
      syncWave: 10
    - name: efs-csi-driver
      path: "tools/efs-csi-driver/overlays/treebo-tools-common"
      syncWave: 0
    - name: volume-attachment-cleaner
      path: "tools/volume-attachment-cleaner"
      syncWave: 0
    - name: nexus
      path: "apps/nexus/overlays/treebo-tools-common"
      syncWave: 50
    - name: opentelemetry-demo
      path: "tools/opentelemetry-demo/overlays/treebo-tools-common"
      syncWave: 50
    - name: goldilocks
      path: "tools/goldilocks/overlays/treebo-tools-common"
      syncWave: 50
    - name: plane
      path: "tools/plane/overlays/treebo-tools-common"
      syncWave: 50
