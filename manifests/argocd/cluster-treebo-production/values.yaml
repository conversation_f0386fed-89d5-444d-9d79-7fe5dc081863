# Values for treebo-production cluster
global:
  cluster: "treebo-production"
  environment: "production"
  annotations:
    notifications.argoproj.io/subscribe.on-sync-failed.slack: "production-alerts"
    notifications.argoproj.io/subscribe.on-health-degraded.slack: "production-alerts"
  labels:
    environment: "production"
    criticality: "high"

applications:
  directories:
    # Infrastructure - Wave 0
    - name: "external-secrets"
      path: "tools/external-secrets/overlays/treebo-production"
      syncWave: 0

    - name: "ebs-csi-driver"
      path: "tools/ebs-csi-driver/overlays/treebo-production"
      syncWave: 0

    - name: "efs-csi-driver"
      path: "tools/efs-csi-driver/overlays/treebo-production"
      syncWave: 0

    - name: "metrics-server"
      path: "tools/metrics-server/base"
      syncWave: 0

    # Platform - Wave 5
    - name: "aws-load-balancer-controller"
      path: "tools/aws-load-balancer-controller/overlays/treebo-production"
      syncWave: 5

    - name: "external-dns"
      path: "tools/external-dns/overlays/treebo-production"
      syncWave: 5

    - name: "nginx"
      path: "tools/nginx/overlays/treebo-production"
      syncWave: 5

    # Applications - Wave 10
    - name: "auth"
      path: "apps/auth/overlays/treebo-production"
      syncWave: 10

    - name: "rewards-service"
      path: "apps/rewards-service/overlays/treebo-production"
      syncWave: 10

    - name: "wallet"
      path: "apps/wallet/overlays/treebo-production"
      syncWave: 10

    # Observability - Wave 15
    - name: "opentelemetry-collector"
      path: "tools/opentelemetry-collector/overlays/treebo-production"
      syncWave: 15

    - name: "promtail"
      path: "tools/promtail/overlays/treebo-production"
      syncWave: 15
