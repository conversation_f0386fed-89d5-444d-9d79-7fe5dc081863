# Values for treebo-staging cluster
global:
  cluster: "treebo-staging"
  environment: "staging"
  # targetRevision: "tools-4"
  annotations:
    notifications.argoproj.io/subscribe.on-sync-failed.slack: "devops-alerts"
  labels:
    environment: "staging"

applications:
  directories:
    # Infrastructure - Wave 0
    - name: "external-secrets"
      path: "tools/external-secrets/overlays/treebo-staging"
      syncWave: 0

    - name: "ebs-csi-driver"
      path: "tools/ebs-csi-driver/overlays/treebo-staging"
      syncWave: 0

    - name: "efs-csi-driver"
      path: "tools/efs-csi-driver/overlays/treebo-staging"
      syncWave: 0

    - name: "metrics-server"
      path: "tools/metrics-server/base"
      syncWave: 0

    # Platform - Wave 5
    - name: "aws-load-balancer-controller"
      path: "tools/aws-load-balancer-controller/overlays/treebo-staging"
      syncWave: 5

    - name: "external-dns"
      path: "tools/external-dns/overlays/treebo-staging"
      syncWave: 5
      description: "External DNS for automatic DNS record management"

    - name: "nginx"
      path: "tools/nginx/overlays/treebo-staging"
      syncWave: 5

    - name: "opentelemetry-collector"
      path: "tools/opentelemetry-collector/overlays/treebo-staging"
      syncWave: 5

    - name: "promtail"
      path: "tools/promtail/overlays/treebo-staging"
      syncWave: 5

    - name: "typesense-ha"
      path: "tools/typesense-ha/overlays/treebo-staging"
      syncWave: 5

    # Applications
    - name: "pricing-orchestrator"
      path: "apps/pricing-orchestrator/overlays/treebo-staging"
      syncWave: 10

    - name: keda
      path: "tools/keda/overlays/treebo-staging"
      syncWave: 10
    - name: "cert-manager"
      path: "tools/cert-manager/overlays/treebo-staging"
      syncWave: 0
    - name: argocd
      path: "tools/argocd/overlays/treebo-staging"
      syncWave: 0
