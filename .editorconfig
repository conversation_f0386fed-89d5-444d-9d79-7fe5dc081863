# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# TypeScript and JavaScript files
[*.{ts,js,tsx,jsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Package files
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[*.{toml,ini}]
indent_style = space
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2

# Dockerfile
[Dockerfile*]
indent_style = space
indent_size = 2

# Makefile (requires tabs)
[Makefile]
indent_style = tab
indent_size = 4
