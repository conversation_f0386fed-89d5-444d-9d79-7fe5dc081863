{
  // Spell check
  "cSpell.words": ["ingester", "rollouts"],

  // Editor settings
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "editor.rulers": [100],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 100,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports.biome": "explicit",
    "quickfix.biome": "explicit"
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "biomejs.biome",

  // File settings
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "files.trimFinalNewlines": true,

  // Language specific settings
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports.biome": "explicit",
      "quickfix.biome": "explicit"
    }
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports.biome": "explicit",
      "quickfix.biome": "explicit"
    }
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true
  },
  "[yaml]": {
    "editor.defaultFormatter": "redhat.vscode-yaml",
    "editor.formatOnSave": true,
    "editor.insertSpaces": true,
    "editor.tabSize": 2
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "files.trimTrailingWhitespace": false
  },

  // TypeScript settings
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",

  // ESLint settings
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.format.enable": false,

  // Prettier settings (for YAML and other non-JS files)
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": true,

  // Biome settings
  "biome.enabled": true,
  "biome.lspBin": "./node_modules/@biomejs/biome/bin/biome",

  // Search settings
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/coverage": true,
    "**/*.generated.*": true,
    "**/*.gen.*": true,
    "**/charts": true,
    "**/vendor": true,
    "**/cdk.out": true
  },

  // File associations
  "files.associations": {
    "*.yaml": "yaml",
    "*.yml": "yaml",
    "Dockerfile*": "dockerfile",
    ".prettierrc": "json",
    ".eslintrc": "json",
    "biome.json": "jsonc"
  }
}
