{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "declaration": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "removeComments": false, "noEmitOnError": true, "resolveJsonModule": true, "types": ["node"]}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.generated.*", "**/*.gen.*", "charts", "vendor", "cdk.out"]}