import * as cdk from "aws-cdk-lib";
import { createClusterContext } from "../lib/helpers/cluster-context";
import { AWS_MUMBAI_REGION, AWS_ROOT_ACCOUNT_ID, AWS_SINGAPORE_REGION, OLD_DIRECT_STAGING_VPC_NAME, SUPERHERO_STAGING_VPC_INFO, TOOLS_COMMON_VPC_NAME, VPN_VPC_NAME } from "../lib/helpers/constants";
import { fetchVpcAttributesByName } from "../lib/helpers/vpc-helper";
import { TreeboBaseStack } from "../lib/stacks/base-eks-stack";

async function main() {
  const commonPrefix = "treebo-staging";

  const commonTags = {};

  const vpnVpInfo = await fetchVpcAttributesByName(VPN_VPC_NAME, AWS_MUMBAI_REGION);

  // Option: Keep existing VPN connection only
  // const peeringVpcs = [];

  // Option: Add new peering VPCs alongside existing VPN
  const peeringVpcs = [
    await fetchVpcAttributesByName(TOOLS_COMMON_VPC_NAME, AWS_MUMBAI_REGION),
    // SUPERHERO_STAGING_VPC_INFO, // Cross-account VPC - use static config, not fetch
    // await fetchVpcAttributesByName(OLD_DIRECT_STAGING_VPC_NAME, AWS_SINGAPORE_REGION),
  ]

  const context = createClusterContext({
    commonPrefix,
    region: AWS_MUMBAI_REGION,
    accountId: AWS_ROOT_ACCOUNT_ID,
    environment: "staging",
    tags: commonTags,
    vpcCidrBlock: "10.80.0.0/16",
    vpnVpc: vpnVpInfo, // Keep existing VPN connection
    peeringVpcs, // Add new peering connections
  });

  const app = new cdk.App();

  new TreeboBaseStack(app, `stack-${commonPrefix}`, context);
}

main();
