import * as crypto from "node:crypto";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import type * as eks from "aws-cdk-lib/aws-eks";
import * as iam from "aws-cdk-lib/aws-iam";
import type { ClusterExecutionContext } from "./cluster-context";

/**
 * Adds a node group to the given EKS cluster with a unique name based on the arguments.
 * Avoids unnecessary updates if the configuration hasn't changed.
 */
export function addNodeGroup(
  cluster: eks.Cluster,
  context: ClusterExecutionContext,
  capacityType: eks.CapacityType,
  instanceTypes: ec2.InstanceType[],
  sizePrefs: { desiredSize: number; minSize: number; maxSize: number },
  launchTemplate: ec2.ILaunchTemplate,
  nodeType: string, // Required for workload placement strategy
  additionalTags?: Record<string, string>,
  taints?: eks.TaintSpec[]
) {
  const nameGenerator = context.nameGenerator;

  const defaultSizePrefs = { desiredSize: 1, minSize: 1, maxSize: 2 };
  const finalSizePrefs = { ...defaultSizePrefs, ...sizePrefs };

  const configHash = createStableConfigHash(
    capacityType,
    instanceTypes,
    launchTemplate,
    nodeType,
    taints
  );

  const nodegroupName = nameGenerator.getNodeGroupName(`${nodeType}-${configHash}`);

  // Simplified approach: Use hash-based naming for nodegroups
  // CDK will handle lifecycle automatically - same config = same name = no changes
  const nodegroupConfig: eks.NodegroupOptions = {
    desiredSize: finalSizePrefs.desiredSize,
    minSize: finalSizePrefs.minSize,
    maxSize: finalSizePrefs.maxSize,
    subnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
    nodegroupName,
    capacityType,
    instanceTypes: instanceTypes,
    launchTemplateSpec: {
      id: launchTemplate.launchTemplateId!,
      version: launchTemplate.versionNumber,
    },
    labels: {
      "pod-capacity-type": capacityType,
      "node-type": nodeType,
    },
    tags: {
      // NOTE: Cluster autoscaler tags removed - Karpenter manages application node groups
      // Only SystemNodeGroup should have cluster autoscaler tags
      "node-type": nodeType,
      "karpenter.sh/discovery": nameGenerator.getClusterName(), // For Karpenter discovery
      ...nameGenerator.getResourceTags("nodegroup", additionalTags),
    },
    ...(taints && taints.length > 0 && { taints }),
  };

  // Create the nodegroup
  const nodegroup = cluster.addNodegroupCapacity(nodegroupName, nodegroupConfig);

  // Add essential EC2 permissions to the nodegroup role
  nodegroup.role.addToPrincipalPolicy(
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        // Essential EC2 permissions for node operations
        "ec2:DescribeInstances",
        "ec2:DescribeInstanceAttribute",
        "ec2:DescribeRegions",
        "ec2:DescribeAvailabilityZones",
      ],
      resources: ["*"],
      sid: "NodeEssentialEC2Permissions",
    })
  );

  return nodegroup;
}

function createStableConfigHash(
  capacityType: eks.CapacityType,
  instanceTypes: ec2.InstanceType[],
  launchTemplate: ec2.ILaunchTemplate,
  nodeType: string,
  taints?: eks.TaintSpec[]
): string {
  // Create a string representation of the configuration
  const instanceTypeNames = instanceTypes
    .map(it => it.toString())
    .sort()
    .join(",");
  const taintsString = taints
    ? taints
        .map(t => `${t.key}:${t.value}:${t.effect}`)
        .sort()
        .join(",")
    : "";

  const configString = [
    capacityType,
    instanceTypeNames,
    launchTemplate.launchTemplateId,
    launchTemplate.versionNumber,
    nodeType,
    taintsString,
  ].join("|");

  // Create a hash of the configuration
  const hash = crypto.createHash("sha256").update(configString).digest("hex");

  // Return a short version of the hash
  return hash.substring(0, 8);
}
