import boto3
import json
import logging
import time
from typing import Dict, List, Any

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def handler(event: Dict[str, Any], context: Any) -> Dict[str, str]:
    """
    Lambda function to cleanup ALBs created by AWS Load Balancer Controller
    before CloudFormation stack deletion.
    """
    logger.info(f"Received event: {json.dumps(event, default=str)}")
    
    request_type = event.get('RequestType', '')
    cluster_name = event.get('ResourceProperties', {}).get('ClusterName', '')
    
    if request_type == 'Delete':
        try:
            cleanup_albs(cluster_name)
            return {
                'PhysicalResourceId': f'alb-cleanup-{cluster_name}',
                'Status': 'SUCCESS',
                'Reason': 'ALB cleanup completed successfully'
            }
        except Exception as e:
            logger.error(f"Error during ALB cleanup: {str(e)}")
            return {
                'PhysicalResourceId': f'alb-cleanup-{cluster_name}',
                'Status': 'FAILED',
                'Reason': f'ALB cleanup failed: {str(e)}'
            }
    else:
        # For Create/Update, just return success
        return {
            'PhysicalResourceId': f'alb-cleanup-{cluster_name}',
            'Status': 'SUCCESS',
            'Reason': f'ALB cleanup resource created for cluster {cluster_name}'
        }

def cleanup_albs(cluster_name: str) -> None:
    """
    Find and delete ALBs created by the AWS Load Balancer Controller for the given cluster.
    """
    if not cluster_name:
        logger.warning("No cluster name provided, skipping ALB cleanup")
        return
    
    elbv2_client = boto3.client('elbv2')
    logger.info(f"Starting ALB cleanup for cluster: {cluster_name}")
    
    try:
        # Get all load balancers
        paginator = elbv2_client.get_paginator('describe_load_balancers')
        albs_to_delete = []
        
        for page in paginator.paginate():
            for alb in page.get('LoadBalancers', []):
                if should_delete_alb(elbv2_client, alb, cluster_name):
                    albs_to_delete.append(alb)
        
        if not albs_to_delete:
            logger.info(f"No ALBs found for cluster {cluster_name}")
            return
        
        # Delete ALBs
        for alb in albs_to_delete:
            delete_alb_with_retry(elbv2_client, alb)
        
        logger.info(f"Successfully cleaned up {len(albs_to_delete)} ALBs for cluster {cluster_name}")
        
    except Exception as e:
        logger.error(f"Error during ALB cleanup: {str(e)}")
        raise

def should_delete_alb(elbv2_client: boto3.client, alb: Dict[str, Any], cluster_name: str) -> bool:
    """
    Check if an ALB should be deleted based on its tags.
    """
    try:
        alb_arn = alb['LoadBalancerArn']
        
        # Get tags for the ALB
        response = elbv2_client.describe_tags(ResourceArns=[alb_arn])
        tags = {}
        
        if response.get('TagDescriptions'):
            for tag in response['TagDescriptions'][0].get('Tags', []):
                tags[tag['Key']] = tag['Value']
        
        # Check for AWS Load Balancer Controller tags
        cluster_tag_key = f'kubernetes.io/cluster/{cluster_name}'
        elbv2_cluster_tag = 'elbv2.k8s.aws/cluster'
        
        # ALB belongs to this cluster if it has the cluster tag
        if cluster_tag_key in tags:
            logger.info(f"Found ALB {alb['LoadBalancerName']} with cluster tag {cluster_tag_key}")
            return True
        
        if elbv2_cluster_tag in tags and tags[elbv2_cluster_tag] == cluster_name:
            logger.info(f"Found ALB {alb['LoadBalancerName']} with elbv2 cluster tag")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error checking ALB tags for {alb.get('LoadBalancerName', 'unknown')}: {str(e)}")
        return False

def delete_alb_with_retry(elbv2_client: boto3.client, alb: Dict[str, Any], max_retries: int = 3) -> None:
    """
    Delete an ALB with retry logic.
    """
    alb_arn = alb['LoadBalancerArn']
    alb_name = alb['LoadBalancerName']
    
    for attempt in range(max_retries):
        try:
            logger.info(f"Attempting to delete ALB {alb_name} (attempt {attempt + 1}/{max_retries})")
            
            elbv2_client.delete_load_balancer(LoadBalancerArn=alb_arn)
            logger.info(f"Successfully initiated deletion of ALB {alb_name}")
            return
            
        except elbv2_client.exceptions.LoadBalancerNotFoundException:
            logger.info(f"ALB {alb_name} already deleted")
            return
            
        except Exception as e:
            logger.warning(f"Attempt {attempt + 1} failed to delete ALB {alb_name}: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
            else:
                logger.error(f"Failed to delete ALB {alb_name} after {max_retries} attempts")
                raise
