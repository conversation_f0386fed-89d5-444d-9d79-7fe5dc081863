import type { ClusterExecutionContext } from "./cluster-context";

class NameGenerator {
  private readonly context: ClusterExecutionContext;

  constructor(context: ClusterExecutionContext) {
    this.context = context;
  }

  get clusterName(): string {
    return this.getClusterName();
  }

  get resourcePrefix(): string {
    return this.context.commonPrefix;
  }

  getClusterName(): string {
    return `cluster-${this.resourcePrefix}`;
  }

  getNodeGroupName(suffix?: string): string {
    const base = `nodegroup-${this.resourcePrefix}`;
    return suffix ? `${base}-${suffix}` : base;
  }

  getLaunchTemplateName(suffix?: string): string {
    const base = `launch-template-${this.resourcePrefix}`;
    return suffix ? `${base}-${suffix}` : base;
  }

  getServiceAccountRoleName(namespace: string): string {
    return `role-${namespace}-${this.resourcePrefix}`;
  }

  getServiceAccountPolicyName(namespace: string): string {
    return `policy-${namespace}-${this.resourcePrefix}`;
  }

  getClusterServiceRoleName(): string {
    return `role-cluster-service-${this.resourcePrefix}`;
  }

  getNodeGroupRoleName(): string {
    return `role-nodegroup-${this.resourcePrefix}`;
  }

  getClusterAutoscalerRoleName(): string {
    return `role-cluster-autoscaler-${this.resourcePrefix}`;
  }

  getVpcName(): string {
    return `vpc-${this.resourcePrefix}`;
  }

  getSubnetName(type: "public" | "private", az: string): string {
    return `subnet-${type}-${this.resourcePrefix}-${az}`;
  }

  getInternetGatewayName(): string {
    return `igw-${this.resourcePrefix}`;
  }

  getNatGatewayName(az: string): string {
    return `nat-${this.resourcePrefix}-${az}`;
  }

  getRouteTableName(type: "public" | "private", az?: string): string {
    const base = `rt-${type}-${this.resourcePrefix}`;
    return az ? `${base}-${az}` : base;
  }

  getSecurityGroupName(purpose: string): string {
    return `sec-grp-${purpose}-${this.resourcePrefix}`;
  }

  getAlbName(suffix?: string): string {
    const base = `alb-${this.resourcePrefix}`;
    return suffix ? `${base}-${suffix}` : base;
  }

  getTargetGroupName(service: string): string {
    return `tg-${service}-${this.resourcePrefix}`;
  }

  getLogGroupName(service: string): string {
    return `/aws/eks/cluster-${this.resourcePrefix}/${service}`;
  }

  getDashboardName(): string {
    return `dashboard-${this.resourcePrefix}`;
  }

  getSecretName(purpose: string): string {
    return `secret-${purpose}-${this.resourcePrefix}`;
  }

  getParameterName(purpose: string): string {
    return `/parameter/${this.resourcePrefix}/${purpose}`;
  }

  getResourceName(resourceType: string, suffix?: string): string {
    const base = `${resourceType}-${this.resourcePrefix}`;
    return suffix ? `${base}-${suffix}` : base;
  }

  getCommonTags(): Record<string, string> {
    return {
      Cluster: this.clusterName,
      Environment: this.context.environment ?? "production",
      Region: this.context.region,
      CommonPrefix: this.context.commonPrefix,
      ...this.context.tags,
    };
  }

  getResourceTags(
    resourceType: string,
    additionalTags?: Record<string, string>
  ): Record<string, string> {
    return {
      ...this.getCommonTags(),
      ResourceType: resourceType,
      ...additionalTags,
    };
  }
}

export type { NameGenerator };

export function createNameGenerator(context: ClusterExecutionContext): NameGenerator {
  return new NameGenerator(context);
}
