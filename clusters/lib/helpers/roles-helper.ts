import fs from "node:fs";
import path from "node:path";
import * as sdkiam from "@aws-sdk/client-iam";
import * as cdk from "aws-cdk-lib";
import type * as eks from "aws-cdk-lib/aws-eks";
import * as cdkiam from "aws-cdk-lib/aws-iam";
import type { Construct } from "constructs";
import { z } from "zod";
import type { ClusterExecutionContext } from "./cluster-context";

export const PolicyStatementSchema = z
  .object({
    Effect: z.enum(["Allow", "Deny"]),
    Action: z.array(z.string()).min(1, "At least one action is required"),
    Resource: z.union([
      z.string().min(1, "Resource string cannot be empty"),
      z.array(z.string().min(1)).min(1, "Resource array cannot be empty"),
    ]),
  })
  .catchall(z.any()); // Allow additional properties

const PolicyDocumentSchema = z.object({
  Version: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Version must be in YYYY-MM-DD format"),
  Statement: z.array(PolicyStatementSchema).min(1, "At least one statement is required"),
  ManagedPolicyArns: z.array(z.string().min(1, "Managed policy ARN cannot be empty")).optional(),
});

export type TreeboAWSPolicyDocument = z.infer<typeof PolicyDocumentSchema>;

const ServiceAccountSetupConfigSchema = z.object({
  toolName: z
    .string()
    .min(1, "Tool name is required")
    .regex(/^[a-z0-9-]+$/, "Tool name must contain only lowercase letters, numbers, and hyphens"),
  namespace: z
    .string()
    .min(1, "Namespace is required")
    .regex(/^[a-z0-9-]+$/, "Invalid namespace format"),
  serviceAccountName: z
    .string()
    .min(1, "Service account name is required")
    .regex(/^[a-z0-9-]+$/, "Invalid service account name format"),
  policyPath: z
    .string()
    .min(1, "Policy path is required")
    .regex(/\.json$/, "Policy path must end with .json"),
  policyUpdater: z
    .function()
    .args(PolicyDocumentSchema, z.string())
    .returns(PolicyDocumentSchema)
    .optional(),
});

type ServiceAccountSetupConfig = z.infer<typeof ServiceAccountSetupConfigSchema>;

const TOOL_CONFIGS: Record<string, ServiceAccountSetupConfig> = {
  "cluster-autoscaler": {
    toolName: "cluster-autoscaler",
    namespace: "kube-system",
    serviceAccountName: "cluster-autoscaler-sa",
    policyPath: "tools/cluster-autoscaler/policy.json",
  },
  "external-secrets": {
    toolName: "external-secrets",
    namespace: "external-secrets",
    serviceAccountName: "external-secrets-sa",
    policyPath: "tools/external-secrets/policy.json",
  },
  "aws-load-balancer-controller": {
    toolName: "aws-load-balancer-controller",
    namespace: "kube-system",
    serviceAccountName: "aws-load-balancer-controller",
    policyPath: "tools/aws-load-balancer-controller/policy.json",
  },
  "ebs-csi-driver": {
    toolName: "ebs-csi-driver",
    namespace: "kube-system",
    serviceAccountName: "ebs-csi-controller-sa",
    policyPath: "tools/ebs-csi-driver/policy.json",
  },
  "external-dns": {
    toolName: "external-dns",
    namespace: "external-dns",
    serviceAccountName: "external-dns-sa",
    policyPath: "tools/external-dns/policy.json",
  },
  drone: {
    toolName: "drone-runner",
    namespace: "drone",
    serviceAccountName: "drone-runner-sa",
    policyPath: "tools/drone/policy.json",
  },
  "woodpecker-agent": {
    toolName: "woodpecker-agent",
    namespace: "woodpecker",
    serviceAccountName: "woodpecker-agent-sa",
    policyPath: "tools/woodpecker/policy.json",
  },
  "efs-csi-driver-controller": {
    toolName: "efs-csi-driver-controller",
    namespace: "kube-system",
    serviceAccountName: "efs-csi-controller-sa",
    policyPath: "tools/efs-csi-driver/controller-policy.json",
  },
  "efs-csi-driver-node": {
    toolName: "efs-csi-driver-node",
    namespace: "kube-system",
    serviceAccountName: "efs-csi-node-sa",
    policyPath: "tools/efs-csi-driver/node-policy.json",
  },
  "uptime-kuma": {
    toolName: "uptime-kuma",
    namespace: "uptime-kuma",
    serviceAccountName: "uptime-kuma-sa",
    policyPath: "tools/uptime-kuma/policy.json",
  },

};

export function cdkIRSARequiredRolesInIam(
  scope: Construct,
  cluster: eks.Cluster,
  context: ClusterExecutionContext
): Record<string, cdkiam.Role> {
  const nameGenerator = context.nameGenerator;
  const roles: Record<string, cdkiam.Role> = {};

  // Get OIDC provider for the cluster
  const oidcProvider = cluster.openIdConnectProvider;
  const oidcProviderArn = oidcProvider.openIdConnectProviderArn;
  const oidcIssuerHostname = oidcProvider.openIdConnectProviderIssuer.replace("https://", "");

  // Create roles for each tool in TOOL_CONFIGS
  for (const [toolName, toolConfig] of Object.entries(TOOL_CONFIGS)) {
    console.log(`Creating IAM role for ${toolConfig.toolName}...`);

    const roleName = nameGenerator.getServiceAccountRoleName(toolName);

    const conditions = new cdk.CfnJson(scope, `${toolName}OidcCondition`, {
      value: {
        [`${oidcIssuerHostname}:sub`]: `system:serviceaccount:${toolConfig.namespace}:${toolConfig.serviceAccountName}`,
        [`${oidcIssuerHostname}:aud`]: "sts.amazonaws.com",
      },
    });

    console.log(`Creating role: ${roleName}`);

    // Create the role with OIDC provider as the principal
    const role = new cdkiam.Role(scope, `${toolName}ServiceAccountRole`, {
      roleName,
      assumedBy: new cdkiam.WebIdentityPrincipal(oidcProviderArn, {
        StringEquals: conditions,
      }),
      description: `IRSA role for ${toolName} in ${toolConfig.namespace} namespace`,
    });

    // Read policy from JSON file
    const policyPath = path.resolve(process.cwd(), toolConfig.policyPath);
    if (fs.existsSync(policyPath)) {
      let policyDocument = JSON.parse(fs.readFileSync(policyPath, "utf8"));

      // Apply policy updates if configured
      if (toolConfig.policyUpdater) {
        policyDocument = toolConfig.policyUpdater(policyDocument, nameGenerator.getClusterName());
      }

      // Add policy statements to the role
      for (const statement of policyDocument.Statement) {
        role.addToPolicy(
          new cdkiam.PolicyStatement({
            effect: statement.Effect === "Allow" ? cdkiam.Effect.ALLOW : cdkiam.Effect.DENY,
            actions: Array.isArray(statement.Action) ? statement.Action : [statement.Action],
            resources: Array.isArray(statement.Resource)
              ? statement.Resource
              : [statement.Resource],
            conditions: statement.Condition,
          })
        );
      }

      // Attach managed policies if specified
      if (policyDocument.ManagedPolicyArns && Array.isArray(policyDocument.ManagedPolicyArns)) {
        for (const policyArn of policyDocument.ManagedPolicyArns) {
          role.addManagedPolicy(
            cdkiam.ManagedPolicy.fromManagedPolicyArn(
              scope,
              `${toolName}-${policyArn.split("/").pop()}`,
              policyArn
            )
          );
        }
      }
    } else {
      console.warn(`Policy file not found: ${policyPath} for tool ${toolName}`);
    }

    roles[toolName] = role;
  }

  return roles;
}

export async function sdkCreateRolesForS3Buckets(
  iamClient: sdkiam.IAMClient,
  roleName: string,
  accountId: string,
  oidcProviderArn: string,
  toolServiceAccountName: string,
  toolNameSpace: string,
  policyDocument: TreeboAWSPolicyDocument
): Promise<void> {
  console.log(`Creating role: ${roleName} - ${JSON.stringify(policyDocument)}`);

  const assumeRolePolicyDocument = {
    Version: "2012-10-17",
    Statement: [
      {
        Effect: "Allow",
        Principal: { Federated: `arn:aws:iam::${accountId}:oidc-provider/${oidcProviderArn}` },
        Action: "sts:AssumeRoleWithWebIdentity",
        Condition: {
          StringEquals: {
            [`${oidcProviderArn}:sub`]: `system:serviceaccount:${toolNameSpace}:${toolServiceAccountName}`,
            [`${oidcProviderArn}:aud`]: "sts.amazonaws.com",
          },
        },
      },
    ],
  };

  try {
    await iamClient.send(
      new sdkiam.CreateRoleCommand({
        RoleName: roleName,
        AssumeRolePolicyDocument: JSON.stringify(assumeRolePolicyDocument),
      })
    );
    console.log(`Role ${roleName} created successfully`);
  } catch (error) {
    if (error instanceof sdkiam.EntityAlreadyExistsException) {
      console.log(`Role ${roleName} already exists, updating policy...`);
      await iamClient.send(
        new sdkiam.UpdateAssumeRolePolicyCommand({
          RoleName: roleName,
          PolicyDocument: JSON.stringify(assumeRolePolicyDocument),
        })
      );
    } else {
      throw error;
    }
  }

  await iamClient.send(
    new sdkiam.PutRolePolicyCommand({
      RoleName: roleName,
      PolicyName: `${roleName}-S3Policy`,
      PolicyDocument: JSON.stringify(policyDocument),
    })
  );

  return Promise.resolve();
}
