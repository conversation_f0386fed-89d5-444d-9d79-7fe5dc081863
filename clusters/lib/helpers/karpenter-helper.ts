import * as path from "node:path";
import * as cdk from "aws-cdk-lib";
import type * as eks from "aws-cdk-lib/aws-eks";
import * as iam from "aws-cdk-lib/aws-iam";
import { CfnInclude } from "aws-cdk-lib/cloudformation-include";
import type { Construct } from "constructs";
import type { ClusterExecutionContext } from "./cluster-context";
/**
 * Sets up Karpenter IAM roles and policies for dynamic node provisioning
 * Note: CRDs and Helm chart installation moved to Kustomize post-EKS setup
 */
export function setupKarpenter(
  scope: Construct,
  cluster: eks.Cluster,
  context: ClusterExecutionContext
) {
  const karpenterRoles = setupKarpenterRolesAndPolicies(scope, cluster, context);

  addKarpenterNodeRoleToAuthConfigMap(cluster, context);

  // Return roles for potential use in other parts of the stack
  return karpenterRoles;
}

function setupKarpenterRolesAndPolicies(
  scope: Construct,
  cluster: eks.Cluster,
  context: ClusterExecutionContext
): { nodeRole: iam.IRole; serviceAccountRole: iam.Role } {
  const nameGenerator = context.nameGenerator;
  const mainPoliciestemplatePath = path.join(
    __dirname,
    "../../../tools/karpenter/cloudformation.yaml"
  );

  const supplimentaryPoliciestemplatePath = path.join(
    __dirname,
    "../../../tools/karpenter/supplementary-policy-cfn.yaml"
  );

  // CFN bootstrap yaml
  const cfnNodeRoleAndPolicies = new CfnInclude(scope, "KarpenterRolesAndPolicies", {
    templateFile: mainPoliciestemplatePath,
    parameters: {
      ClusterName: nameGenerator.getClusterName(),
    },
  });

  const cfnSupplimentaryPolicies = new CfnInclude(scope, "KarpenterSupplimentaryRolesAndPolicies", {
    templateFile: supplimentaryPoliciestemplatePath,
    parameters: {
      ClusterName: nameGenerator.getClusterName(),
    },
  });

  const oidcProvider = cluster.openIdConnectProvider;

  const oidcProviderArn = oidcProvider.openIdConnectProviderArn;
  const oidcIssuerHostname = oidcProvider.openIdConnectProviderIssuer;

  const nodeRoleResource = cfnNodeRoleAndPolicies.getResource("KarpenterNodeRole");
  const nodeRole = iam.Role.fromRoleArn(
    scope,
    "KarpenterNodeRole",
    cdk.Fn.getAtt(nodeRoleResource.logicalId, "Arn").toString()
  );

  // For managed policies, we need to construct the ARN instead of using getAtt
  // Format: arn:${AWS::Partition}:iam::${AWS::AccountId}:policy/KarpenterControllerPolicy-${ClusterName}
  const controllerPolicyArn = cdk.Fn.join("", [
    "arn:",
    cdk.Fn.ref("AWS::Partition"),
    ":iam::",
    cdk.Fn.ref("AWS::AccountId"),
    ":policy/",
    "KarpenterControllerPolicy-",
    nameGenerator.getClusterName(),
  ]);

  const supplimentaryPolicyArn = cdk.Fn.join("", [
    "arn:",
    cdk.Fn.ref("AWS::Partition"),
    ":iam::",
    cdk.Fn.ref("AWS::AccountId"),
    ":policy/",
    "KarpenterSupplementaryPolicy-v1-",
    nameGenerator.getClusterName(),
  ]);

  const controllerPolicy = iam.ManagedPolicy.fromManagedPolicyArn(
    scope,
    "ImportedKarpenterControllerPolicy",
    controllerPolicyArn
  );

  const supplimentaryPolicy = iam.ManagedPolicy.fromManagedPolicyArn(
    scope,
    "ImportedKarpenterSupplimentaryPolicy",
    supplimentaryPolicyArn
  );

  const oidcCondition = new cdk.CfnJson(scope, "KarpenterOidcCondition", {
    value: {
      [`${oidcIssuerHostname.replace("https://", "")}:sub`]:
        "system:serviceaccount:karpenter:karpenter-sa",
      [`${oidcIssuerHostname.replace("https://", "")}:aud`]: "sts.amazonaws.com",
    },
  });

  const serviceAccountRole = new iam.Role(scope, "KarpenterServiceAccountRole", {
    roleName: nameGenerator.getResourceName("KarpenterServiceAccountRole"),
    assumedBy: new iam.WebIdentityPrincipal(oidcProviderArn, {
      StringEquals: oidcCondition,
    }),
  });
  serviceAccountRole.node.addDependency(cfnNodeRoleAndPolicies);
  serviceAccountRole.node.addDependency(cfnSupplimentaryPolicies);
  serviceAccountRole.addManagedPolicy(controllerPolicy);
  serviceAccountRole.addManagedPolicy(supplimentaryPolicy);

  return {
    nodeRole,
    serviceAccountRole,
  };
}

function addKarpenterNodeRoleToAuthConfigMap(
  cluster: eks.Cluster,
  context: ClusterExecutionContext
): void {
  const nameGenerator = context.nameGenerator;
  const karpenterNodeRoleArn = `arn:aws:iam::${context.accountId}:role/KarpenterNodeRole-${nameGenerator.getClusterName()}`;

  cluster.awsAuth.addRoleMapping(
    iam.Role.fromRoleArn(cluster.stack, "KarpenterNodeRoleImport", karpenterNodeRoleArn),
    {
      username: "system:node:{{EC2PrivateDNSName}}",
      groups: ["system:bootstrappers", "system:nodes"],
    }
  );
}
