import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import type { Construct } from "constructs";
import type { ClusterExecutionContext } from "./cluster-context";
import type { VpcInfo } from "./vpc-helper";

export interface VpcPeeringResult {
  peeringConnection: ec2.CfnVPCPeeringConnection;
  privateRoutes: ec2.CfnRoute[];
  publicRoutes: ec2.CfnRoute[];
}

export function createVpcPeering(
  scope: Construct,
  eksVpc: ec2.Vpc,
  targetVpc: VpcInfo,
  context: ClusterExecutionContext,
  suffix?: string
) {
  const resourceSuffix = suffix ? `-${suffix}` : "";
  const peeringName = targetVpc.vpcName || targetVpc.vpcId;

  // Create VPC peering connection
  const peeringProps: any = {
    vpcId: targetVpc.vpcId,
    peerVpcId: eksVpc.vpcId,
    peerRegion: targetVpc.peerRegion || context.region,
  };

  // Add peer account ID for cross-account peering
  if (targetVpc.peerAccountId) {
    peeringProps.peerOwnerId = targetVpc.peerAccountId;
  }

  const vpcPeering = new ec2.CfnVPCPeeringConnection(
    scope,
    `VPCPeering-${peeringName}-${context.commonPrefix}${resourceSuffix}`,
    peeringProps
  );

  // Log cross-account peering requirements
  if (targetVpc.peerAccountId) {
    console.warn(`⚠️  Cross-account VPC peering created for ${peeringName}`);
    console.warn(`   Requester (EKS): Account ${context.accountId}, VPC ${eksVpc.vpcId}, CIDR ${context.vpcCidrBlock}`);
    console.warn(`   Accepter (Peer): Account ${targetVpc.peerAccountId}, VPC ${targetVpc.vpcId}, CIDR ${targetVpc.cidrBlock}`);
    console.warn(`   Region: ${targetVpc.peerRegion || context.region}`);
    console.warn(`   📋 Peering Connection ID: ${vpcPeering.ref}`);
    console.warn(``);
    console.warn(`   🔄 MANUAL ACTIONS REQUIRED IN PEER ACCOUNT (${targetVpc.peerAccountId}):`);
    console.warn(``);
    console.warn(`   🚀 OPTION 1: Full automated setup (recommended):`);
    console.warn(`      ./scripts/manage-vpc-peering.sh full-setup ${vpcPeering.ref} --region ${targetVpc.peerRegion || context.region}`);
    console.warn(``);
    console.warn(`   🔧 OPTION 2: Manual step-by-step:`);
    console.warn(`   1️⃣ Accept the peering connection:`);
    console.warn(`      ./scripts/manage-vpc-peering.sh accept ${vpcPeering.ref} --region ${targetVpc.peerRegion || context.region}`);
    console.warn(`   2️⃣ Add routes to enable return traffic:`);
    console.warn(`      ./scripts/manage-vpc-peering.sh add-routes ${targetVpc.vpcId} ${context.vpcCidrBlock} ${vpcPeering.ref} --region ${targetVpc.peerRegion || context.region}`);
    console.warn(``);
    console.warn(`   💡 Use './scripts/manage-vpc-peering.sh --help' for more options`);
  }

  // Add routes for target VPC access in private subnets
  const privateRoutes: ec2.CfnRoute[] = [];
  eksVpc.privateSubnets.forEach((subnet, index) => {
    const route = new ec2.CfnRoute(
      scope,
      `PeeringRoutePrivate-${peeringName}-${index}-${context.commonPrefix}${resourceSuffix}`,
      {
        routeTableId: subnet.routeTable.routeTableId,
        destinationCidrBlock: targetVpc.cidrBlock,
        vpcPeeringConnectionId: vpcPeering.ref,
      }
    );
    privateRoutes.push(route);
  });

  // Add routes for target VPC access in public subnets
  const publicRoutes: ec2.CfnRoute[] = [];
  eksVpc.publicSubnets.forEach((subnet, index) => {
    const route = new ec2.CfnRoute(
      scope,
      `PeeringRoutePublic-${peeringName}-${index}-${context.commonPrefix}${resourceSuffix}`,
      {
        routeTableId: subnet.routeTable.routeTableId,
        destinationCidrBlock: targetVpc.cidrBlock,
        vpcPeeringConnectionId: vpcPeering.ref,
      }
    );
    publicRoutes.push(route);
  });

  // Add Name tag to VPC peering connection
  const peeringConnectionName = `peering-${peeringName}-to-${context.nameGenerator.getVpcName()}`;
  cdk.Tags.of(vpcPeering).add("Name", peeringConnectionName);

  // Add description tag if provided
  if (targetVpc.description) {
    cdk.Tags.of(vpcPeering).add("Description", targetVpc.description);
  }

  // Add standard tags
  for (const [tagName, tagValue] of Object.entries(context.getStandardizedTags())) {
    cdk.Tags.of(vpcPeering).add(tagName, tagValue);
  }

  return {
    peeringConnection: vpcPeering,
    privateRoutes,
    publicRoutes,
  };
}

export function createMultipleVpcPeerings(
  scope: Construct,
  eksVpc: ec2.Vpc,
  peeringVpcs: VpcInfo[],
  context: ClusterExecutionContext
) {
  const peeringConnections = new Map<string, VpcPeeringResult>();
  let enabledConnections = 0;

  peeringVpcs.forEach((peeringVpc, index) => {
    // Skip disabled peering connections
    if (peeringVpc.enabled === false) {
      console.log(`Skipping disabled VPC peering for ${peeringVpc.vpcName || peeringVpc.vpcId}`);
      return;
    }

    const peeringKey = peeringVpc.vpcName || peeringVpc.vpcId;
    const result = createVpcPeering(scope, eksVpc, peeringVpc, context, index.toString());

    peeringConnections.set(peeringKey, result);
    enabledConnections++;

    console.log(`Created VPC peering connection for ${peeringKey} (${peeringVpc.cidrBlock})`);
  });

  return {
    peeringConnections,
    totalConnections: enabledConnections,
  };
}

export function createVpnVpcPeering(
  scope: Construct,
  eksVpc: ec2.Vpc,
  vpnVpc: { vpcId: string; vpcName?: string; cidrBlock: string },
  context: ClusterExecutionContext
) {
  const vpnVpcInfo: VpcInfo = {
    vpcId: vpnVpc.vpcId,
    vpcName: vpnVpc.vpcName,
    cidrBlock: vpnVpc.cidrBlock,
    description: "VPN VPC connection",
    enabled: true,
  };

  return createVpcPeering(scope, eksVpc, vpnVpcInfo, context, "vpn");
}

export function validateVpcPeeringConfigurations(
  peeringVpcs: VpcInfo[],
  eksVpcCidr: string
) {
  const enabledVpcs = peeringVpcs.filter(vpc => vpc.enabled !== false);

  if (enabledVpcs.length === 0) {
    console.warn("No VPC peering connections are enabled");
    return;
  }

  // Count cross-account peering connections
  const crossAccountVpcs = enabledVpcs.filter(vpc => vpc.peerAccountId);
  if (crossAccountVpcs.length > 0) {
    console.warn(`⚠️  Found ${crossAccountVpcs.length} cross-account VPC peering connection(s)`);
    console.warn("   These will require manual acceptance from the peer account(s)");
  }

  // Check for duplicate VPC IDs
  const vpcIds = enabledVpcs.map(vpc => vpc.vpcId);
  const duplicateIds = vpcIds.filter((id, index) => vpcIds.indexOf(id) !== index);
  if (duplicateIds.length > 0) {
    throw new Error(`Duplicate VPC IDs found: ${duplicateIds.join(", ")}`);
  }

  // Check for CIDR block overlaps
  const cidrBlocks = enabledVpcs.map(vpc => vpc.cidrBlock);
  cidrBlocks.push(eksVpcCidr); // Include EKS VPC CIDR in overlap check

  for (let i = 0; i < cidrBlocks.length; i++) {
    for (let j = i + 1; j < cidrBlocks.length; j++) {
      if (cidrBlocksOverlap(cidrBlocks[i]!, cidrBlocks[j]!)) {
        throw new Error(
          `CIDR block overlap detected between ${cidrBlocks[i]} and ${cidrBlocks[j]}`
        );
      }
    }
  }

  console.log(`Validated ${enabledVpcs.length} VPC peering configurations`);
}

function cidrBlocksOverlap(cidr1: string, cidr2: string) {
  // Simple CIDR overlap check - in production, you might want a more robust implementation
  const [network1, prefix1] = cidr1.split('/');
  const [network2, prefix2] = cidr2.split('/');

  // Convert IP addresses to numbers for comparison
  const ip1 = ipToNumber(network1!);
  const ip2 = ipToNumber(network2!);

  const mask1 = (0xffffffff << (32 - parseInt(prefix1!))) >>> 0;
  const mask2 = (0xffffffff << (32 - parseInt(prefix2!))) >>> 0;

  const network1Start = (ip1 & mask1) >>> 0;
  const network1End = (network1Start | (~mask1)) >>> 0;
  const network2Start = (ip2 & mask2) >>> 0;
  const network2End = (network2Start | (~mask2)) >>> 0;

  return !(network1End < network2Start || network2End < network1Start);
}

function ipToNumber(ip: string) {
  return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
}
