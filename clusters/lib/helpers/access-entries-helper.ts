import * as eks from "aws-cdk-lib/aws-eks";
import type { Construct } from "constructs";
import type { ClusterExecutionContext } from "./cluster-context";
import {
  ADMIN_ROLE_ARN_NAME,
  DEVELOPERS_ROLE_ARN_NAME,
  DEVELOPERS_TEMP_ROLE_ARN_NAME,
} from "./constants";

export function addAccessEntries(
  scope: Construct,
  cluster: eks.Cluster,
  context: ClusterExecutionContext
) {
  const buildSsoRoleArn = (roleName: string): string => {
    return `arn:aws:iam::${context.accountId}:role/aws-reserved/sso.amazonaws.com/${context.region}/${roleName}`;
  };

  const createAccessEntry = (id: string, roleName: string, policyName: string): eks.AccessEntry => {
    return new eks.AccessEntry(scope, id, {
      accessPolicies: [
        eks.AccessPolicy.fromAccessPolicyName(policyName, {
          accessScopeType: eks.AccessScopeType.CLUSTER,
        }),
      ],
      cluster: cluster,
      principal: buildSsoRoleArn(roleName),
      accessEntryName: id,
      accessEntryType: eks.AccessEntryType.STANDARD,
    });
  };

  createAccessEntry("access-entry-ps-developers", DEVELOPERS_ROLE_ARN_NAME, "AmazonEKSViewPolicy");
  createAccessEntry(
    "access-entry-ps-developers-temp",
    DEVELOPERS_TEMP_ROLE_ARN_NAME,
    "AmazonEKSClusterAdminPolicy"
  );
  createAccessEntry("access-entry-ps-admin", ADMIN_ROLE_ARN_NAME, "AmazonEKSClusterAdminPolicy");
}
