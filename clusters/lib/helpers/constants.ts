export const VPN_VPC_NAME = "tb-prod-cs-aps1-vpc";
export const AWS_MUMBAI_REGION = "ap-south-1";
export const AWS_SINGAPORE_REGION = "ap-southeast-1";
export const AWS_ROOT_ACCOUNT_ID = "************";
export const AWS_SUPERHERO_STAGING_ACCOUNT_ID = "************";
export const AWS_SUPERHERO_PRODUCTION_ACCOUNT_ID = "************";

export const DEVELOPERS_ROLE_ARN_NAME = "AWSReservedSSO_Developers_48f7b5e49632a137";
export const DEVELOPERS_TEMP_ROLE_ARN_NAME =
  "AWSReservedSSO_Dev_Extra_Temp_Access_b5cd8d00169919dc";
export const ADMIN_ROLE_ARN_NAME = "AWSReservedSSO_AdministratorAccess_e1c4ad1abe4b793c";

export const TOOLS_COMMON_VPC_NAME = "vpc-treebo-tools-common";
export const STAGING_VPC_NAME = "vpc-treebo-staging";
export const PRODUCTION_VPC_NAME = "vpc-treebo-production";

export const OLD_DIRECT_STAGING_VPC_NAME = "treebo-staging-vpc";
export const OLD_DIRECT_PRODUCTION_VPC_NAME = "treebo-prod-vpc";


export const SUPERHERO_STAGING_VPC_INFO = {
  vpcId: "vpc-00963d16062dd7e92",
  vpcName: "s-7587-apse1-11-vpc",
  cidrBlock: "**********/16",
  description: "Facets staging VPC",
  enabled: true,
  peerAccountId: AWS_SUPERHERO_STAGING_ACCOUNT_ID,
  peerRegion: AWS_SINGAPORE_REGION,
};

export const SUPERHERO_PRODUCTION_VPC_INFO = {
  vpcId: "vpc-0c5b3689dca38eba4",
  vpcName: "p-2621-aps1-01-vpc",
  cidrBlock: "********/16",
  description: "Facets production VPC",
  enabled: true,
  peerAccountId: AWS_SUPERHERO_PRODUCTION_ACCOUNT_ID,
  peerRegion: AWS_MUMBAI_REGION,
};
