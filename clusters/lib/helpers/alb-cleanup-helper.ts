import path from "node:path";
import * as cdk from "aws-cdk-lib";
import * as iam from "aws-cdk-lib/aws-iam";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as logs from "aws-cdk-lib/aws-logs";
import * as cr from "aws-cdk-lib/custom-resources";
import type { Construct } from "constructs";
import type { ClusterExecutionContext } from "./cluster-context";

// ALBs are created via a plugin and not from CDK itself
// Which causes CDK deletion to block on ALBs
// This script adds a callback to delete ALBs before CDK deletion

export function addALBCleanupFinalizer(scope: Construct, context: ClusterExecutionContext): void {
  const nameGenerator = context.nameGenerator;
  // Step 1: Create the Lambda function
  const albCleanupFunction = new lambda.Function(scope, "ALBCleanupFunction", {
    runtime: lambda.Runtime.PYTHON_3_9,
    handler: "index.handler",
    code: lambda.Code.fromAsset(path.join(__dirname, "./alb-cleanup-lambda")),
    timeout: cdk.Duration.minutes(10),
    functionName: nameGenerator.getResourceName("alb-cleanup-lambda"),
    description: `ALB cleanup function for cluster ${nameGenerator.getClusterName()}`,
    environment: {
      CLUSTER_NAME: nameGenerator.getClusterName(),
    },
    retryAttempts: 0,
  });

  // Step 2: Add IAM permissions
  albCleanupFunction.addToRolePolicy(
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        "elasticloadbalancing:DescribeLoadBalancers",
        "elasticloadbalancing:DescribeTags",
        "elasticloadbalancing:DeleteLoadBalancer",
      ],
      resources: ["*"],
      sid: "ALBCleanupPermissions",
    })
  );

  albCleanupFunction.addToRolePolicy(
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"],
      resources: [`arn:aws:logs:*:*:log-group:/aws/lambda/${albCleanupFunction.functionName}*`],
      sid: "CloudWatchLogsPermissions",
    })
  );

  // Step 3: Create outputs for the function
  new cdk.CfnOutput(scope, "ALBCleanupFunctionArn", {
    value: albCleanupFunction.functionArn,
    description: "ARN of the ALB cleanup Lambda function",
    exportName: nameGenerator.getResourceName("alb-cleanup-function-arn"),
  });

  // Step 4: Create the provider with minimal configuration
  const albCleanupProvider = new cr.Provider(scope, "ALBCleanupProvider", {
    onEventHandler: albCleanupFunction,
    logRetention: logs.RetentionDays.ONE_WEEK,
    providerFunctionName: nameGenerator.getResourceName("alb-cleanup-provider"),
  });

  // Step 5: Create the custom resource with minimal dependencies
  const albCleanupResource = new cdk.CustomResource(scope, "ALBCleanupResource", {
    serviceToken: albCleanupProvider.serviceToken,
    properties: {
      ClusterName: nameGenerator.getClusterName(),
      // Add a random value to ensure the resource is updated on each deployment
      Timestamp: new Date().getTime(),
    },
  });

  // Step 6: Add an output for the configuration status
  new cdk.CfnOutput(scope, "ALBCleanupInfo", {
    value: `ALB cleanup finalizer configured for cluster ${nameGenerator.getClusterName()}`,
    description: "ALB cleanup configuration status",
  });
}
