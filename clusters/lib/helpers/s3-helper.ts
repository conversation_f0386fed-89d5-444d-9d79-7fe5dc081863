import {
  type BucketLifecycleConfiguration,
  CreateBucketCommand,
  HeadBucketCommand,
  PutBucketEncryptionCommand,
  PutBucketLifecycleConfigurationCommand,
  PutObjectCommand,
  PutPublicAccessBlockCommand,
  type S3Client,
} from "@aws-sdk/client-s3";
import type { TreeboAWSPolicyDocument } from "./roles-helper";

export const defaultLifecycleConfiguration: BucketLifecycleConfiguration = {
  Rules: [
    {
      ID: "TransitionToGlacierAndExpire",
      Prefix: "",
      Status: "Enabled",
      Transitions: [
        {
          Days: 90,
          StorageClass: "GLACIER",
        },
      ],
      Expiration: {
        Days: 365,
      },
      NoncurrentVersionTransitions: [
        {
          NoncurrentDays: 90,
          StorageClass: "GLACIER",
        },
      ],
      NoncurrentVersionExpiration: {
        NoncurrentDays: 365,
      },
    },
  ],
};

export const veleroLifecycleConfiguration: BucketLifecycleConfiguration = {
  Rules: [
    {
      ID: "VeleroBackupLifecycle",
      Prefix: "",
      Status: "Enabled",
      Transitions: [
        {
          Days: 30,
          StorageClass: "STANDARD_IA",
        },
        {
          Days: 90,
          StorageClass: "GLACIER",
        },
        {
          Days: 180,
          StorageClass: "DEEP_ARCHIVE",
        },
      ],
      Expiration: {
        Days: 2555, // 7 years for compliance
      },
      NoncurrentVersionTransitions: [
        {
          NoncurrentDays: 30,
          StorageClass: "STANDARD_IA",
        },
        {
          NoncurrentDays: 90,
          StorageClass: "GLACIER",
        },
      ],
      NoncurrentVersionExpiration: {
        NoncurrentDays: 365,
      },
    },
  ],
};

export async function bucketExists(s3Client: S3Client, bucketName: string): Promise<boolean> {
  try {
    await s3Client.send(new HeadBucketCommand({ Bucket: bucketName }));
    return true;
  } catch (error: any) {
    if (error.name === "NotFound" || error.name === "NoSuchBucket") {
      return false;
    }
    // For other errors (like access denied), we'll treat as "exists but not accessible"
    // This prevents trying to create a bucket we can't access
    if (error.name === "Forbidden" || error.name === "AccessDenied") {
      return true;
    }
    throw error;
  }
}

export interface S3BucketCreationResult {
  bucketName: string;
  created: boolean;
  skipped: boolean;
  reason?: string;
}

export async function createS3Bucket(s3Client: S3Client, bucketName: string): Promise<void> {
  try {
    const exists = await bucketExists(s3Client, bucketName);

    if (exists) {
      console.warn(`⚠️  S3 bucket ${bucketName} already exists. Skipping creation.`);
      return;
    }

    await s3Client.send(
      new CreateBucketCommand({
        Bucket: bucketName,
        // CreateBucketConfiguration: {
        //   LocationConstraint: s3Client.config.region as BucketLocationConstraint,
        // },
      })
    );

    console.log(`S3 bucket ${bucketName} created successfully`);
  } catch (error) {
    console.error(`Error creating S3 bucket ${bucketName}:`, error);
    throw error;
  }
}

export async function updateBucketEncryption(
  s3Client: S3Client,
  bucketName: string
): Promise<void> {
  try {
    await s3Client.send(
      new PutBucketEncryptionCommand({
        Bucket: bucketName,
        ServerSideEncryptionConfiguration: {
          Rules: [
            {
              ApplyServerSideEncryptionByDefault: {
                SSEAlgorithm: "AES256",
              },
            },
          ],
        },
      })
    );

    console.log(`Encryption enabled for bucket ${bucketName}`);
  } catch (error) {
    console.error(`Error enabling encryption for S3 bucket ${bucketName}:`, error);
    throw error;
  }
}

export async function updateBucketACL(s3Client: S3Client, bucketName: string): Promise<void> {
  try {
    await s3Client.send(
      new PutPublicAccessBlockCommand({
        Bucket: bucketName,
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          IgnorePublicAcls: true,
          BlockPublicPolicy: true,
          RestrictPublicBuckets: true,
        },
      })
    );

    console.log(`Public access blocked for bucket ${bucketName}`);
  } catch (error) {
    console.error(`Error blocking public access for S3 bucket ${bucketName}:`, error);
    throw error;
  }
}

export async function updateBucketLifecycleConfiguration(
  s3Client: S3Client,
  bucketName: string,
  lifecycleConfiguration: BucketLifecycleConfiguration
): Promise<void> {
  try {
    await s3Client.send(
      new PutBucketLifecycleConfigurationCommand({
        Bucket: bucketName,
        LifecycleConfiguration: lifecycleConfiguration,
      })
    );

    console.log(`Lifecycle configuration set for bucket ${bucketName}`);
  } catch (error) {
    console.error(`Error setting lifecycle configuration for S3 bucket ${bucketName}:`, error);
    throw error;
  }
}

export async function createS3BucketDirectory(
  s3Client: S3Client,
  bucketName: string,
  directoryName: string
): Promise<void> {
  try {
    await s3Client.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: `${directoryName}/`,
        Body: "",
      })
    );

    console.log(`Directory '${directoryName}' created in bucket ${bucketName}`);
  } catch (error) {
    console.error(`Error creating directory '${directoryName}' in S3 bucket ${bucketName}:`, error);
    throw error;
  }
}

export const createS3PolicyDocument = (
  bucketName: string,
  commonPrefix?: string,
  actions: string[] = [
    "s3:GetObject",
    "s3:ListBucket",
    "s3:GetBucketLocation",
    "s3:PutObject",
    "s3:DeleteObject",
    "s3:GetObjectVersion",
    "s3:DeleteObjectVersion",
  ]
): TreeboAWSPolicyDocument => {
  const resourcePolicyString = commonPrefix
    ? `arn:aws:s3:::${bucketName}/${commonPrefix}/*`
    : `arn:aws:s3:::${bucketName}/*`;

  return {
    Version: "2012-10-17",
    Statement: [
      {
        Effect: "Allow",
        Action: actions,
        Resource: [`arn:aws:s3:::${bucketName}`, resourcePolicyString],
      },
    ],
  };
};
