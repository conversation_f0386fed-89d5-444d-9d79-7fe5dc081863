import type * as cdk from "aws-cdk-lib";
import { z } from "zod";
import { AWS_MUMBAI_REGION, AWS_ROOT_ACCOUNT_ID } from "./constants";
import { type NameGenerator, createNameGenerator } from "./name-generator";
import { VpcInfoSchema } from "./vpc-helper";

export const ClusterExecutionContextSchema = z.object({
  commonPrefix: z
    .string()
    .min(1, "Common prefix is required")
    .regex(
      /^[a-z0-9-]+$/,
      "Common prefix must contain only lowercase letters, numbers, and hyphens"
    ),
  region: z
    .string()
    .min(1, "Region is required")
    .regex(/^[a-z0-9-]+$/, "Invalid region format"),
  accountId: z.string().regex(/^\d{12}$/, "Account ID must be a 12-digit number"),
  environment: z
    .string()
    .min(1, "Environment is required")
    .regex(/^[a-z0-9-]+$/, "Environment must contain only lowercase letters, numbers, and hyphens"),
  tags: z.record(z.string(), z.string()).optional(),
  vpcCidrBlock: z
    .string()
    .regex(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\d{1,2}$/, "Invalid CIDR format"),
  // Support both single VPN VPC (backward compatibility) and multiple VPN VPCs
  vpnVpc: VpcInfoSchema.optional(),
  peeringVpcs: z.array(VpcInfoSchema).optional(),
  // Removed launchTemplate - no longer needed with Karpenter
}).refine(
  (data) => {
    // At least one of vpnVpc or peeringVpcs must be provided
    return data.vpnVpc || (data.peeringVpcs && data.peeringVpcs.length > 0);
  },
  {
    message: "Either vpnVpc or peeringVpcs (with at least one VPC) must be provided",
    path: ["vpnVpc", "peeringVpcs"],
  }
);

export type ClusterExecutionContext = z.infer<typeof ClusterExecutionContextSchema> & {
  nameGenerator: NameGenerator;
  getStandardizedTags(): Record<string, string>;
};
export const DEFAULT_CLUSTER_CONTEXT: Partial<ClusterExecutionContext> = {
  region: AWS_MUMBAI_REGION,
  accountId: AWS_ROOT_ACCOUNT_ID,
  environment: "production",
  tags: {
    Project: "treebo-devops",
    ManagedBy: "cdk",
  },
};

export function createClusterContext(
  context: Partial<ClusterExecutionContext>
): ClusterExecutionContext {
  const mergedContext = { ...DEFAULT_CLUSTER_CONTEXT, ...context };

  try {
    const validatedContext = ClusterExecutionContextSchema.parse(mergedContext);
    const nameGenerator = createNameGenerator(validatedContext as ClusterExecutionContext);
    return {
      ...validatedContext,
      nameGenerator,
      getStandardizedTags: function (): Record<string, string> {
        return {
          "karpenter.sh/discovery": this.nameGenerator.getClusterName(),
          treebo_env: this.environment,
          "held-by": `k8s-cluster-${this.commonPrefix}`,
          environment: this.environment,
          project: "treebo-devops",
          team: "devops",
          managedBy: "cdk",
          costCenter: "devops",
          ...this.tags,
        };
      },
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path.join(".")}: ${err.message}`);
      throw new Error(`Invalid cluster context: ${errorMessages.join(", ")}`);
    }
    throw error;
  }
}

export function createStackPropsFromContext(context: ClusterExecutionContext): cdk.StackProps {
  return {
    env: {
      account: context.accountId,
      region: context.region,
    },
    tags: context.getStandardizedTags(),
    description: `EKS cluster stack for ${context.commonPrefix}`,
  };
}
