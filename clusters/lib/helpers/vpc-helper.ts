import { z } from "zod";
import { EC2Client, DescribeVpcsCommand } from '@aws-sdk/client-ec2';

export const VpcInfoSchema = z.object({
  vpcId: z.string().regex(/^vpc-[a-z0-9]+$/, "Invalid VPC ID format"),
  vpcName: z.string().optional(),
  cidrBlock: z.string().regex(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\d{1,2}$/, "Invalid CIDR format"),
  description: z.string().optional(),
  enabled: z.boolean().optional().default(true),
  peerAccountId: z.string().regex(/^\d{12}$/, "Account ID must be a 12-digit number").optional(),
  peerRegion: z.string().regex(/^[a-z0-9-]+$/, "Invalid region format").optional(),
});

export type VpcInfo = z.infer<typeof VpcInfoSchema>;

/**
 * Creates a static VPC configuration for cross-account peering
 * Use this instead of fetch functions for VPCs in other accounts
 */
export function createCrossAccountVpcInfo(
  vpcId: string,
  cidrBlock: string,
  peerAccountId: string,
  options: {
    vpcName?: string;
    description?: string;
    enabled?: boolean;
    peerRegion?: string;
  } = {}
): VpcInfo {
  return {
    vpcId,
    cidrBlock,
    peerAccountId,
    vpcName: options.vpcName,
    description: options.description,
    enabled: options.enabled ?? true,
    peerRegion: options.peerRegion,
  };
}

export async function fetchVpcAttributesById(vpcId: string, region: string): Promise<VpcInfo> {
  const ec2Client = new EC2Client({ region });

  // Note: This function only works for VPCs in the current account
  // For cross-account VPCs, use static configuration instead
  try {
    const response = await ec2Client.send(new DescribeVpcsCommand({
      VpcIds: [vpcId],
    }));

    const vpc = response.Vpcs?.[0];
    if (!vpc) {
      throw new Error(`VPC ${vpcId} not found`);
    }

    const vpcName = vpc.Tags?.find(tag => tag.Key === 'Name')?.Value;

    return {
      vpcId: vpc.VpcId!,
      vpcName,
      cidrBlock: vpc.CidrBlock!,
      enabled: true,
    };
  } catch (error) {
    throw new Error(`Failed to fetch VPC attributes for ${vpcId}: ${error}`);
  }
}

export async function fetchVpcAttributesByName(vpcName: string, region: string): Promise<VpcInfo> {
  const ec2Client = new EC2Client({ region });

  // Note: This function only works for VPCs in the current account
  // For cross-account VPCs, use static configuration instead
  try {
    const response = await ec2Client.send(new DescribeVpcsCommand({
      Filters: [
        {
          Name: 'tag:Name',
          Values: [vpcName],
        },
      ],
    }));

    const vpc = response.Vpcs?.[0];
    if (!vpc) {
      throw new Error(`VPC with name '${vpcName}' not found`);
    }

    if (response.Vpcs!.length > 1) {
      throw new Error(`Multiple VPCs found with name '${vpcName}'. Please use VPC ID instead.`);
    }

    return {
      vpcId: vpc.VpcId!,
      vpcName: vpcName,
      cidrBlock: vpc.CidrBlock!,
      enabled: true,
    };
  } catch (error) {
    throw new Error(`Failed to fetch VPC attributes for name '${vpcName}': ${error}`);
  }
}

/**
 * Fetches VPC attributes for multiple VPCs by their IDs
 * @param vpcIds Array of VPC IDs to fetch
 * @param region AWS region
 * @returns Array of VpnVpcInfo objects
 */
export async function fetchMultipleVpcAttributesById(vpcIds: string[], region: string): Promise<VpcInfo[]> {
  if (vpcIds.length === 0) {
    return [];
  }

  const ec2Client = new EC2Client({ region });

  try {
    const response = await ec2Client.send(new DescribeVpcsCommand({
      VpcIds: vpcIds,
    }));

    if (!response.Vpcs || response.Vpcs.length === 0) {
      throw new Error(`No VPCs found for IDs: ${vpcIds.join(', ')}`);
    }

    return response.Vpcs.map(vpc => {
      const vpcName = vpc.Tags?.find(tag => tag.Key === 'Name')?.Value;
      const description = vpc.Tags?.find(tag => tag.Key === 'Description')?.Value;

      return {
        vpcId: vpc.VpcId!,
        vpcName,
        cidrBlock: vpc.CidrBlock!,
        description,
        enabled: true, // Default to enabled
      };
    });
  } catch (error) {
    throw new Error(`Failed to fetch VPC attributes for IDs ${vpcIds.join(', ')}: ${error}`);
  }
}

/**
 * Fetches VPC attributes for multiple VPCs by their names
 * @param vpcNames Array of VPC names to fetch
 * @param region AWS region
 * @returns Array of VpnVpcInfo objects
 */
export async function fetchMultipleVpcAttributesByName(vpcNames: string[], region: string): Promise<VpcInfo[]> {
  if (vpcNames.length === 0) {
    return [];
  }

  const ec2Client = new EC2Client({ region });

  try {
    const response = await ec2Client.send(new DescribeVpcsCommand({
      Filters: [
        {
          Name: 'tag:Name',
          Values: vpcNames,
        },
      ],
    }));

    if (!response.Vpcs || response.Vpcs.length === 0) {
      throw new Error(`No VPCs found for names: ${vpcNames.join(', ')}`);
    }

    // Check if all requested VPCs were found
    const foundNames = response.Vpcs.map(vpc =>
      vpc.Tags?.find(tag => tag.Key === 'Name')?.Value
    ).filter(Boolean);

    const missingNames = vpcNames.filter(name => !foundNames.includes(name));
    if (missingNames.length > 0) {
      throw new Error(`VPCs not found for names: ${missingNames.join(', ')}`);
    }

    return response.Vpcs.map(vpc => {
      const vpcName = vpc.Tags?.find(tag => tag.Key === 'Name')?.Value!;
      const description = vpc.Tags?.find(tag => tag.Key === 'Description')?.Value;

      return {
        vpcId: vpc.VpcId!,
        vpcName,
        cidrBlock: vpc.CidrBlock!,
        description,
        enabled: true, // Default to enabled
      };
    });
  } catch (error) {
    throw new Error(`Failed to fetch VPC attributes for names ${vpcNames.join(', ')}: ${error}`);
  }
}

/**
 * Validates VPC configuration and fetches missing attributes
 * @param vpnVpcs Array of VPN VPC configurations (may have missing attributes)
 * @param region AWS region
 * @returns Array of complete VpnVpcInfo objects
 */
export async function validateAndFetchVpcAttributes(
  vpnVpcs: Partial<VpcInfo>[],
  region: string
): Promise<VpcInfo[]> {
  const results: VpcInfo[] = [];
  const vpcIdsToFetch: string[] = [];
  const vpcNamesToFetch: string[] = [];

  // Separate VPCs that need attribute fetching
  for (const vpc of vpnVpcs) {
    if (vpc.vpcId && vpc.cidrBlock) {
      // VPC has all required attributes
      results.push({
        vpcId: vpc.vpcId,
        vpcName: vpc.vpcName,
        cidrBlock: vpc.cidrBlock,
        description: vpc.description,
        enabled: vpc.enabled ?? true,
      });
    } else if (vpc.vpcId) {
      // Has VPC ID but missing other attributes
      vpcIdsToFetch.push(vpc.vpcId);
    } else if (vpc.vpcName) {
      // Has VPC name but missing other attributes
      vpcNamesToFetch.push(vpc.vpcName);
    } else {
      throw new Error('VPC configuration must have either vpcId or vpcName');
    }
  }

  // Fetch missing attributes
  if (vpcIdsToFetch.length > 0) {
    const fetchedByIds = await fetchMultipleVpcAttributesById(vpcIdsToFetch, region);
    results.push(...fetchedByIds);
  }

  if (vpcNamesToFetch.length > 0) {
    const fetchedByNames = await fetchMultipleVpcAttributesByName(vpcNamesToFetch, region);
    results.push(...fetchedByNames);
  }

  return results;
}
