import * as crypto from "node:crypto";
import * as sdkec2 from "@aws-sdk/client-ec2";
import * as cdk from "aws-cdk-lib";
import * as cdkec2 from "aws-cdk-lib/aws-ec2";
import * as cr from "aws-cdk-lib/custom-resources";
import type { Construct } from "constructs";
import { z } from "zod";

export interface LaunchTemplateAttributes {
  launchTemplateName: string;
  sdkAttributes: sdkec2.RequestLaunchTemplateData;
  cdkAttributes: cdkec2.LaunchTemplateProps;
}

export const LaunchTemplateConfigSchema = z.object({
  id: z.string().min(1, "Launch template ID is required"),
  name: z.string(),
  version: z.string().optional(),
});

export type LaunchTemplateConfig = z.infer<typeof LaunchTemplateConfigSchema>;

// **IMPORTANT NOTE**:
// Its better to create launch template from SDK and pass around the info
// CDK has some issues around stack and launch template getting updated in same operation
// ref: https://github.com/aws/aws-cdk/issues/16644#issuecomment-1653591171

// const ec2Client = new sdkec2.EC2Client({});

/**
 * Creates a stable hash for launch template configuration to detect changes
 */
export function createLaunchTemplateConfigHash(data: sdkec2.RequestLaunchTemplateData): string {
  // Extract and normalize the key configuration elements
  const normalizedConfig = {
    // Block device mappings
    blockDevices: data.BlockDeviceMappings?.map(bd => ({
      deviceName: bd.DeviceName,
      ebs: bd.Ebs
        ? {
            volumeSize: bd.Ebs.VolumeSize,
            volumeType: bd.Ebs.VolumeType,
            encrypted: bd.Ebs.Encrypted,
            deleteOnTermination: bd.Ebs.DeleteOnTermination,
            iops: bd.Ebs.Iops,
            throughput: bd.Ebs.Throughput,
          }
        : undefined,
      virtualName: bd.VirtualName,
      noDevice: bd.NoDevice,
    })).sort((a, b) => (a.deviceName || "").localeCompare(b.deviceName || "")),

    // Instance metadata options
    metadataOptions: data.MetadataOptions
      ? {
          httpTokens: data.MetadataOptions.HttpTokens,
          httpEndpoint: data.MetadataOptions.HttpEndpoint,
          httpPutResponseHopLimit: data.MetadataOptions.HttpPutResponseHopLimit,
          httpProtocolIpv6: data.MetadataOptions.HttpProtocolIpv6,
          instanceMetadataTags: data.MetadataOptions.InstanceMetadataTags,
        }
      : undefined,

    // Network interfaces
    networkInterfaces: data.NetworkInterfaces?.map(ni => ({
      associateCarrierIpAddress: ni.AssociateCarrierIpAddress,
      associatePublicIpAddress: ni.AssociatePublicIpAddress,
      deleteOnTermination: ni.DeleteOnTermination,
      description: ni.Description,
      deviceIndex: ni.DeviceIndex,
      groups: ni.Groups?.sort(),
      interfaceType: ni.InterfaceType,
      ipv6AddressCount: ni.Ipv6AddressCount,
      subnetId: ni.SubnetId,
    })).sort((a, b) => (a.deviceIndex || 0) - (b.deviceIndex || 0)),

    // Security group IDs (sorted for consistency)
    securityGroupIds: data.SecurityGroupIds?.sort(),
    securityGroups: data.SecurityGroups?.sort(),

    // Instance configuration
    imageId: data.ImageId,
    instanceType: data.InstanceType,
    keyName: data.KeyName,
    userData: data.UserData,

    // IAM instance profile
    iamInstanceProfile: data.IamInstanceProfile
      ? {
          arn: data.IamInstanceProfile.Arn,
          name: data.IamInstanceProfile.Name,
        }
      : undefined,

    // Monitoring
    monitoring: data.Monitoring
      ? {
          enabled: data.Monitoring.Enabled,
        }
      : undefined,

    // Placement
    placement: data.Placement
      ? {
          availabilityZone: data.Placement.AvailabilityZone,
          affinity: data.Placement.Affinity,
          groupName: data.Placement.GroupName,
          hostId: data.Placement.HostId,
          tenancy: data.Placement.Tenancy,
          spreadDomain: data.Placement.SpreadDomain,
          hostResourceGroupArn: data.Placement.HostResourceGroupArn,
          partitionNumber: data.Placement.PartitionNumber,
        }
      : undefined,

    // Tags (sorted by key for consistency)
    tagSpecifications: data.TagSpecifications?.map(ts => ({
      resourceType: ts.ResourceType,
      tags: ts.Tags?.map(tag => ({
        key: tag.Key,
        value: tag.Value,
      })).sort((a, b) => (a.key || "").localeCompare(b.key || "")),
    })).sort((a, b) => (a.resourceType || "").localeCompare(b.resourceType || "")),

    // Other important fields
    creditSpecification: data.CreditSpecification,
    disableApiStop: data.DisableApiStop,
    disableApiTermination: data.DisableApiTermination,
    ebsOptimized: data.EbsOptimized,
    elasticGpuSpecifications: data.ElasticGpuSpecifications,
    elasticInferenceAccelerators: data.ElasticInferenceAccelerators,
    enclaveOptions: data.EnclaveOptions,
    hibernationOptions: data.HibernationOptions,
    instanceInitiatedShutdownBehavior: data.InstanceInitiatedShutdownBehavior,
    instanceMarketOptions: data.InstanceMarketOptions,
    licenseSpecifications: data.LicenseSpecifications,
    maintenanceOptions: data.MaintenanceOptions,
    privateDnsNameOptions: data.PrivateDnsNameOptions,
    ramDiskId: data.RamDiskId,
  };

  // Create a stable string representation
  const configString = JSON.stringify(normalizedConfig, null, 0);

  // Create hash
  const hash = crypto.createHash("sha256").update(configString).digest("hex");

  // Return first 12 characters for readability
  return hash.substring(0, 12);
}

export function getLaunchTemplateSDKAndCDKAttributes(
  commonPrefix: string,
  commonTags?: Record<string, string>,
  extraCDKAttributes?: Partial<cdkec2.LaunchTemplateProps>
): LaunchTemplateAttributes {
  const launchTemplateName = `launch-template-${commonPrefix}`;

  const sdkAttributes: sdkec2.RequestLaunchTemplateData = {
    BlockDeviceMappings: [
      {
        DeviceName: "/dev/xvda",
        Ebs: {
          VolumeSize: 20,
          VolumeType: "gp3",
        },
      },
    ],
    MetadataOptions: {
      // Important for ebs csi to work
      HttpTokens: "required", // enables IMDSv2
      HttpEndpoint: "enabled",

      // from docs: In order for the driver to access IMDS, it either must be run in host networking mode, or with a hop limit of at least 2.
      HttpPutResponseHopLimit: 3, // very very important
      // #########################
    },
    TagSpecifications: [
      {
        ResourceType: sdkec2.ResourceType.instance,
        Tags: [
          {
            Key: "Name",
            Value: `eks-${commonPrefix}-instance`,
          },
          ...Object.entries(commonTags || {}).map(([key, value]) => ({
            Key: key,
            Value: value,
          })),
        ],
      },
    ],
  };

  const cdkAttributes: cdkec2.LaunchTemplateProps = {
    // launchTemplateName: launchTemplateName,
    blockDevices: [
      {
        deviceName: "/dev/xvda",
        volume: cdkec2.BlockDeviceVolume.ebs(20),
      },
    ],
    // Important for ebs csi to work
    requireImdsv2: true,
    httpTokens: cdkec2.LaunchTemplateHttpTokens.REQUIRED,
    httpEndpoint: true,

    // from docs: In order for the driver to access IMDS, it either must be run in host networking mode, or with a hop limit of at least 2.
    httpPutResponseHopLimit: 3, // very important
    // #########################
    ...extraCDKAttributes,
  };

  return {
    launchTemplateName,
    sdkAttributes,
    cdkAttributes,
  };
}

// export async function upsertLaunchTemplate(
//   name: string,
//   props: sdkec2.RequestLaunchTemplateData
// ): Promise<LaunchTemplateConfig> {
//   try {
//     const describeParams: sdkec2.DescribeLaunchTemplatesCommandInput = {
//       LaunchTemplateNames: [name],
//     };

//     const describeResult = await ec2Client.send(
//       new sdkec2.DescribeLaunchTemplatesCommand(describeParams)
//     );
//     if (describeResult.LaunchTemplates && describeResult.LaunchTemplates.length > 0) {
//       console.log(
//         `LaunchTemplate: ${describeResult.LaunchTemplates.length} Launch templates found for ${name}`
//       );
//       const lt = describeResult.LaunchTemplates[0];
//       if (!lt) {
//         throw new Error("LaunchTemplate: Launch template not found");
//       }

//       const latestVersionNumber = lt.LatestVersionNumber ?? 0;
//       console.log(`LaunchTemplate: Latest version number: ${latestVersionNumber}`);
//       const describeVersionResult = await ec2Client.send(
//         new sdkec2.DescribeLaunchTemplateVersionsCommand({
//           LaunchTemplateId: lt.LaunchTemplateId,
//           Versions: [latestVersionNumber.toString()],
//         })
//       );
//       const latestVersion = describeVersionResult.LaunchTemplateVersions?.[0];
//       // Compare launch template configurations using stable hash
//       const responseData = latestVersion?.LaunchTemplateData || {};
//       const existingData = responseData as sdkec2.RequestLaunchTemplateData;

//       const existingHash = createLaunchTemplateConfigHash(existingData);
//       const newHash = createLaunchTemplateConfigHash(props);
//       const isSameProps = existingHash === newHash;

//       console.log(`LaunchTemplate: Existing hash: ${existingHash}, New hash: ${newHash}`);
//       console.log(`LaunchTemplate: Is Matching: ${isSameProps}`);
//       if (isSameProps) {
//         console.log("LaunchTemplate: No update needed");
//         return {
//           id: lt.LaunchTemplateId!,
//           name: lt.LaunchTemplateName!,
//           version: latestVersionNumber?.toString(),
//         };
//       }

//       console.log("LaunchTemplate: Data differs, creating new version");
//       const createVersionParams: sdkec2.CreateLaunchTemplateVersionCommandInput = {
//         LaunchTemplateId: lt.LaunchTemplateId!,
//         LaunchTemplateData: props,
//         VersionDescription: `Updated at ${new Date().toISOString()}`,
//       };
//       const versionResult = await ec2Client.send(
//         new sdkec2.CreateLaunchTemplateVersionCommand(createVersionParams)
//       );
//       return {
//         id: lt.LaunchTemplateId!,
//         name: lt.LaunchTemplateName!,
//         version: versionResult.LaunchTemplateVersion?.VersionNumber?.toString(),
//       };
//     }
//   } catch (e: unknown) {
//     console.log("LaunchTemplate: template not found, creating it below");
//     const error = e as { name?: string };
//     if (!error.name || error.name !== "InvalidLaunchTemplateName.NotFoundException") {
//       throw e;
//     }
//   }

//   const createParams: sdkec2.CreateLaunchTemplateCommandInput = {
//     LaunchTemplateName: name,
//     LaunchTemplateData: props,
//     VersionDescription: "Initial version",
//   };
//   const createResult = await ec2Client.send(new sdkec2.CreateLaunchTemplateCommand(createParams));
//   if (!createResult.LaunchTemplate) {
//     throw new Error("LaunchTemplate: Failed to create launch template");
//   }
//   return {
//     id: createResult.LaunchTemplate.LaunchTemplateId!,
//     name: createResult.LaunchTemplate.LaunchTemplateName!,
//     version: createResult.LaunchTemplate.LatestVersionNumber?.toString(),
//   };
// }

// export async function setupLaunchTemplate(
//   commonPrefix: string,
//   commonTags?: Record<string, string>
// ): Promise<LaunchTemplateConfig> {
//   const { launchTemplateName, sdkAttributes } = getLaunchTemplateSDKAndCDKAttributes(
//     commonPrefix,
//     commonTags
//   );
//   return upsertLaunchTemplate(launchTemplateName, sdkAttributes);
// }

/**
 * CDK-based launch template creation with hash-based change detection
 * This avoids unnecessary AWS API calls and version updates
 */
export function createLaunchTemplateWithCDK(
  scope: Construct,
  id: string,
  commonPrefix: string,
  commonTags?: Record<string, string>,
  extraCDKAttributes?: Partial<cdkec2.LaunchTemplateProps>
): cdkec2.LaunchTemplate {
  const { cdkAttributes } = getLaunchTemplateSDKAndCDKAttributes(
    commonPrefix,
    commonTags,
    extraCDKAttributes
  );

  // Create a hash of the configuration for change detection
  // const configHash = createLaunchTemplateConfigHash(sdkAttributes);

  // Include the hash in the launch template name to force recreation only when config changes
  // const launchTemplateName = `${cdkAttributes.launchTemplateName}-${configHash}`;

  // Create the launch template with CDK
  const launchTemplate = new cdkec2.LaunchTemplate(scope, id, {
    ...cdkAttributes,
    // launchTemplateName,
  });

  // Add a custom resource to manage the launch template lifecycle
  // const managedLaunchTemplate = new cr.AwsCustomResource(scope, `${id}-Manager`, {
  //   onCreate: {
  //     service: "EC2",
  //     action: "describeLaunchTemplates",
  //     parameters: {
  //       LaunchTemplateNames: [launchTemplateName],
  //     },
  //     physicalResourceId: cr.PhysicalResourceId.of(`${launchTemplateName}-${configHash}`),
  //     ignoreErrorCodesMatching: "InvalidLaunchTemplateName.NotFoundException",
  //   },
  //   onUpdate: {
  //     service: "EC2",
  //     action: "describeLaunchTemplates",
  //     parameters: {
  //       LaunchTemplateNames: [launchTemplateName],
  //     },
  //     physicalResourceId: cr.PhysicalResourceId.of(`${launchTemplateName}-${configHash}`),
  //     ignoreErrorCodesMatching: "InvalidLaunchTemplateName.NotFoundException",
  //   },
  //   policy: cr.AwsCustomResourcePolicy.fromSdkCalls({
  //     resources: cr.AwsCustomResourcePolicy.ANY_RESOURCE,
  //   }),
  // });

  // // Ensure the custom resource depends on the launch template
  // managedLaunchTemplate.node.addDependency(launchTemplate);

  return launchTemplate;
}

/**
 * Alternative: Create launch template with explicit version management
 * This gives you more control over when versions are created
 */
// export function createVersionedLaunchTemplate(
//   scope: Construct,
//   id: string,
//   commonPrefix: string,
//   commonTags?: Record<string, string>
// ): { launchTemplate: cdkec2.LaunchTemplate; configHash: string } {
//   const { cdkAttributes, sdkAttributes } = getLaunchTemplateSDKAndCDKAttributes(
//     commonPrefix,
//     commonTags
//   );

//   const configHash = createLaunchTemplateConfigHash(sdkAttributes);

//   const launchTemplate = new cdkec2.LaunchTemplate(scope, id, {
//     ...cdkAttributes,
//     launchTemplateName: cdkAttributes.launchTemplateName,
//   });

//   launchTemplate.node.addMetadata("configHash", configHash);
//   launchTemplate.node.addMetadata("lastUpdated", new Date().toISOString());

//   return { launchTemplate, configHash };
// }
