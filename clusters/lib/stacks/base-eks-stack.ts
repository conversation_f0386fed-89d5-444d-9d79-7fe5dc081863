import { KubectlV32Layer } from "@aws-cdk/lambda-layer-kubectl-v32";
import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as eks from "aws-cdk-lib/aws-eks";
import * as iam from "aws-cdk-lib/aws-iam";
import type { Construct } from "constructs";
import { addAccessEntries } from "../helpers/access-entries-helper";
import {
  type ClusterExecutionContext,
  createStackPropsFromContext,
} from "../helpers/cluster-context";
import { setupKarpenter } from "../helpers/karpenter-helper";
import { createLaunchTemplateWithCDK } from "../helpers/launch-template-helper";
import { cdkIRSARequiredRolesInIam } from "../helpers/roles-helper";
import type { VpcInfo } from "../helpers/vpc-helper";
import {
  createMultipleVpcPeerings,
  createVpnVpcPeering,
  validateVpcPeeringConfigurations,
} from "../helpers/vpc-peering-helper";

export class TreeboBaseStack extends cdk.Stack {
  public readonly vpc: ec2.Vpc;
  public readonly cluster: eks.Cluster;
  public readonly context: ClusterExecutionContext;

  constructor(scope: Construct, id: string, context: ClusterExecutionContext) {
    const stackProps = createStackPropsFromContext(context);
    super(scope, id, stackProps);

    this.context = context;
    const nameGenerator = this.context.nameGenerator;

    this.vpc = new ec2.Vpc(this, nameGenerator.getVpcName(), {
      vpcName: nameGenerator.getVpcName(),
      maxAzs: 3,
      natGateways: 1,
      ipAddresses: ec2.IpAddresses.cidr(this.context.vpcCidrBlock),
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: "public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: "private",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
      ],
      // securityGroup: this.vpc.vpcDefaultSecurityGroup,
    });

    const kubectlLayer = new KubectlV32Layer(this, "KubectlLayer");

    this.cluster = new eks.Cluster(this, nameGenerator.getClusterName(), {
      vpc: this.vpc,
      clusterName: nameGenerator.getClusterName(),
      defaultCapacity: 0, // Set to 0 as we'll create our own nodegroups
      version: eks.KubernetesVersion.V1_32,
      vpcSubnets: [{ subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS }],
      kubectlLayer,
      authenticationMode: eks.AuthenticationMode.API_AND_CONFIG_MAP,
      tags: context.getStandardizedTags(),
    });

    for (const [tagName, tagValue] of Object.entries(context.getStandardizedTags())) {
      cdk.Tags.of(this.vpc).add(tagName, tagValue);
      // VPC tags automatically propagate to subnets and route tables
      // No need to tag subnets individually for standardized tags
    }

    const launchTemplate = createLaunchTemplateWithCDK(
      this,
      nameGenerator.getLaunchTemplateName(),
      this.context.commonPrefix,
      this.context.getStandardizedTags()
    );

    // Create a system nodegroup with system-critical taint
    const systemNodegroup = this.cluster.addNodegroupCapacity(
      `node-group-system-${this.context.commonPrefix}`,
      {
        // dont specify nodegroup names even if they are aweful. it leads to headaches
        // ref: https://medium.com/adevinta-tech-blog/dont-name-your-eks-managed-nodegroups-unless-you-want-to-trigger-an-incident-77709c68ab6a
        // nodegroupName: nameGenerator.getResourceName("system-ng"),
        instanceTypes: [ec2.InstanceType.of(ec2.InstanceClass.T3A, ec2.InstanceSize.MEDIUM)],
        minSize: 1,
        desiredSize: 2,
        maxSize: 3,
        // diskSize: 20,
        amiType: eks.NodegroupAmiType.AL2023_X86_64_STANDARD,
        launchTemplateSpec: {
          id: launchTemplate.launchTemplateId!,
          // version: launchTemplate.versionNumber,
        },
        subnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
        labels: {
          "node-type": "system",
          "pod-capacity-type": "ON_DEMAND",
          "workload-type": "system-critical",
        },
        taints: [
          {
            key: "CriticalAddonsOnly",
            value: "true",
            effect: eks.TaintEffect.NO_SCHEDULE,
          },
        ],
        tags: {
          Name: `node-group-system-${nameGenerator.getClusterName()}`,
          "node-type": "system",
          "workload-type": "system-critical",
          // Enable cluster autoscaler management - ONLY for SystemNodeGroup
          "k8s.io/cluster-autoscaler/enabled": "true",
          [`k8s.io/cluster-autoscaler/${nameGenerator.getClusterName()}`]: "owned",
          "cluster-autoscaler/managed": "true", // Explicit tag for cluster autoscaler management
          ...context.getStandardizedTags(),
        },
      }
    );

    // Add SSM permissions to the system nodegroup for remote access and management
    systemNodegroup.role.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSSMManagedInstanceCore")
    );

    this.setupVpcPeering();

    addAccessEntries(this, this.cluster, this.context);

    // Setup Karpenter for dynamic node provisioning
    // Make sure Karpenter depends on the system nodegroup
    setupKarpenter(this, this.cluster, this.context);
    cdkIRSARequiredRolesInIam(this, this.cluster, this.context);

    // Add Name tags to VPC
    cdk.Tags.of(this.vpc).add("Name", nameGenerator.getVpcName());
    cdk.Tags.of(launchTemplate).add("Name", nameGenerator.getLaunchTemplateName());

    // Add Name tags to subnets (Name tags are resource-specific, not inherited)
    this.vpc.publicSubnets.forEach((subnet, _index) => {
      const az = subnet.availabilityZone;
      cdk.Tags.of(subnet).add("Name", nameGenerator.getSubnetName("public", az));
      // Add subnet-specific tags that should NOT propagate to route tables
      // cdk.Tags.of(subnet).add("SubnetType", "public");
    });

    this.vpc.privateSubnets.forEach((subnet, _index) => {
      const az = subnet.availabilityZone;
      cdk.Tags.of(subnet).add("Name", nameGenerator.getSubnetName("private", az));
      // Add subnet-specific tags that should NOT propagate to route tables
      // cdk.Tags.of(subnet).add("SubnetType", "private");
    });

    // Route table tagging is disabled to avoid duplicate tags
    // Route tables inherit tags from their associated subnets and VPC
    // If you need specific route table tags, use AWS CLI or custom resources

    // Note: subnet.routeTable is an interface and doesn't support direct CDK tagging
    // Use AWS CLI for route table specific tagging if needed:
    // aws ec2 create-tags --resources rtb-12345678 --tags Key=Name,Value=my-route-table
  }

  /**
   * Sets up VPC peering connections based on context configuration
   * Supports both single VPN VPC and multiple peering VPCs
   */
  private setupVpcPeering(): void {
    // Get VPN VPC if configured
    const vpnVpc = this.context.vpnVpc;

    // Get additional peering VPCs if configured
    const peeringVpcs = this.context.peeringVpcs || [];

    // Combine all VPCs for validation
    const allPeeringVpcs: VpcInfo[] = [];

    // Add VPN VPC if configured
    if (vpnVpc) {
      allPeeringVpcs.push({
        vpcId: vpnVpc.vpcId,
        vpcName: vpnVpc.vpcName,
        cidrBlock: vpnVpc.cidrBlock,
        description: "VPN VPC connection",
        enabled: true,
      });
    }

    // Add additional peering VPCs
    if (peeringVpcs.length > 0) {
      allPeeringVpcs.push(...peeringVpcs);
    }

    if (allPeeringVpcs.length === 0) {
      console.warn("No VPCs configured for peering");
      return;
    }

    // Validate all VPC peering configurations
    validateVpcPeeringConfigurations(allPeeringVpcs, this.context.vpcCidrBlock);

    // Create peering connections
    console.log(`Setting up ${allPeeringVpcs.length} VPC peering connections`);

    // Create VPN VPC peering if configured
    if (vpnVpc) {
      console.log(`Setting up VPN VPC peering connection to ${vpnVpc.vpcName || vpnVpc.vpcId}`);
      createVpnVpcPeering(this, this.vpc, vpnVpc, this.context);
      console.log("Successfully created VPN VPC peering connection");
    }

    // Create additional peering connections if configured
    if (peeringVpcs.length > 0) {
      console.log(`Setting up ${peeringVpcs.length} additional VPC peering connections`);
      const result = createMultipleVpcPeerings(this, this.vpc, peeringVpcs, this.context);
      console.log(
        `Successfully created ${result.totalConnections} additional VPC peering connections`
      );
    }
  }

  // async postConstructSetup() {
  //   // Step 1: Setup S3 buckets for tools that need them
  //   console.log("🪣 Setting up S3 buckets...");
  //   try {
  //     const bucketMap = await setupAllS3BucketsWithContext(this.context);
  //     console.log("✅ S3 buckets setup completed");
  //     console.log(
  //       "📝 Created buckets:",
  //       Object.entries(bucketMap)
  //         .map(([tool, bucket]) => `${tool}:${bucket}`)
  //         .join(", ")
  //     );
  //   } catch (error) {
  //     console.error("❌ S3 buckets setup failed:", error);
  //     // Don't throw here to allow service account setup to proceed
  //     // S3 buckets can be set up manually later if needed
  //   }

  //   // Step 2: Setup service account roles for all tools
  //   console.log("🔧 Setting up service account roles...");
  //   try {
  //     await setupAllServiceAccountRolesWithContext(this.context);
  //     console.log("✅ Service account roles setup completed");
  //   } catch (error) {
  //     console.error("❌ Service account roles setup failed:", error);
  //     // Don't throw here to allow cluster creation to complete
  //     // Service accounts can be set up manually later if needed
  //   }
  // }
}
