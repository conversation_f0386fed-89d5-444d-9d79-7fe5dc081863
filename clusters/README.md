# EKS Cluster Infrastructure

This directory contains the AWS CDK infrastructure code for deploying EKS clusters with comprehensive VPC peering, Karpenter autoscaling, and security configurations.

## Features

- **Automated EKS Cluster Setup**: Complete cluster with managed node groups
- **Karpenter Integration**: Dynamic node provisioning and scaling
- **Multiple VPC Peering**: Connect to multiple VPN VPCs for different purposes
- **Flexible VPN Access**: Office, partner, monitoring, and DR connections
- **Security Groups**: Properly configured for cluster and node communication
- **IAM Roles**: Service accounts and IRSA setup for AWS integrations
- **Launch Templates**: Optimized for different workload types
- **Tagging Strategy**: Consistent resource tagging for cost allocation

## Architecture

### Core Components

1. **EKS Cluster**: Kubernetes v1.32 with API and ConfigMap authentication
2. **VPC**: 3-AZ setup with public/private subnets and single NAT gateway
3. **System Node Group**: Dedicated nodes for system workloads with taints
4. **Karpenter**: Dynamic node provisioning for application workloads
5. **VPC Peering**: Multiple connections to VPN VPCs for secure access

### VPC Peering Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Office VPN    │    │  Partner VPN    │    │ Monitoring VPN  │
│   10.0.0.0/16   │    │   ********/16   │    │   ********/16   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ Peering              │ Peering              │ Peering
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │      EKS Cluster       │
                    │     **********/16      │
                    │                        │
                    │  ┌─────────────────┐   │
                    │  │ System Nodes    │   │
                    │  │ (Managed)       │   │
                    │  └─────────────────┘   │
                    │                        │
                    │  ┌─────────────────┐   │
                    │  │ Karpenter Nodes │   │
                    │  │ (Dynamic)       │   │
                    │  └─────────────────┘   │
                    └────────────────────────┘
```

## Directory Structure

```
clusters/
├── docs/                           # Documentation
│   └── vpc-peering-guide.md       # VPC peering configuration guide
├── examples/                       # Example configurations
│   └── multiple-vpn-vpcs-config.ts # Multiple VPN VPC examples
├── lib/                           # CDK library code
│   ├── helpers/                   # Helper modules
│   │   ├── cluster-context.ts     # Cluster configuration context
│   │   ├── vpc-helper.ts          # VPC utilities and validation
│   │   ├── vpc-peering-helper.ts  # VPC peering management
│   │   ├── karpenter-helper.ts    # Karpenter setup
│   │   └── ...                    # Other helpers
│   └── stacks/                    # CDK stack definitions
│       └── base-eks-stack.ts      # Main EKS stack
├── scripts/                       # Utility scripts
│   ├── migrate-vpn-config.ts      # VPN configuration migration
│   ├── cdk-eks-setup.sh          # Cluster deployment script
│   └── ...                       # Other scripts
└── cluster-*/                     # Environment-specific configurations
    ├── cluster-treebo-production/
    ├── cluster-treebo-staging/
    └── cluster-treebo-tools-common/
```

## Quick Start

### 1. Configure VPN VPCs

Choose between single VPN VPC (legacy) or multiple VPN VPCs:

#### Multiple VPN VPCs (Recommended)

```typescript
export const clusterConfig = createClusterExecutionContext({
  commonPrefix: "my-cluster",
  region: "ap-south-1",
  accountId: "********9012",
  environment: "production",
  vpcCidrBlock: "**********/16",

  vpnVpcs: [
    {
      vpcId: "vpc-********",
      vpcName: "office-vpn-vpc",
      cidrBlock: "10.0.0.0/16",
      description: "Office VPN connection",
      enabled: true,
    },
    {
      vpcId: "vpc-********",
      vpcName: "partner-vpn-vpc",
      cidrBlock: "********/16",
      description: "Partner integrations VPN",
      enabled: true,
    },
  ],
});
```

#### Single VPN VPC (Legacy)

```typescript
export const legacyConfig = createClusterExecutionContext({
  commonPrefix: "legacy-cluster",
  region: "ap-south-1",
  accountId: "********9012",
  environment: "production",
  vpcCidrBlock: "**********/16",

  vpnVpc: {
    vpcId: "vpc-legacy123",
    vpcName: "office-vpn",
    cidrBlock: "***********/16",
  },
});
```

### 2. Deploy Cluster

```bash
# Navigate to environment directory
cd cluster-treebo-staging

# Deploy the stack
npx cdk deploy

# Or use the setup script
../scripts/cdk-eks-setup.sh
```

### 3. Verify Deployment

```bash
# Update kubeconfig
aws eks update-kubeconfig --region ap-south-1 --name your-cluster-name

# Check cluster status
kubectl get nodes
kubectl get pods -A

# Verify VPC peering
aws ec2 describe-vpc-peering-connections --region ap-south-1
```

## VPC Peering Configuration

### Supported Use Cases

1. **Office Connectivity**: Connect to office VPN for employee access
2. **Partner Integrations**: Secure connections to partner systems
3. **Monitoring**: External monitoring and alerting systems
4. **Disaster Recovery**: Backup site connectivity
5. **Development**: Isolated development environment access

### Configuration Options

| Property        | Type    | Required | Description                                                      |
| --------------- | ------- | -------- | ---------------------------------------------------------------- |
| `vpcId`         | string  | Yes      | AWS VPC ID (vpc-xxxxxxxx)                                        |
| `vpcName`       | string  | No       | Human-readable VPC name                                          |
| `cidrBlock`     | string  | Yes      | VPC CIDR block                                                   |
| `description`   | string  | No       | Connection purpose description                                   |
| `enabled`       | boolean | No       | Enable/disable peering (default: true)                           |
| `peerAccountId` | string  | No       | AWS Account ID for cross-account peering (12 digits)             |
| `peerRegion`    | string  | No       | AWS Region for cross-region peering (defaults to current region) |

### Validation Features

- **CIDR Overlap Detection**: Prevents overlapping network ranges
- **Duplicate VPC Check**: Ensures unique VPC IDs
- **Format Validation**: Validates VPC IDs and CIDR formats
- **Automatic Route Creation**: Sets up routing tables automatically
- **Cross-Account Support**: Handles cross-account peering with acceptance requirements

### Cross-Account VPC Peering

For VPCs in different AWS accounts, additional configuration and manual steps are required:

#### Configuration Example

**❌ Don't use fetch functions for cross-account VPCs:**

```typescript
// This will FAIL during CDK synthesis
peeringVpcs: [
  await fetchVpcAttributesByName("partner-vpc", "ap-south-1"), // ❌ Cross-account
];
```

**✅ Use static configuration for cross-account VPCs:**

```typescript
// Define cross-account VPCs statically
export const PARTNER_VPC_INFO = {
  vpcId: "vpc-********",
  vpcName: "partner-vpc",
  cidrBlock: "********/16",
  description: "Partner account VPC",
  enabled: true,
  peerAccountId: "********9012", // Partner's AWS Account ID
  peerRegion: "ap-south-1", // Optional: defaults to current region
};

// Use in configuration
vpnVpcs: [
  PARTNER_VPC_INFO, // ✅ Static config works
];
```

**✅ Mixed same-account and cross-account:**

```typescript
peeringVpcs: [
  await fetchVpcAttributesByName("same-account-vpc", "ap-south-1"), // ✅ Same account
  PARTNER_VPC_INFO, // ✅ Cross-account static config
];
```

#### Manual Steps Required

Cross-account peering connections require **two manual steps** in the peer account:

**🚀 Option 1: Automated Full Setup (Recommended)**

```bash
# One command does everything: accept connection + add routes
./scripts/manage-vpc-peering.sh full-setup pcx-******** --region ap-south-1
```

**🔧 Option 2: Manual Step-by-Step**

```bash
# Step 1: List and accept pending connections
./scripts/manage-vpc-peering.sh list-pending --region ap-south-1
./scripts/manage-vpc-peering.sh accept pcx-******** --region ap-south-1

# Step 2: Add return routes to enable bidirectional traffic
./scripts/manage-vpc-peering.sh add-routes vpc-your-vpc **********/16 pcx-******** --region ap-south-1
```

**🔍 Additional Commands**

```bash
# List route tables in your VPC
./scripts/manage-vpc-peering.sh list-routes vpc-your-vpc --region ap-south-1

# Remove routes (for cleanup)
./scripts/manage-vpc-peering.sh remove-routes vpc-your-vpc **********/16 --region ap-south-1

# Accept all pending connections (use with caution)
./scripts/manage-vpc-peering.sh accept-all --region ap-south-1 --confirm
```

> ⚠️ **Important**: Both acceptance AND route addition are required! Without routes, traffic will be one-way only (EKS → Peer works, but Peer → EKS fails).

## Migration Guide

### From Single to Multiple VPN VPCs

Use the migration script to transition existing configurations:

```bash
# Validate current configuration
npx ts-node scripts/migrate-vpn-config.ts validate config.ts

# Migrate configuration
npx ts-node scripts/migrate-vpn-config.ts migrate \
  --input old-config.ts \
  --output new-config.ts \
  --region ap-south-1
```

## Environment Configurations

### Production

- Multiple VPN VPCs for comprehensive access
- High availability with 3 AZs
- Dedicated system node group
- Comprehensive monitoring

### Staging

- Selective VPN access for testing
- Cost-optimized configuration
- Development-friendly settings

### Tools/Common

- Shared infrastructure components
- Cross-environment connectivity
- Centralized monitoring and logging

## Security Considerations

### Network Security

- Private subnets for worker nodes
- Security groups with least privilege
- VPC peering with specific route targets

### Access Control

- IAM roles with minimal permissions
- Service accounts for workload identity
- System node group isolation with taints

### Monitoring

- CloudTrail logging enabled
- VPC Flow Logs for network monitoring
- Resource tagging for cost allocation

## Cost Optimization

### Karpenter Benefits

- Dynamic node provisioning
- Right-sizing based on workload requirements
- Automatic scale-down during low usage

### VPC Peering

- Disable unused peering connections
- Monitor data transfer costs
- Use VPC endpoints where appropriate

### Resource Management

- Spot instances for non-critical workloads
- Proper resource requests and limits
- Regular cost reviews and optimization

## Troubleshooting

### Common Issues

1. **CIDR Overlap**: Use non-overlapping network ranges
2. **VPC Not Found**: Verify VPC exists in correct region
3. **Peering Connection Failed**: Check security groups and route tables
4. **Node Group Issues**: Verify launch template and IAM roles

### Debugging Commands

```bash
# Check cluster status
kubectl cluster-info

# Verify nodes
kubectl get nodes -o wide

# Check Karpenter logs
kubectl logs -n karpenter deployment/karpenter

# Verify VPC peering status
aws ec2 describe-vpc-peering-connections --region ap-south-1

# Check route tables (replace with actual route table IDs)
aws ec2 describe-route-tables --route-table-ids rtb-******** --region ap-south-1

# Test connectivity from EKS to peer VPC
kubectl run test-pod --image=busybox --rm -it -- ping <peer-vpc-ip>
```

### Cross-Account Peering Troubleshooting

**Problem**: One-way connectivity (EKS → Peer works, Peer → EKS fails)

- **Cause**: Missing return routes in peer account
- **Solution**: `./scripts/manage-vpc-peering.sh add-routes <vpc-id> <eks-cidr> <connection-id>`

**Problem**: Connection stuck in "pending-acceptance"

- **Cause**: Peer account hasn't accepted the connection
- **Solution**: `./scripts/manage-vpc-peering.sh accept <connection-id>` in peer account

**Problem**: "Route already exists" error

- **Cause**: Routes were previously added
- **Solution**: Check existing routes with `./scripts/manage-vpc-peering.sh list-routes <vpc-id>`

**Problem**: No connectivity despite active peering

- **Cause**: Security groups or NACLs blocking traffic
- **Solution**: Check security group rules and NACL entries

## Documentation

- [VPC Peering Guide](docs/vpc-peering-guide.md) - Comprehensive VPC peering configuration
- [Examples](examples/) - Configuration examples for different scenarios
- [Migration Scripts](scripts/) - Tools for configuration migration

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review the documentation
3. Validate configuration with migration scripts
4. Contact the DevOps team for assistance
