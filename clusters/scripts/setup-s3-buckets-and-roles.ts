import { DescribeClusterCommand, EKSClient } from "@aws-sdk/client-eks";
import { IAMClient } from "@aws-sdk/client-iam";
import { S3Client } from "@aws-sdk/client-s3";
import * as fs from "fs";
import * as path from "path";
import { AWS_MUMBAI_REGION } from "../lib/helpers/constants";
import { sdkCreateRolesForS3Buckets } from "../lib/helpers/roles-helper";
import {
  createS3Bucket,
  createS3PolicyDocument,
  defaultLifecycleConfiguration,
  updateBucketACL,
  updateBucketEncryption,
  updateBucketLifecycleConfiguration,
  veleroLifecycleConfiguration,
} from "../lib/helpers/s3-helper";

type S3SetupConfig = {
  bucketName: string;
  toolNameSpace: string;
  toolServiceAccountName: string;
  description: string;
  additionalPolicyPath?: string; // Optional path to additional policy JSON file (relative to repo root)
};

/**
 * S3 bucket configurations for tools.
 *
 * To add additional IAM permissions beyond S3 access:
 * 1. Create a policy.json file in your tool directory (e.g., tools/my-tool/policy.json)
 * 2. Add additionalPolicyPath: "tools/my-tool/policy.json" to your config
 * 3. The script will automatically merge your policy with the base S3 policy
 */
export const S3_BUCKET_CONFIGS: Record<string, S3SetupConfig> = {
  loki: {
    bucketName: "s3-loki-treebo-v1",
    toolNameSpace: "monitoring",
    toolServiceAccountName: "loki",
    description: "Loki logs storage",
  },
  mimir: {
    bucketName: "s3-mimir-treebo-v1",
    toolNameSpace: "monitoring",
    toolServiceAccountName: "mimir",
    description: "Mimir metrics storage",
  },
  pyroscope: {
    bucketName: "s3-pyroscope-treebo-v1",
    toolNameSpace: "monitoring",
    toolServiceAccountName: "pyroscope",
    description: "Pyroscope profiling data storage",
  },
  tempo: {
    bucketName: "s3-tempo-treebo-v1",
    toolNameSpace: "monitoring",
    toolServiceAccountName: "tempo",
    description: "Tempo traces storage",
  },
  velero: {
    bucketName: "s3-velero-treebo-v1",
    toolNameSpace: "velero",
    toolServiceAccountName: "velero-sa",
    description: "Velero backup storage",
  },
  drone: {
    bucketName: "s3-drone-treebo-v1",
    toolNameSpace: "default",
    toolServiceAccountName: "drone-runner-sa",
    description: "Drone runner storage",
  },
  plane: {
    bucketName: "s3-plane-treebo-v1",
    toolNameSpace: "plane",
    toolServiceAccountName: "plane-srv-account",
    description: "Plane file storage",
    additionalPolicyPath: "tools/plane/policy.json",
  },
};

/**
 * Loads and merges additional policy from file with the base S3 policy
 */
function createMergedPolicyDocument(bucketName: string, additionalPolicyPath?: string): any {
  let policyDocument = createS3PolicyDocument(bucketName, "");

  if (additionalPolicyPath) {
    try {
      // Resolve path relative to the repository root (two levels up from this script)
      const scriptDir = __dirname;
      const repoRoot = path.resolve(scriptDir, "..", "..");
      const fullPolicyPath = path.resolve(repoRoot, additionalPolicyPath);

      const additionalPolicyContent = fs.readFileSync(fullPolicyPath, "utf8");
      const additionalPolicy = JSON.parse(additionalPolicyContent);

      // Merge the statements from additional policy
      if (additionalPolicy.Statement && Array.isArray(additionalPolicy.Statement)) {
        policyDocument = {
          ...policyDocument,
          Statement: [
            ...policyDocument.Statement,
            ...additionalPolicy.Statement
          ]
        };
      }
    } catch (error) {
      console.warn(`Warning: Could not load additional policy from ${additionalPolicyPath}:`, error);
    }
  }

  return policyDocument;
}

export async function setupS3Bucket(commonPrefix: string): Promise<void> {
  const s3Client = new S3Client({ region: AWS_MUMBAI_REGION });
  const iamClient = new IAMClient();
  const eksClient = new EKSClient();

  const clusterInfo = await eksClient.send(
    new DescribeClusterCommand({
      name: `cluster-${commonPrefix}`,
    })
  );
  const oidcProvider = clusterInfo.cluster?.identity?.oidc?.issuer;
  if (!oidcProvider) {
    throw new Error("OIDC provider not found");
  }
  const accountId = clusterInfo.cluster?.arn?.split(":")[4];
  if (!accountId) {
    throw new Error("Account ID not found");
  }
  const oidcProviderArn = oidcProvider.replace("https://", "");
  console.log(
    `OIDC provider: ${oidcProvider} \n  ARN: ${oidcProviderArn} \n  Account ID: ${accountId}`
  );

  for (const [key, config] of Object.entries(S3_BUCKET_CONFIGS)) {
    await createS3Bucket(s3Client, config.bucketName);
    await updateBucketEncryption(s3Client, config.bucketName);
    await updateBucketACL(s3Client, config.bucketName);

    // Use Velero-specific lifecycle configuration for Velero bucket
    const lifecycleConfig =
      key === "velero" ? veleroLifecycleConfiguration : defaultLifecycleConfiguration;
    await updateBucketLifecycleConfiguration(s3Client, config.bucketName, lifecycleConfig);

    // await createS3BucketDirectory(s3Client, config.bucketName, commonPrefix);

    // Create policy document, merging additional policies if specified
    const policyDocument = createMergedPolicyDocument(config.bucketName, config.additionalPolicyPath);

    const roleName = `role-eks-${config.bucketName}`;
    sdkCreateRolesForS3Buckets(
      iamClient,
      roleName,
      accountId,
      oidcProviderArn,
      config.toolServiceAccountName,
      config.toolNameSpace,
      policyDocument
    );
  }
}

async function main() {
  if (process.argv.length < 3) {
    console.error(
      "Please provide commonPrefix as argument. Example: npx ts-node setup-s3-buckets-and-roles.ts tools-treebo-common"
    );
    process.exit(1);
  }
  const commonPrefix = process.argv[2]!;
  await setupS3Bucket(commonPrefix);
}

main();
