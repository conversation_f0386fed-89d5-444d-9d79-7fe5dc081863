#!/bin/bash

# Usage: ./cdk-eks-setup.sh <environment-name>
# Example: ./cdk-eks-setup.sh cluster-treebo-tools-common

set -e

if [ -z "$1" ]; then
  echo "Error: No environment name provided."
  echo "Usage: $0 <environment-name>"
  exit 1
fi

ENV_NAME="$1"
STACK_NAME="stack-$ENV_NAME"
STAGE_NAME="stage-$ENV_NAME"
STACK_FILE="clusters/cluster-$ENV_NAME/stack.ts"

if [ ! -f "$STACK_FILE" ]; then
  echo "Error: Stack file $STACK_FILE does not exist!"
  exit 1
fi

# Print AWS environment variables for debugging
env | grep AWS || true

# Check for --bootstrap flag
BOOTSTRAP=false
for arg in "$@"; do
  if [ "$arg" = "--bootstrap" ]; then
    BOOTSTRAP=true
    # Remove the --bootstrap flag from positional parameters
    set -- "${@/--bootstrap/}"
    break
  fi
done

echo "Installing npm dependencies..."
npm install

if [ "$BOOTSTRAP" = true ]; then
  echo "Bootstrapping AWS CDK environment..."
  npx cdk bootstrap --app "npx ts-node $STACK_FILE"
fi

# echo "Synthesizing CDK stack for $STAGE_NAME..."
# npx cdk synth --quiet true "$STACK_NAME" --app "npx ts-node $STACK_FILE"

echo "1️⃣ Clearing CDK context cache..."
npx cdk context "$STACK_NAME" --app "npx ts-node $STACK_FILE" --clear
echo "✅ CDK context cleared"
echo ""

echo "2️⃣ Running CDK diff to see what needs to be fixed..."
npx cdk diff "$STACK_NAME" --app "npx ts-node $STACK_FILE" || true
echo ""

echo "Deploying EKS stack: $STAGE_NAME..."
npx cdk deploy "$STACK_NAME" --app "npx ts-node $STACK_FILE" --require-approval never

echo "\nAll steps completed successfully for $ENV_NAME!"
