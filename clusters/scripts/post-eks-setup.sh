#!/bin/bash

set -e

if [ -z "$1" ]; then
  echo "Error: No environment name provided."
  echo "Usage: $0 <environment-name> [--bootstrap]"
  exit 1
fi

ENV_NAME="$1"

# Check for --bootstrap flag
BOOTSTRAP=false
for arg in "$@"; do
  if [ "$arg" = "--bootstrap" ]; then
    BOOTSTRAP=true
    break
  fi
done

# Verify kubectl is configured and working
echo "Verifying cluster connection..."
if ! kubectl get nodes &>/dev/null; then
  echo "Error: Failed to connect to the cluster. Make sure kubectl is properly configured."
  exit 1
fi

# Get the cluster name from the current context
CURRENT_CONTEXT=$(kubectl config current-context)
if [ $? -ne 0 ]; then
  echo "Error: Failed to get current kubectl context."
  exit 1
fi

# For EKS clusters, the context is typically in the format: arn:aws:eks:<region>:<account>:cluster/<cluster-name>
# Extract the cluster name from the context
CLUSTER_NAME=$(echo "$CURRENT_CONTEXT" | sed -n 's/.*cluster\/\(.*\)/\1/p')

# If the above doesn't work, try getting it from AWS CLI if available
if [ -z "$CLUSTER_NAME" ] && command -v aws &>/dev/null; then
  echo "Attempting to get cluster name from AWS CLI..."
  # Get all clusters and find one that matches the endpoint in current context
  ENDPOINT=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}')
  CLUSTER_NAME="cluster-$ENV_NAME"
  CLUSTER_ENDPOINT=$(aws eks describe-cluster --name $CLUSTER_NAME --query "cluster.endpoint" --output text)
  if [ "$ENDPOINT" = "$CLUSTER_ENDPOINT" ]; then
    echo "Found cluster name from AWS CLI: $CLUSTER_NAME"
  else
    echo "Error: Cluster endpoint from context ($ENDPOINT) does not match any EKS cluster endpoint."
    exit 1
  fi
fi

echo "Connected to cluster: $CLUSTER_NAME"

# Verify cluster name matches ENV_NAME
if [[ "$CLUSTER_NAME" != "cluster-$ENV_NAME" ]]; then
  echo "Error: Connected to cluster '$CLUSTER_NAME' but expected 'cluster-$ENV_NAME'."
  echo "Please ensure you're connected to the correct cluster before running this script."
  exit 1
fi

echo "✅ Connected to the correct cluster for environment: $ENV_NAME"

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
echo "Script directory: $SCRIPT_DIR"

# Only install CRDs if --bootstrap flag is provided
if [ "$BOOTSTRAP" = true ]; then
  bash $SCRIPT_DIR/../../tools/karpenter/install.sh $ENV_NAME

  echo "Installing CRDs for Prometheus, External Secrets, and ArgoCD..."
  bash $SCRIPT_DIR/../../tools/prometheus/install-crds.sh
  bash $SCRIPT_DIR/../../tools/external-secrets/install-crds.sh
  echo "CRD installation completed."
else
  echo "Skipping CRD installation. Use --bootstrap flag to install CRDs."
fi

# Add additional post-setup steps here that should run regardless of bootstrap flag
echo "Running additional post-setup steps..."
bash $SCRIPT_DIR/../../tools/argocd/install.sh $ENV_NAME
echo "Post-EKS setup completed for $ENV_NAME!"
