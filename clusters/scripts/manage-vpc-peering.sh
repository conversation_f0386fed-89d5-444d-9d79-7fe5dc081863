#!/bin/bash

# Cross-Account VPC Peering Management Script
# Handles both acceptance and route management for VPC peering connections
#
# Usage:
#   ./manage-vpc-peering.sh list-pending [--region REGION]
#   ./manage-vpc-peering.sh accept <CONNECTION_ID> [--region REGION]
#   ./manage-vpc-peering.sh accept-all [--region REGION] [--confirm]
#   ./manage-vpc-peering.sh list-routes <VPC_ID> [--region REGION]
#   ./manage-vpc-peering.sh add-routes <CONNECTION_ID> [--region REGION]
#   ./manage-vpc-peering.sh remove-routes <CONNECTION_ID> [--region REGION]
#   ./manage-vpc-peering.sh full-setup <CONNECTION_ID> [--region REGION]

set -e

# Default values
REGION="ap-south-1"
CONFIRM=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_usage() {
    echo "Cross-Account VPC Peering Management Script"
    echo ""
    echo "Usage:"
    echo "  $0 list-pending [--region REGION]"
    echo "  $0 accept <CONNECTION_ID> [--region REGION]"
    echo "  $0 accept-all [--region REGION] [--confirm]"
    echo "  $0 list-routes <VPC_ID> [--region REGION]"
    echo "  $0 add-routes <CONNECTION_ID> [--region REGION]"
    echo "  $0 remove-routes <CONNECTION_ID> [--region REGION]"
    echo "  $0 full-setup <CONNECTION_ID> [--region REGION]"
    echo ""
    echo "Commands:"
    echo "  list-pending     List all pending VPC peering connections"
    echo "  accept          Accept a specific peering connection"
    echo "  accept-all      Accept all pending connections (requires --confirm)"
    echo "  list-routes     List route tables for a VPC"
    echo "  add-routes      Add routes for peering connection to both VPCs"
    echo "  remove-routes   Remove routes from both VPCs"
    echo "  full-setup      Accept connection and add routes automatically"
    echo ""
    echo "Options:"
    echo "  -r, --region    AWS region (default: ap-south-1)"
    echo "  -c, --confirm   Confirm bulk operations"
    echo "  -h, --help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 list-pending --region us-east-1"
    echo "  $0 list-pending -r us-east-1"
    echo "  $0 accept pcx-12345678 --region ap-southeast-1"
    echo "  $0 accept pcx-12345678 -r ap-southeast-1"
    echo "  $0 add-routes pcx-12345678"
    echo "  $0 full-setup pcx-12345678"
    echo "  $0 accept-all --confirm --region us-west-2"
    echo "  $0 accept-all -c -r us-west-2"
}



# List pending VPC peering connections
list_pending_connections() {
    log_info "Listing pending VPC peering connections in region: $REGION"
    echo ""

    local connections=$(aws ec2 describe-vpc-peering-connections \
        --region "$REGION" \
        --filters "Name=status-code,Values=pending-acceptance" \
        --query 'VpcPeeringConnections[*].[VpcPeeringConnectionId,RequesterVpcInfo.VpcId,AccepterVpcInfo.VpcId,RequesterVpcInfo.OwnerId,AccepterVpcInfo.OwnerId,RequesterVpcInfo.CidrBlock,AccepterVpcInfo.CidrBlock,RequesterVpcInfo.Region,AccepterVpcInfo.Region]' \
        --output text)

    if [[ -z "$connections" ]]; then
        log_success "No pending VPC peering connections found"
        return 0
    fi

    echo "Found pending connection(s):"
    echo ""

    local count=0
    while IFS=$'\t' read -r conn_id req_vpc acc_vpc req_account acc_account req_cidr acc_cidr req_region acc_region; do
        ((count++))
        echo "$count. Connection ID: $conn_id"
        echo "   Requester: $req_vpc (Account: $req_account, CIDR: $req_cidr, Region: $req_region)"
        echo "   Accepter:  $acc_vpc (Account: $acc_account, CIDR: $acc_cidr, Region: $acc_region)"
        echo ""
    done <<< "$connections"

    echo "💡 To accept a connection:"
    echo "   $0 accept <CONNECTION_ID> --region $REGION"
    echo ""
    echo "💡 To accept and setup routes automatically:"
    echo "   $0 full-setup <CONNECTION_ID> --region $REGION"
}

# Accept a VPC peering connection
accept_connection() {
    local connection_id="$1"

    if [[ -z "$connection_id" ]]; then
        log_error "Connection ID is required"
        exit 1
    fi

    log_info "Accepting VPC peering connection: $connection_id in region: $REGION"

    local result=$(aws ec2 accept-vpc-peering-connection \
        --region "$REGION" \
        --vpc-peering-connection-id "$connection_id" \
        --query 'VpcPeeringConnection.[Status.Code,AccepterVpcInfo.VpcId,RequesterVpcInfo.CidrBlock]' \
        --output text)

    local status=$(echo "$result" | cut -f1)
    local accepter_vpc=$(echo "$result" | cut -f2)
    local requester_cidr=$(echo "$result" | cut -f3)

    if [[ "$status" == "active" ]]; then
        log_success "Successfully accepted VPC peering connection: $connection_id"
        echo "   Status: $status"
        echo ""
        log_warning "NEXT STEP: Add routes to enable bidirectional traffic"
        echo "   Run: $0 add-routes $connection_id --region $REGION"
        echo ""
        echo "   Or use full-setup to do both steps automatically:"
        echo "   $0 full-setup $connection_id --region $REGION"
    else
        log_warning "VPC peering connection accepted but status is: $status"
    fi
}

# Accept all pending connections
accept_all_connections() {
    if [[ "$CONFIRM" != "true" ]]; then
        log_error "This command requires --confirm flag for safety"
        echo "   Use: $0 accept-all --confirm --region $REGION"
        exit 1
    fi

    log_info "Finding pending VPC peering connections..."

    local connections=$(aws ec2 describe-vpc-peering-connections \
        --region "$REGION" \
        --filters "Name=status-code,Values=pending-acceptance" \
        --query 'VpcPeeringConnections[*].VpcPeeringConnectionId' \
        --output text)

    if [[ -z "$connections" ]]; then
        log_success "No pending VPC peering connections found"
        return 0
    fi

    local conn_array=($connections)
    log_warning "About to accept ${#conn_array[@]} pending connection(s):"

    local count=0
    for conn_id in "${conn_array[@]}"; do
        ((count++))
        echo "   $count. $conn_id"
    done
    echo ""

    for conn_id in "${conn_array[@]}"; do
        accept_connection "$conn_id"
        echo ""
    done

    log_success "Successfully processed ${#conn_array[@]} peering connection(s)"
    log_warning "IMPORTANT: Don't forget to add routes for each accepted connection!"
}

# List route tables for a VPC
list_route_tables() {
    local vpc_id="$1"

    if [[ -z "$vpc_id" ]]; then
        log_error "VPC ID is required"
        exit 1
    fi

    log_info "Listing route tables for VPC: $vpc_id"
    echo ""

    local route_tables=$(aws ec2 describe-route-tables \
        --region "$REGION" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'RouteTables[*].[RouteTableId,Associations[?Main==`true`] | length(@),Associations[?SubnetId] | length(@),Tags[?Key==`Name`].Value | [0]]' \
        --output text)

    if [[ -z "$route_tables" ]]; then
        log_warning "No route tables found for VPC $vpc_id"
        return 1
    fi

    echo "Found route table(s):"
    echo ""

    local count=0
    while IFS=$'\t' read -r rt_id is_main subnet_count name; do
        ((count++))
        local rt_type="Custom Route Table"
        if [[ "$is_main" == "1" ]]; then
            rt_type="Main Route Table"
        fi

        echo "$count. Route Table: $rt_id"
        echo "   Type: $rt_type"
        echo "   VPC: $vpc_id"
        echo "   Associated Subnets: $subnet_count"
        if [[ -n "$name" && "$name" != "None" ]]; then
            echo "   Name: $name"
        fi
        echo ""
    done <<< "$route_tables"

    echo "💡 To add routes for peering:"
    echo "   $0 add-routes <CONNECTION_ID> --region $REGION"
}

# Add routes to all route tables in a VPC
add_routes_to_vpc() {
    local vpc_id="$1"
    local destination_cidr="$2"
    local connection_id="$3"

    if [[ -z "$vpc_id" || -z "$destination_cidr" || -z "$connection_id" ]]; then
        log_error "VPC ID, destination CIDR, and connection ID are required"
        exit 1
    fi

    log_info "Adding routes for VPC: $vpc_id"
    echo "   Destination CIDR: $destination_cidr"
    echo "   Peering Connection: $connection_id"

    local route_table_ids=$(aws ec2 describe-route-tables \
        --region "$REGION" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'RouteTables[*].RouteTableId' \
        --output text)

    if [[ -z "$route_table_ids" ]]; then
        log_warning "No route tables found for VPC $vpc_id"
        return 1
    fi

    local rt_array=($route_table_ids)
    echo "   Found ${#rt_array[@]} route table(s)"

    local success_count=0
    local skip_count=0
    local conflict_count=0

    for rt_id in "${rt_array[@]}"; do
        echo "   🔄 Adding route to $rt_id..."

        # First, try to create the route
        if aws ec2 create-route \
            --region "$REGION" \
            --route-table-id "$rt_id" \
            --destination-cidr-block "$destination_cidr" \
            --vpc-peering-connection-id "$connection_id" \
            --output text >/dev/null 2>&1; then
            echo "   ✅ Successfully added route to $rt_id"
            ((success_count++))
        else
            # Route creation failed, check why
            local existing_route=$(aws ec2 describe-route-tables \
                --region "$REGION" \
                --route-table-ids "$rt_id" \
                --query "RouteTables[0].Routes[?DestinationCidrBlock=='$destination_cidr'].[State,VpcPeeringConnectionId,GatewayId,InstanceId,NetworkInterfaceId]" \
                --output text)

            if [[ -n "$existing_route" ]]; then
                local route_state=$(echo "$existing_route" | cut -f1)
                local existing_pcx=$(echo "$existing_route" | cut -f2)
                local gateway_id=$(echo "$existing_route" | cut -f3)
                local instance_id=$(echo "$existing_route" | cut -f4)
                local eni_id=$(echo "$existing_route" | cut -f5)

                # Check if route is blackholed
                if [[ "$route_state" == "blackhole" ]]; then
                    echo "   🔄 Found blackholed route, replacing..."
                    if aws ec2 replace-route \
                        --region "$REGION" \
                        --route-table-id "$rt_id" \
                        --destination-cidr-block "$destination_cidr" \
                        --vpc-peering-connection-id "$connection_id" \
                        --output text >/dev/null 2>&1; then
                        echo "   ✅ Successfully replaced blackholed route in $rt_id"
                        ((success_count++))
                    else
                        echo "   ❌ Failed to replace blackholed route in $rt_id"
                    fi
                # Check if route points to the same peering connection
                elif [[ "$existing_pcx" == "$connection_id" ]]; then
                    echo "   ℹ️  Route already exists and points to correct peering connection in $rt_id"
                    ((skip_count++))
                # Route exists but points to different target
                else
                    echo "   ⚠️  CONFLICT: Route already exists in $rt_id pointing to different target:"
                    if [[ -n "$existing_pcx" && "$existing_pcx" != "None" ]]; then
                        echo "       Existing: Peering Connection $existing_pcx"
                    elif [[ -n "$gateway_id" && "$gateway_id" != "None" ]]; then
                        echo "       Existing: Gateway $gateway_id"
                    elif [[ -n "$instance_id" && "$instance_id" != "None" ]]; then
                        echo "       Existing: Instance $instance_id"
                    elif [[ -n "$eni_id" && "$eni_id" != "None" ]]; then
                        echo "       Existing: Network Interface $eni_id"
                    fi
                    echo "       Requested: Peering Connection $connection_id"
                    echo "       Manual intervention required to resolve conflict"
                    ((conflict_count++))
                fi
            else
                echo "   ❌ Failed to add route to $rt_id (unknown error)"
            fi
        fi
    done

    echo "   📊 VPC $vpc_id: Added $success_count, Existed $skip_count, Conflicts $conflict_count, Total ${#rt_array[@]}"

    # Return non-zero if there were conflicts
    if [[ $conflict_count -gt 0 ]]; then
        return 1
    fi
    return 0
}

# Add routes to both VPCs for bidirectional communication
add_routes_for_peering() {
    local connection_id="$1"

    if [[ -z "$connection_id" ]]; then
        log_error "Connection ID is required"
        exit 1
    fi

    log_info "Adding bidirectional routes for peering connection: $connection_id"
    echo ""

    # Get peering connection details
    local connection_info=$(aws ec2 describe-vpc-peering-connections \
        --region "$REGION" \
        --vpc-peering-connection-ids "$connection_id" \
        --query 'VpcPeeringConnections[0].[Status.Code,RequesterVpcInfo.VpcId,AccepterVpcInfo.VpcId,RequesterVpcInfo.CidrBlock,AccepterVpcInfo.CidrBlock,RequesterVpcInfo.Region,AccepterVpcInfo.Region]' \
        --output text)

    if [[ -z "$connection_info" ]]; then
        log_error "Peering connection $connection_id not found"
        exit 1
    fi

    local status=$(echo "$connection_info" | cut -f1)
    local requester_vpc=$(echo "$connection_info" | cut -f2)
    local accepter_vpc=$(echo "$connection_info" | cut -f3)
    local requester_cidr=$(echo "$connection_info" | cut -f4)
    local accepter_cidr=$(echo "$connection_info" | cut -f5)
    local requester_region=$(echo "$connection_info" | cut -f6)
    local accepter_region=$(echo "$connection_info" | cut -f7)

    if [[ "$status" != "active" ]]; then
        log_error "Peering connection is not active. Status: $status"
        log_warning "Accept the connection first: $0 accept $connection_id --region $REGION"
        exit 1
    fi

    echo "Connection Details:"
    echo "   Status: $status"
    echo "   Requester VPC: $requester_vpc (CIDR: $requester_cidr, Region: $requester_region)"
    echo "   Accepter VPC: $accepter_vpc (CIDR: $accepter_cidr, Region: $accepter_region)"
    echo ""

    local accepter_success=true
    local requester_success=true

    # Add routes to accepter VPC (pointing to requester CIDR)
    log_info "Step 1: Adding routes to accepter VPC ($accepter_vpc)"
    if ! add_routes_to_vpc "$accepter_vpc" "$requester_cidr" "$connection_id"; then
        accepter_success=false
        log_warning "Some routes in accepter VPC had conflicts"
    fi
    echo ""

    # Add routes to requester VPC (pointing to accepter CIDR) if in same region
    if [[ "$requester_region" == "$REGION" ]]; then
        log_info "Step 2: Adding routes to requester VPC ($requester_vpc)"
        if ! add_routes_to_vpc "$requester_vpc" "$accepter_cidr" "$connection_id"; then
            requester_success=false
            log_warning "Some routes in requester VPC had conflicts"
        fi
        echo ""
    else
        log_warning "Step 2: Skipping requester VPC routes (different region: $requester_region)"
        echo "   To add routes to requester VPC, run this command in region $requester_region:"
        echo "   $0 add-routes $connection_id --region $requester_region"
        echo ""
        echo "   Or use this AWS CLI command directly:"
        echo "   aws ec2 describe-route-tables --region $requester_region --filters \"Name=vpc-id,Values=$requester_vpc\" --query 'RouteTables[*].RouteTableId' --output text | xargs -I {} aws ec2 create-route --region $requester_region --route-table-id {} --destination-cidr-block $accepter_cidr --vpc-peering-connection-id $connection_id"
        echo ""
    fi

    # Provide summary and guidance
    if [[ "$accepter_success" == "true" && "$requester_success" == "true" ]]; then
        log_success "Bidirectional route setup completed successfully!"
    else
        log_warning "Bidirectional route setup completed with conflicts!"
        echo ""
        echo "💡 To resolve route conflicts:"
        echo "   1. Review the conflicting routes shown above"
        echo "   2. Determine if the existing routes are still needed"
        echo "   3. If not needed, delete them manually:"
        echo "      aws ec2 delete-route --route-table-id <RT_ID> --destination-cidr-block <CIDR>"
        echo "   4. Then re-run this command to add the peering routes"
        echo ""
        echo "💡 To check current routes:"
        echo "   $0 list-routes $accepter_vpc --region $REGION"
        if [[ "$requester_region" == "$REGION" ]]; then
            echo "   $0 list-routes $requester_vpc --region $REGION"
        fi
    fi
    echo ""
    echo "💡 Test connectivity between VPCs:"
    echo "   - From accepter VPC ($accepter_vpc) to requester CIDR ($requester_cidr)"
    if [[ "$requester_region" == "$REGION" ]]; then
        echo "   - From requester VPC ($requester_vpc) to accepter CIDR ($accepter_cidr)"
    fi
    echo ""
    echo "💡 Verify security groups allow the required traffic"
}

# Remove routes from all route tables in a VPC
remove_routes_from_vpc() {
    local vpc_id="$1"
    local destination_cidr="$2"

    if [[ -z "$vpc_id" || -z "$destination_cidr" ]]; then
        log_error "VPC ID and destination CIDR are required"
        exit 1
    fi

    log_info "Removing routes for VPC: $vpc_id"
    echo "   Destination CIDR: $destination_cidr"

    local route_table_ids=$(aws ec2 describe-route-tables \
        --region "$REGION" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'RouteTables[*].RouteTableId' \
        --output text)

    if [[ -z "$route_table_ids" ]]; then
        log_warning "No route tables found for VPC $vpc_id"
        return 1
    fi

    local rt_array=($route_table_ids)
    echo "   Found ${#rt_array[@]} route table(s)"

    local success_count=0
    local skip_count=0

    for rt_id in "${rt_array[@]}"; do
        echo "   🔄 Removing route from $rt_id..."

        if aws ec2 delete-route \
            --region "$REGION" \
            --route-table-id "$rt_id" \
            --destination-cidr-block "$destination_cidr" \
            --output text >/dev/null 2>&1; then
            echo "   ✅ Successfully removed route from $rt_id"
            ((success_count++))
        else
            echo "   ℹ️  Route not found in $rt_id"
            ((skip_count++))
        fi
    done

    echo "   📊 VPC $vpc_id: Removed $success_count, Not found $skip_count, Total ${#rt_array[@]}"
    return 0
}

# Remove routes from both VPCs for peering connection
remove_routes_for_peering() {
    local connection_id="$1"

    if [[ -z "$connection_id" ]]; then
        log_error "Connection ID is required"
        exit 1
    fi

    log_info "Removing bidirectional routes for peering connection: $connection_id"
    echo ""

    # Get peering connection details
    local connection_info=$(aws ec2 describe-vpc-peering-connections \
        --region "$REGION" \
        --vpc-peering-connection-ids "$connection_id" \
        --query 'VpcPeeringConnections[0].[Status.Code,RequesterVpcInfo.VpcId,AccepterVpcInfo.VpcId,RequesterVpcInfo.CidrBlock,AccepterVpcInfo.CidrBlock,RequesterVpcInfo.Region,AccepterVpcInfo.Region]' \
        --output text)

    if [[ -z "$connection_info" ]]; then
        log_error "Peering connection $connection_id not found"
        exit 1
    fi

    local status=$(echo "$connection_info" | cut -f1)
    local requester_vpc=$(echo "$connection_info" | cut -f2)
    local accepter_vpc=$(echo "$connection_info" | cut -f3)
    local requester_cidr=$(echo "$connection_info" | cut -f4)
    local accepter_cidr=$(echo "$connection_info" | cut -f5)
    local requester_region=$(echo "$connection_info" | cut -f6)
    local accepter_region=$(echo "$connection_info" | cut -f7)

    echo "Connection Details:"
    echo "   Status: $status"
    echo "   Requester VPC: $requester_vpc (CIDR: $requester_cidr, Region: $requester_region)"
    echo "   Accepter VPC: $accepter_vpc (CIDR: $accepter_cidr, Region: $accepter_region)"
    echo ""

    # Remove routes from accepter VPC (pointing to requester CIDR)
    log_info "Step 1: Removing routes from accepter VPC ($accepter_vpc)"
    if remove_routes_from_vpc "$accepter_vpc" "$requester_cidr"; then
        echo ""
    fi

    # Remove routes from requester VPC (pointing to accepter CIDR) if in same region
    if [[ "$requester_region" == "$REGION" ]]; then
        log_info "Step 2: Removing routes from requester VPC ($requester_vpc)"
        if remove_routes_from_vpc "$requester_vpc" "$accepter_cidr"; then
            echo ""
        fi
    else
        log_warning "Step 2: Skipping requester VPC routes (different region: $requester_region)"
        echo "   To remove routes from requester VPC, run:"
        echo "   $0 remove-routes $connection_id --region $requester_region"
        echo ""
    fi

    log_success "Bidirectional route removal completed!"
}

# Full setup: accept connection and add routes automatically
full_setup() {
    local connection_id="$1"

    if [[ -z "$connection_id" ]]; then
        log_error "Connection ID is required"
        exit 1
    fi

    log_info "Starting full setup for connection: $connection_id"
    echo ""

    # Step 1: Accept the connection
    log_info "Step 1: Accepting peering connection..."
    local result=$(aws ec2 accept-vpc-peering-connection \
        --region "$REGION" \
        --vpc-peering-connection-id "$connection_id" \
        --query 'VpcPeeringConnection.[Status.Code,AccepterVpcInfo.VpcId,RequesterVpcInfo.CidrBlock]' \
        --output text)

    local status=$(echo "$result" | cut -f1)
    local accepter_vpc=$(echo "$result" | cut -f2)
    local requester_cidr=$(echo "$result" | cut -f3)

    if [[ "$status" != "active" ]]; then
        log_error "Failed to accept connection or connection not active. Status: $status"
        exit 1
    fi

    log_success "Connection accepted successfully"
    echo ""

    # Step 2: Add bidirectional routes
    log_info "Step 2: Adding bidirectional routes..."
    add_routes_for_peering "$connection_id"

    echo ""
    log_success "Full setup completed successfully!"
    echo "   Connection: $connection_id"
    echo "   Status: $status"
    echo "   Bidirectional routes configured"
}

# Main script logic
main() {
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    # Parse arguments manually to handle both short and long options properly
    local positional_args=()

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -r|--region)
                if [[ -n "$2" && "$2" != -* ]]; then
                    REGION="$2"
                    shift 2
                else
                    log_error "Option $1 requires an argument"
                    show_usage
                    exit 1
                fi
                ;;
            --region=*)
                REGION="${1#*=}"
                shift
                ;;
            -c|--confirm)
                CONFIRM=true
                shift
                ;;
            -*)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                # Collect positional arguments
                positional_args+=("$1")
                shift
                ;;
        esac
    done

    # Restore positional arguments
    set -- "${positional_args[@]}"

    # Now $@ contains only the positional arguments (command and its parameters)
    if [[ $# -eq 0 ]]; then
        log_error "No command specified"
        show_usage
        exit 1
    fi

    local command="$1"
    shift

    case "$command" in
        list-pending)
            list_pending_connections
            ;;
        accept)
            if [[ $# -eq 0 ]]; then
                log_error "Connection ID is required for accept command"
                exit 1
            fi
            accept_connection "$1"
            ;;
        accept-all)
            accept_all_connections
            ;;
        list-routes)
            if [[ $# -eq 0 ]]; then
                log_error "VPC ID is required for list-routes command"
                exit 1
            fi
            list_route_tables "$1"
            ;;
        add-routes)
            if [[ $# -eq 0 ]]; then
                log_error "Connection ID is required for add-routes command"
                exit 1
            fi
            add_routes_for_peering "$1"
            ;;
        remove-routes)
            if [[ $# -eq 0 ]]; then
                log_error "Connection ID is required for remove-routes command"
                exit 1
            fi
            remove_routes_for_peering "$1"
            ;;
        full-setup)
            if [[ $# -eq 0 ]]; then
                log_error "Connection ID is required for full-setup command"
                exit 1
            fi
            full_setup "$1"
            ;;
        -h|--help)
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    log_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Run main function
main "$@"
