#!/usr/bin/env npx ts-node

/**
 * EFS File System Provisioning Script
 *
 * Creates EFS file systems for a given cluster and outputs the file system IDs.
 * This script should be run after EKS cluster creation but before deploying the EFS CSI driver.
 *
 * Usage: npx ts-node setup-efs-file-systems.ts <cluster-name>
 * Example: npx ts-node setup-efs-file-systems.ts treebo-tools-common
 */

import * as fs from "node:fs";
import * as path from "node:path";
import {
  AuthorizeSecurityGroupEgressCommand,
  AuthorizeSecurityGroupIngressCommand,
  CreateSecurityGroupCommand,
  DescribeSecurityGroupsCommand,
  DescribeSubnetsCommand,
  DescribeVpcsCommand,
  EC2Client,
} from "@aws-sdk/client-ec2";
import {
  CreateFileSystemCommand,
  CreateMountTargetCommand,
  DescribeFileSystemsCommand,
  DescribeMountTargetsCommand,
  EFSClient,
} from "@aws-sdk/client-efs";
import { GetCallerIdentityCommand, STSClient } from "@aws-sdk/client-sts";

interface EFSConfig {
  alias: string;
  name: string;
  performanceMode: "generalPurpose" | "maxIO";
  throughputMode: "provisioned" | "bursting";
  provisionedThroughput?: number;
  description: string;
}

class EFSProvisioner {
  private efsClient: EFSClient;
  private ec2Client: EC2Client;
  private stsClient: STSClient;
  private clusterName: string;
  private region: string;
  private accountId = "";

  constructor(clusterName: string, region = "ap-south-1") {
    this.clusterName = clusterName;
    this.region = region;
    this.efsClient = new EFSClient({ region });
    this.ec2Client = new EC2Client({ region });
    this.stsClient = new STSClient({ region });
  }

  async provision(): Promise<Record<string, string>> {
    console.log(`🚀 Provisioning EFS file systems for cluster: ${this.clusterName}`);

    // Get AWS account ID
    const identity = await this.stsClient.send(new GetCallerIdentityCommand({}));
    this.accountId = identity.Account!;
    console.log(`📋 AWS Account ID: ${this.accountId}`);

    // Find VPC for the cluster
    const vpcId = await this.findClusterVpc();
    console.log(`🌐 Using VPC: ${vpcId}`);

    // Create EFS file systems
    const fileSystems = await this.createFileSystems();
    console.log("📁 EFS File Systems created:");
    for (const [alias, fsId] of Object.entries(fileSystems)) {
      console.log(`  ${alias}: ${fsId}`);
    }

    // Wait for file systems to become available
    await this.waitForFileSystemsAvailable(fileSystems);

    // Create security group for EFS
    const securityGroupId = await this.createOrGetSecurityGroup(vpcId);
    console.log(`🔒 Security Group: ${securityGroupId}`);

    // Create mount targets for all file systems
    for (const [alias, fileSystemId] of Object.entries(fileSystems)) {
      await this.createMountTargets(fileSystemId, vpcId, securityGroupId);
      console.log(`🔗 Mount targets created for ${alias}`);
    }

    console.log("✅ EFS provisioning completed successfully!");
    console.log("\nFile System IDs:");
    for (const [alias, fsId] of Object.entries(fileSystems)) {
      console.log(`${alias}: ${fsId}`);
    }

    return fileSystems;
  }

  private async findClusterVpc(): Promise<string> {
    // Find VPC by cluster name tag
    const response = await this.ec2Client.send(
      new DescribeVpcsCommand({
        Filters: [
          {
            Name: "tag:Name",
            Values: [`vpc-${this.clusterName}`],
          },
        ],
      })
    );

    if (!response.Vpcs || response.Vpcs.length === 0) {
      throw new Error(`VPC not found for cluster: ${this.clusterName}`);
    }

    return response.Vpcs[0]?.VpcId!;
  }

  private async createFileSystems(): Promise<Record<string, string>> {
    const fileSystems: Record<string, string> = {};

    // Define EFS configurations for different use cases
    const efsConfigs: EFSConfig[] = [
      {
        alias: "shared",
        name: `efs-${this.clusterName}-shared`,
        performanceMode: "generalPurpose",
        throughputMode: "bursting",
        description: "Shared EFS for general application storage",
      },
      {
        alias: "high-performance",
        name: `efs-${this.clusterName}-performance`,
        performanceMode: "generalPurpose",
        throughputMode: "provisioned",
        provisionedThroughput: 100,
        description: "High-performance EFS for intensive workloads",
      },
    ];

    for (const config of efsConfigs) {
      // Check if file system already exists
      try {
        const response = await this.efsClient.send(
          new DescribeFileSystemsCommand({
            CreationToken: config.name,
          })
        );

        if (response.FileSystems && response.FileSystems.length > 0) {
          fileSystems[config.alias] = response.FileSystems[0]?.FileSystemId!;
          console.log(
            `📁 Using existing ${config.alias} EFS: ${response.FileSystems[0]?.FileSystemId}`
          );
          continue;
        }
      } catch {
        // File system doesn't exist, create it
      }

      // Create new file system
      const createParams: any = {
        CreationToken: config.name,
        PerformanceMode: config.performanceMode,
        ThroughputMode: config.throughputMode,
        Encrypted: true,
        Tags: [
          { Key: "Name", Value: config.name },
          { Key: "Alias", Value: config.alias },
          { Key: "Cluster", Value: this.clusterName },
          { Key: "Description", Value: config.description },
          { Key: "ManagedBy", Value: "efs-provisioning-script" },
        ],
      };

      if (config.throughputMode === "provisioned" && config.provisionedThroughput) {
        createParams.ProvisionedThroughputInMibps = config.provisionedThroughput;
      }

      const response = await this.efsClient.send(new CreateFileSystemCommand(createParams));
      fileSystems[config.alias] = response.FileSystemId!;
      console.log(`📁 Created ${config.alias} EFS: ${response.FileSystemId}`);
    }

    return fileSystems;
  }

  private async waitForFileSystemsAvailable(fileSystems: Record<string, string>): Promise<void> {
    console.log("⏳ Waiting for EFS file systems to become available...");

    const fileSystemIds = Object.values(fileSystems);
    const maxWaitTime = 300; // 5 minutes
    const pollInterval = 10; // 10 seconds
    let elapsed = 0;

    while (elapsed < maxWaitTime) {
      try {
        const response = await this.efsClient.send(new DescribeFileSystemsCommand({}));

        // Filter for our specific file systems
        const ourFileSystems = response.FileSystems?.filter(fs =>
          fileSystemIds.includes(fs.FileSystemId!)
        );

        const allAvailable = ourFileSystems?.every(fs => fs.LifeCycleState === "available");

        if (allAvailable) {
          console.log("✅ All EFS file systems are now available");
          return;
        }

        const states = ourFileSystems
          ?.map(fs => `${fs.FileSystemId}: ${fs.LifeCycleState}`)
          .join(", ");
        console.log(`⏳ File system states: ${states}`);

        await new Promise(resolve => setTimeout(resolve, pollInterval * 1000));
        elapsed += pollInterval;
      } catch (error) {
        console.error("Error checking file system status:", error);
        throw error;
      }
    }

    throw new Error(
      `Timeout waiting for EFS file systems to become available after ${maxWaitTime} seconds`
    );
  }

  private async createOrGetSecurityGroup(vpcId: string): Promise<string> {
    const sgName = `sec-grp-efs-${this.clusterName}`;

    // Check if security group already exists
    try {
      const response = await this.ec2Client.send(
        new DescribeSecurityGroupsCommand({
          Filters: [
            { Name: "group-name", Values: [sgName] },
            { Name: "vpc-id", Values: [vpcId] },
          ],
        })
      );

      if (response.SecurityGroups && response.SecurityGroups.length > 0) {
        const securityGroup = response.SecurityGroups[0];
        if (securityGroup?.GroupId) {
          console.log(`🔒 Using existing security group: ${securityGroup.GroupId}`);
          // Ensure the existing security group has the right rules
          await this.addEfsSecurityGroupRules(securityGroup.GroupId, vpcId);
          await this.addNodeSecurityGroupRules(securityGroup.GroupId, vpcId);
          return securityGroup.GroupId;
        }
      }
    } catch {
      // Security group doesn't exist, create it
    }

    // Create security group
    const createResponse = await this.ec2Client.send(
      new CreateSecurityGroupCommand({
        GroupName: sgName,
        Description: `EFS security group for ${this.clusterName}`,
        VpcId: vpcId,
        TagSpecifications: [
          {
            ResourceType: "security-group",
            Tags: [
              { Key: "Name", Value: sgName },
              { Key: "Cluster", Value: this.clusterName },
            ],
          },
        ],
      })
    );

    const securityGroupId = createResponse.GroupId!;

    // Add NFS inbound rules
    await this.addEfsSecurityGroupRules(securityGroupId, vpcId);

    // Add outbound rules to node security groups
    await this.addNodeSecurityGroupRules(securityGroupId, vpcId);

    console.log(`🔒 Created security group: ${securityGroupId}`);
    return securityGroupId;
  }

  private async addEfsSecurityGroupRules(securityGroupId: string, vpcId: string): Promise<void> {
    // Find Kubernetes node security groups
    const nodeSecurityGroups = await this.findNodeSecurityGroups(vpcId);

    const ipPermissions = [
      // Self-referencing rule for EFS-to-EFS communication
      {
        IpProtocol: "tcp",
        FromPort: 2049,
        ToPort: 2049,
        UserIdGroupPairs: [{ GroupId: securityGroupId }],
      },
      // Allow from VPC CIDR (covers all nodes)
      {
        IpProtocol: "tcp",
        FromPort: 2049,
        ToPort: 2049,
        IpRanges: [{ CidrIp: await this.getVpcCidr(vpcId) }],
      },
    ];

    // Add rules for specific node security groups if found
    for (const nodeSecurityGroup of nodeSecurityGroups) {
      ipPermissions.push({
        IpProtocol: "tcp",
        FromPort: 2049,
        ToPort: 2049,
        UserIdGroupPairs: [{ GroupId: nodeSecurityGroup }],
      });
    }

    try {
      await this.ec2Client.send(
        new AuthorizeSecurityGroupIngressCommand({
          GroupId: securityGroupId,
          IpPermissions: ipPermissions,
        })
      );
      console.log(`🔒 Added NFS rules for ${nodeSecurityGroups.length + 1} sources`);
    } catch (error) {
      if ((error as Error).name !== "InvalidPermission.Duplicate") {
        throw error;
      }
      console.log("🔒 NFS rules already exist");
    }
  }

  private async findNodeSecurityGroups(vpcId: string): Promise<string[]> {
    try {
      const response = await this.ec2Client.send(
        new DescribeSecurityGroupsCommand({
          Filters: [
            { Name: "vpc-id", Values: [vpcId] },
            { Name: `tag:kubernetes.io/cluster/${this.clusterName}`, Values: ["owned"] },
          ],
        })
      );

      return response.SecurityGroups?.map(sg => sg.GroupId!).filter(Boolean) || [];
    } catch (error) {
      console.log(`⚠️  Could not find node security groups: ${error}`);
      return [];
    }
  }

  private async getVpcCidr(vpcId: string): Promise<string> {
    const response = await this.ec2Client.send(
      new DescribeVpcsCommand({
        VpcIds: [vpcId],
      })
    );

    return response.Vpcs?.[0]?.CidrBlock || "10.0.0.0/8";
  }

  private async addNodeSecurityGroupRules(
    efsSecurityGroupId: string,
    vpcId: string
  ): Promise<void> {
    const nodeSecurityGroups = await this.findNodeSecurityGroups(vpcId);

    if (nodeSecurityGroups.length === 0) {
      console.log("⚠️  No node security groups found, skipping outbound rules");
      return;
    }

    for (const nodeSecurityGroupId of nodeSecurityGroups) {
      try {
        // Add outbound rule to allow NFS traffic to EFS
        await this.ec2Client.send(
          new AuthorizeSecurityGroupEgressCommand({
            GroupId: nodeSecurityGroupId,
            IpPermissions: [
              {
                IpProtocol: "tcp",
                FromPort: 2049,
                ToPort: 2049,
                UserIdGroupPairs: [{ GroupId: efsSecurityGroupId }],
              },
            ],
          })
        );
        console.log(`🔓 Added outbound NFS rule to node security group: ${nodeSecurityGroupId}`);
      } catch (error) {
        if ((error as Error).name !== "InvalidPermission.Duplicate") {
          console.log(`⚠️  Could not add outbound rule to ${nodeSecurityGroupId}: ${error}`);
        } else {
          console.log(`🔓 Outbound NFS rule already exists for: ${nodeSecurityGroupId}`);
        }
      }
    }
  }

  private async createMountTargets(
    fileSystemId: string,
    vpcId: string,
    securityGroupId: string
  ): Promise<void> {
    // Get private subnets
    const subnetsResponse = await this.ec2Client.send(
      new DescribeSubnetsCommand({
        Filters: [
          { Name: "vpc-id", Values: [vpcId] },
          { Name: "tag:Name", Values: ["*private*"] },
        ],
      })
    );

    if (!subnetsResponse.Subnets || subnetsResponse.Subnets.length === 0) {
      throw new Error("No private subnets found for EFS mount targets");
    }

    // Check existing mount targets
    const mountTargetsResponse = await this.efsClient.send(
      new DescribeMountTargetsCommand({
        FileSystemId: fileSystemId,
      })
    );

    const existingSubnets = new Set(
      mountTargetsResponse.MountTargets?.map((mt: { SubnetId?: string }) => mt.SubnetId) || []
    );

    // Create mount targets for each private subnet
    for (const subnet of subnetsResponse.Subnets) {
      if (!existingSubnets.has(subnet.SubnetId!)) {
        try {
          await this.efsClient.send(
            new CreateMountTargetCommand({
              FileSystemId: fileSystemId,
              SubnetId: subnet.SubnetId!,
              SecurityGroups: [securityGroupId],
            })
          );
          console.log(`🔗 Created mount target in subnet: ${subnet.SubnetId}`);
        } catch (error) {
          if ((error as Error).name !== "MountTargetConflict") {
            throw error;
          }
          console.log(`🔗 Mount target already exists in subnet: ${subnet.SubnetId}`);
        }
      }
    }
  }
}

// Main execution
async function main() {
  const clusterName = process.argv[2];

  if (!clusterName) {
    console.error("Usage: npx ts-node setup-efs-file-systems.ts <cluster-name>");
    console.error("Example: npx ts-node setup-efs-file-systems.ts treebo-tools-common");
    process.exit(1);
  }

  const region = process.env.AWS_DEFAULT_REGION || "ap-south-1";

  try {
    const provisioner = new EFSProvisioner(clusterName, region);
    const fileSystems = await provisioner.provision();

    console.log("\n🎯 Summary:");
    console.log("File System IDs for cluster configuration:");
    for (const [alias, fsId] of Object.entries(fileSystems)) {
      console.log(`  ${alias}: ${fsId}`);
    }

    console.log("\nNext steps:");
    console.log(
      `1. Deploy EFS CSI driver: kubectl apply -k tools/efs-csi-driver/overlays/${clusterName}`
    );
    console.log("2. EFS file systems are now ready for use in TreeboService applications");
  } catch (error) {
    console.error("❌ EFS provisioning failed:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
