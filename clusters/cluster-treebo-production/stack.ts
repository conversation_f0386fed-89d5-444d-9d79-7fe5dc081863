import * as cdk from "aws-cdk-lib";
import { createClusterContext } from "../lib/helpers/cluster-context";
import { AWS_MUMBAI_REGION, AWS_ROOT_ACCOUNT_ID, VPN_VPC_NAME } from "../lib/helpers/constants";
import { fetchVpcAttributesByName } from "../lib/helpers/vpc-helper";
import { TreeboBaseStack } from "../lib/stacks/base-eks-stack";

async function main() {
  const commonPrefix = "treebo-production";

  const commonTags = {};

  // Fetch VPN VPC info for peering
  const vpnVpInfo = await fetchVpcAttributesByName(VPN_VPC_NAME, AWS_MUMBAI_REGION);

  const context = createClusterContext({
    commonPrefix,
    region: AWS_MUMBAI_REGION,
    accountId: AWS_ROOT_ACCOUNT_ID,
    environment: "production",
    tags: commonTags,
    vpcCidrBlock: "10.100.0.0/16",
    vpnVpc: vpnVpInfo,
  });

  const app = new cdk.App();

  new TreeboBaseStack(app, `stack-${commonPrefix}`, context);
}

main();
