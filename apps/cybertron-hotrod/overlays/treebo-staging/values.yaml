serviceName: cybertron-hotrod
namespace:
  name: cybertron-hotrod-staging

services:
- name: cybertron-hotrod
  type: app-server
  port: 1337
  workloadCriticality: application-semi-critical
  image:
    registry: ************.dkr.ecr.ap-south-1.amazonaws.com
    repository: hotrod
    tag: master-direct-test-5b6f38b426-s-5498-aps1-01
  command: ["/bin/sh", "-c", "npm run staging:start"]
  env:
  - name: CONFIG
    value: config.staging.json
  - name: DEBUG
    value: ${DEBUG:-cybertron:*}
  envFrom:
  - secretRef:
      name: cybertron-hotrod-secrets
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "1000m"
  livenessProbe:
    httpGet:
      path: /health/
      port: 1337
    initialDelaySeconds: 30
    periodSeconds: 30
    timeoutSeconds: 15
    failureThreshold: 10
  readinessProbe:
    httpGet:
      path: /health/
      port: 1337
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 5
  volumes:
  - name: logs
    type: emptyDir
    emptyDir: {}
  volumeMounts:
  - name: logs
    mountPath: /var/log/cybertron/cybertron-hotrod

- name: nginx
  type: nginx
  port: 80
  targetPort: 80
  image:
    repository: nginx
    tag: 1.23.4-alpine
    pullPolicy: IfNotPresent
  volumes:
    - name: nginx-config
      type: configMap
      configMap:
        name: nginx-config
    - name: nginx-cache
      type: emptyDir
      emptyDir: {}
    - name: nginx-run
      type: emptyDir
      emptyDir: {}
    - name: logs
      type: emptyDir
      emptyDir: {}
  volumeMounts:
    - name: nginx-config
      mountPath: /etc/nginx/nginx.conf
      subPath: nginx.conf
    - name: nginx-config
      mountPath: /etc/nginx/sites-available/
    - name: nginx-cache
      mountPath: /var/cache/nginx
    - name: nginx-run
      mountPath: /var/run
    - name: logs
      mountPath: /var/log/nginx
  livenessProbe:
    httpGet:
      path: /healthz
      port: 80
      httpHeaders:
      - name: Host
        value: localhost
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /healthz
      port: 80
      httpHeaders:
      - name: Host
        value: localhost
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
  command: ["nginx", "-g", "daemon off;"]
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "500m"

externalSecrets:
- awsSecretPath: /staging/cybertron-hotrod
  refreshInterval: "2h"
  secretStore: aws-parameters-store
  secretName: cybertron-hotrod-secrets
envFrom:
- secretRef:
    name: cybertron-hotrod-secrets

ingress:
- name: cybertron-hotrod-ingress
  host: cybertron-hotrod.eksdose7983.treebo.com
  ingressClassName: nginx
  annotations:
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/proxy-body-size: 10m
  paths:
  - path: /
    pathType: Prefix
    serviceName: cybertron-hotrod
    servicePort: 1337

serviceAccounts:
- name: cybertron-hotrod-sa
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/CybertronServiceAccountRole-tools
  automountServiceAccountToken: true
