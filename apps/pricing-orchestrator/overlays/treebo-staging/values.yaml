# Required fields for TreeboService Helm chart
serviceName: pricing-orchestrator
environment: staging

namespace:
  create: true
  name: pricing-orchestrator

services:
  - name: app-server
    type: backend
    port: 8000
    workloadCriticality: application-semi-critical
    image:
      registry: 605536185498.dkr.ecr.ap-south-1.amazonaws.com
      repository: pricing-orchestrator
      tag: 5407aee2
    command:
      ["/usr/src/app/scripts/gunicorn_start"]
      # livenessProbe:
      #   httpGet:
      #     path: /pricing-orchestrator/api/health
      #     port: 8000
      #   initialDelaySeconds: 3

    volumes:
      - name: logs
        type: pvc
        pvc:
          size: 5Gi # ignored in efs
          storageClass: efs-sc # Uses shared EFS for logs
        mountPath: /var/log/pricing-orchestrator

externalSecrets:
  - awsSecretPath: staging/pricing-orchestrator
    refreshInterval: "2h"
    secretStore: aws-secrets-store
    secretName: pricing-orchestrator-secrets

env:
  - name: ENV
    value: staging
  - name: LOG_ROOT
    value: "/var/log/pricing-orchestrator/"
  - name: FLASK_APP
    value: autoapp.py
  - name: APP_ENV
    value: staging
  - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
    value: http://opentelemetry-collector.monitoring.svc.cluster.local:4317
  - name: OTEL_LOGS_EXPORTER
    value: None
  - name: OTEL_SERVICE_NAME
    value: pricing-orchestrator-staging

envFrom:
  - secretRef:
      name: pricing-orchestrator-secrets

ingress:
  - name: pricing-orchestrator-ingress
    host: po.eksdose7983.treebo.com
    ingressClassName: nginx
    paths:
      - path: /
        pathType: Prefix
        serviceName: app-server
        servicePort: 8000
