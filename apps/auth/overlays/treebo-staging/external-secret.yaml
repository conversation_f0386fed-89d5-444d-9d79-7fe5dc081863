apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: auth-env
  namespace: auth-staging
  labels:
    argocd.argoproj.io/instance: auth-staging
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/sync-options: SkipDiff
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: auth-env
    creationPolicy: Orphan
    deletionPolicy: Retain
  data:
    - secretKey: AUTH_BASE_URL
      remoteRef:
        key: staging/auth
        property: AUTH_BASE_URL
    - secretKey: RABBITMQ_HOST
      remoteRef:
        key: staging/auth
        property: RABBITMQ_HOST
    - secretKey: DB
      remoteRef:
        key: staging/auth
        property: DB
