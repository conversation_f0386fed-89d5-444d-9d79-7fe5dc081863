apiVersion: v1
kind: ServiceAccount
metadata:
  name: external-secrets-sa
  namespace: auth-staging
  labels:
    argocd.argoproj.io/instance: auth-staging
    environment: staging
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/external-secrets-sa
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: external-secrets-sa
  namespace: auth-staging
  labels:
    argocd.argoproj.io/instance: auth-staging
    environment: staging
rules:
  - apiGroups: ["secrets-store.csi.x-k8s.io"]
    resources: ["*"]
    verbs: ["get", "list", "watch", "create"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list", "watch", "create"]
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: external-secrets-sa
  namespace: auth-staging
  labels:
    argocd.argoproj.io/instance: auth-staging
    environment: staging
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: external-secrets-sa
subjects:
  - kind: ServiceAccount
    name: external-secrets-sa
    namespace: auth-staging
