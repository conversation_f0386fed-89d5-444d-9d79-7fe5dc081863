apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-app-server
spec:
  selector:
    matchLabels:
      app: auth-app-server
      app.kubernetes.io/name: auth
      app.kubernetes.io/part-of: auth
      environment: staging
  template:
    metadata:
      labels:
        app: auth-app-server
        app.kubernetes.io/name: auth
        app.kubernetes.io/part-of: auth
        environment: staging
    spec:
      serviceAccountName: external-secrets-sa
      containers:
        - name: app-server
          image: ************.dkr.ecr.ap-south-1.amazonaws.com/auth:staging
          command: ["/usr/src/app/scripts/gunicorn_start"]
          imagePullPolicy: Always
          env:
            - name: LOG_ROOT
              value: "/var/log/auth/"
            - name: GUNICORN_ERRORLOG
              value: "/var/log/"
            - name: GUNICORN_ACCESSLOG
              value: "/var/log/"
            - name: APP_ENV
              valueFrom:
                configMapKeyRef:
                  name: auth-app-env
                  key: APP_ENV
            - name: DEBUG
              valueFrom:
                configMapKeyRef:
                  name: auth-app-env
                  key: DEBUG
            - name: TESTING
              valueFrom:
                configMapKeyRef:
                  name: auth-app-env
                  key: TESTING
            - name: SQLALCHEMY_ECHO
              valueFrom:
                configMapKeyRef:
                  name: auth-app-env
                  key: SQLALCHEMY_ECHO
            - name: SQLALCHEMY_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: auth-app-env
                  key: SQLALCHEMY_POOL_SIZE
            - name: SQLALCHEMY_MAX_OVERFLOW
              valueFrom:
                configMapKeyRef:
                  name: auth-app-env
                  key: SQLALCHEMY_MAX_OVERFLOW
            - name: AUTH_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: auth-env
                  key: AUTH_BASE_URL
            - name: RABBITMQ_HOST
              valueFrom:
                secretKeyRef:
                  name: auth-env
                  key: RABBITMQ_HOST
            - name: DB
              valueFrom:
                secretKeyRef:
                  name: auth-env
                  key: DB
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: "500m"
              memory: "512Mi"
            requests:
              cpu: "250m"
              memory: "256Mi"
          livenessProbe:
            httpGet:
              path: /auth/api/health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 20
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /auth/api/health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 20
            failureThreshold: 3
      imagePullSecrets:
        - name: ecr-registry-secret
      volumes:
        - name: log-volume
          emptyDir: {}
