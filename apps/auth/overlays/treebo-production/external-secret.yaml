apiVersion: external-secrets.io/v1alpha1
kind: ExternalSecret
metadata:
  name: auth-env
  namespace: auth-prod
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-ssm-parameterstore
    kind: SecretStore
  target:
    name: auth-env
    creationPolicy: Owner
  data:
    - secretKey: RR_DOMAIN
      remoteRef:
        key: /prod/auth/RR_DOMAIN
    - secretKey: DISCOUNT_DOMAIN
      remoteRef:
        key: /prod/auth/DISCOUNT_DOMAIN
    - secretKey: TAX_DOMAIN
      remoteRef:
        key: /prod/auth/TAX_DOMAIN
    - secretKey: CATALOG_DOMAIN
      remoteRef:
        key: /prod/auth/CATALOG_DOMAIN
---
apiVersion: external-secrets.io/v1alpha1
kind: ExternalSecret
metadata:
  name: auth-slack
  namespace: auth-prod
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secretsmanager
    kind: SecretStore
  target:
    name: auth-slack
    creationPolicy: Owner
  data:
    - secretKey: SLACK_URL
      remoteRef:
        key: prod/auth
        property: SLACK_URL
