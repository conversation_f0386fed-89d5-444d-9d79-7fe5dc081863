# ConfigMap definitions for nexus application components
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nexus-server-config
  labels:
    app: nexus-app-server
    component: app-server
data:
  # Application Configuration
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  ENVIRONMENT: "tools"
  SERVICE_PORT: "8000"

  # Gunicorn Configuration
  WORKERS: "4"
  WORKER_CLASS: "uvicorn.workers.UvicornWorker"

  # Database Configuration (non-sensitive)
  DATABASE_POOL_SIZE: "10"
  DATABASE_MAX_OVERFLOW: "20"
  DATABASE_POOL_TIMEOUT: "30"
  DATABASE_POOL_RECYCLE: "3600"

  # JWT Configuration (non-sensitive)
  JWT_ALGORITHM: "HS256"
  JWT_EXPIRE_MINUTES: "30"

  # Google OAuth Configuration (non-sensitive)
  GOOGLE_REDIRECT_URI: "https://nexus.ekscraving1775.treebo.com/login"

  # Security Configuration (non-sensitive)
  BCRYPT_ROUNDS: "12"
  SESSION_TIMEOUT: "3600"

  # AWS Configuration (non-sensitive)
  AWS_REGION: "ap-south-1"
  AWS_S3_BUCKET: "treebo-nexus-p"

  # Elasticsearch Configuration
  ELASTICSEARCH_URL: ""
  ELASTICSEARCH_INDEX: "knowledge_items"

  # File Upload Configuration
  MAX_FILE_SIZE_MB: "100"

  # Search Configuration
  SEARCH_RESULTS_LIMIT: "50"
  SEARCH_SNIPPET_LENGTH: "200"

  # Content Processing Configuration
  CONTENT_ANALYSIS_ENABLED: "true"
  AUTO_TAGGING_ENABLED: "true"

  # Export Configuration
  EXPORT_TIMEOUT_SECONDS: "300"
  MAX_EXPORT_ITEMS: "1000"

  # Git Configuration (non-sensitive)
  GIT_CLONE_TIMEOUT: "300"
  GIT_CLONE_DEPTH: "1"

  # Repomix Configuration
  REPOMIX_TIMEOUT: "600"
  REPOMIX_MAX_FILE_SIZE: "100"

  # Processing Configuration
  MAX_CONCURRENT_JOBS: "5"
  JOB_RETENTION_DAYS: "30"

  # CORS Configuration
  CORS_ORIGINS: '["https://nexus.ekscraving1775.treebo.com"]'

  # Trusted Hosts
  ALLOWED_HOSTS: '["api-nexus.ekscraving1775.treebo.com","nexus-backend-service","nexus-app-server.nexus.svc.cluster.local","localhost","127.0.0.1"]'

  # Rate Limiting
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nexus-worker-config
  labels:
    app: nexus-worker
    component: worker
data:
  # Application Configuration (shared with server)
  DEBUG: "true"
  LOG_LEVEL: "debug"
  ENVIRONMENT: "tools"
  SERVICE_PORT: "8000"

  # Gunicorn Configuration
  WORKERS: "4"
  WORKER_CLASS: "uvicorn.workers.UvicornWorker"

  # Database Configuration (non-sensitive)
  DATABASE_POOL_SIZE: "10"
  DATABASE_MAX_OVERFLOW: "20"
  DATABASE_POOL_TIMEOUT: "30"
  DATABASE_POOL_RECYCLE: "3600"

  # JWT Configuration (non-sensitive)
  JWT_ALGORITHM: "HS256"
  JWT_EXPIRE_MINUTES: "30"

  # Google OAuth Configuration (non-sensitive)
  GOOGLE_REDIRECT_URI: "https://nexus.ekscraving1775.treebo.com/login"

  # Security Configuration (non-sensitive)
  BCRYPT_ROUNDS: "12"
  SESSION_TIMEOUT: "3600"

  # AWS Configuration (non-sensitive)
  AWS_REGION: "ap-south-1"
  AWS_S3_BUCKET: "treebo-nexus-p"

  # Elasticsearch Configuration
  ELASTICSEARCH_URL: ""
  ELASTICSEARCH_INDEX: "knowledge_items"

  # File Upload Configuration
  MAX_FILE_SIZE_MB: "100"

  # Search Configuration
  SEARCH_RESULTS_LIMIT: "50"
  SEARCH_SNIPPET_LENGTH: "200"

  # Content Processing Configuration
  CONTENT_ANALYSIS_ENABLED: "true"
  AUTO_TAGGING_ENABLED: "true"

  # Export Configuration
  EXPORT_TIMEOUT_SECONDS: "300"
  MAX_EXPORT_ITEMS: "1000"

  # Git Configuration (non-sensitive)
  GIT_CLONE_TIMEOUT: "300"
  GIT_CLONE_DEPTH: "1"

  # Repomix Configuration
  REPOMIX_TIMEOUT: "600"
  REPOMIX_MAX_FILE_SIZE: "100"

  # Processing Configuration
  MAX_CONCURRENT_JOBS: "5"
  JOB_RETENTION_DAYS: "30"

  # CORS Configuration
  CORS_ORIGINS: '["https://nexus.ekscraving1775.treebo.com"]'

  # Trusted Hosts
  ALLOWED_HOSTS: '["api-nexus.ekscraving1775.treebo.com","nexus-backend-service","nexus-app-server.nexus.svc.cluster.local","localhost","127.0.0.1"]'

  # Rate Limiting
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"

  # Advanced Celery Worker Configuration (from entrypoint.sh)
  CELERY_WORKER_CONCURRENCY: "2"
  CELERY_WORKER_QUEUES: "processing,cleanup"
  CELERY_LOG_LEVEL: "info"
  CELERY_WORKER_MAX_TASKS_PER_CHILD: "1000"
  CELERY_WORKER_PREFETCH_MULTIPLIER: "1"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nexus-mcp-service-config
  labels:
    app: nexus-mcp-service
    component: mcp-service
data:
  # Server Configuration
  TREEBO_MCP_SERVER_NAME: "treebo-nexus-mcp"
  TREEBO_MCP_SERVER_VERSION: "1.0.0"
  TREEBO_MCP_HTTP_HOST: "0.0.0.0"
  TREEBO_MCP_HTTP_PORT: "8080"
  TREEBO_MCP_HTTP_WORKERS: "1"

  # Backend Service URLs
  TREEBO_MCP_CORE_PLATFORM_SERVICE_URL: "http://nexus-backend-service:8000"

  # Redis Configuration
  TREEBO_MCP_REDIS_MAX_CONNECTIONS: "20"

  # Cache TTL Settings (in seconds)
  TREEBO_MCP_CACHE_TTL_SEARCH: "300"
  TREEBO_MCP_CACHE_TTL_CONTENT: "600"
  TREEBO_MCP_CACHE_TTL_REPOSITORIES: "120"
  TREEBO_MCP_CACHE_TTL_REPO_CONTENT: "900"
  TREEBO_MCP_CACHE_TTL_STATUS: "30"
  TREEBO_MCP_CACHE_TTL_CODE_SEARCH: "180"

  # Rate Limiting
  TREEBO_MCP_RATE_LIMIT_BASIC: "100"
  TREEBO_MCP_RATE_LIMIT_PREMIUM: "1000"
  TREEBO_MCP_RATE_LIMIT_ENTERPRISE: "10000"

  # Performance Settings
  TREEBO_MCP_MAX_CONTENT_SIZE: "10485760"
  TREEBO_MCP_CHUNK_SIZE: "8192"
  TREEBO_MCP_CONNECTION_TIMEOUT: "30"
  TREEBO_MCP_REQUEST_TIMEOUT: "60"

  # Monitoring
  TREEBO_MCP_METRICS_ENABLED: "false"
  TREEBO_MCP_METRICS_PORT: "9090"
  TREEBO_MCP_HEALTH_CHECK_ENABLED: "true"

  # Logging
  TREEBO_MCP_LOG_LEVEL: "INFO"
  TREEBO_MCP_LOG_FORMAT: "json"

  # Feature Flags
  TREEBO_MCP_ENABLE_STREAMING: "true"
  TREEBO_MCP_ENABLE_COMPRESSION: "true"
  TREEBO_MCP_ENABLE_BATCHING: "true"
