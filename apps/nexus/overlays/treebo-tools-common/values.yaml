# Required fields for TreeboService Helm chart
serviceName: nexus
environment: production

services:
- name: frontend
  type: frontend
  port: 8080
  workloadCriticality: application-semi-critical
  image:
    registry: ************.dkr.ecr.ap-south-1.amazonaws.com
    repository: nexus-frontend-service
    tag: 63f606fb
  env:
  - name: NODE_ENV
    value: production
  - name: API_BASE_URL
    value: https://api-nexus.ekscraving1775.treebo.com
  envFrom:
  - configMapRef:
      name: nexus-server-config
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "1000m"

- name: backend-service
  type: backend
  port: 8000
  workloadCriticality: application-semi-critical
  serviceAccountName: nexus-service-sa
  image:
    registry: ************.dkr.ecr.ap-south-1.amazonaws.com
    repository: nexus-backend-service
    tag: latest
  envFrom:
  - configMapRef:
      name: nexus-server-config

- name: mcp-service
  type: backend
  port: 8080
  workloadCriticality: application-semi-critical
  serviceAccountName: nexus-service-sa
  image:
    registry: ************.dkr.ecr.ap-south-1.amazonaws.com
    repository: nexus-mcp-service
    tag: latest
  envFrom:
  - configMapRef:
      name: nexus-mcp-service-config

workers:
- name: celery-worker
  type: celery
  workloadCriticality: application-semi-critical
  serviceAccountName: nexus-service-sa
  image:
    registry: ************.dkr.ecr.ap-south-1.amazonaws.com
    repository: nexus-backend-service
    tag: latest
  command: ["./entrypoint.sh"]
  args: ["celery-worker"]
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  envFrom:
  - configMapRef:
      name: nexus-worker-config

tasks:
- name: migration-job
  type: migration
  workloadCriticality: application-semi-critical
  image:
    registry: ************.dkr.ecr.ap-south-1.amazonaws.com
    repository: nexus-backend-service
    tag: latest
  command: ["./entrypoint.sh"]
  args: ["migrate"]
  jobConfig:
    backoffLimit: 1
    ttlSecondsAfterFinished: 86400
    restartPolicy: Never
  envFrom:
  - configMapRef:
      name: nexus-server-config

# Production external secrets
externalSecrets:
- awsSecretPath: /treebo/production/eks/cluster-treebo-tools-common/nexus-secrets
  refreshInterval: "2h"
  secretStore: aws-parameters-store
  secretName: nexus-secrets

# Production environment variables
env:
- name: LOG_LEVEL
  value: DEBUG
- name: ENVIRONMENT
  value: production
- name: REDIS_URL
  value: redis://redis-cluster:6379/0

envFrom:
  - secretRef:
      name: nexus-secrets

# Production ingress
ingress:
- name: nexus-frontend-ingress
  host: nexus.ekscraving1775.treebo.com
  ingressClassName: nginx
  annotations:
    nginx.ingress.kubernetes.io/rate-limit: "100"
  paths:
  - path: /
    pathType: Prefix
    serviceName: frontend
    servicePort: 8080

- name: nexus-backend-ingress
  host: api-nexus.ekscraving1775.treebo.com # todo: change this to nexus-api
  ingressClassName: nginx
  annotations:
    nginx.ingress.kubernetes.io/rate-limit: "100"
  paths:
  - path: /
    pathType: Prefix
    serviceName: backend-service
    servicePort: 8000
- name: nexus-mcp-ingress
  host: nexus-mcp.ekscraving1775.treebo.com
  ingressClassName: nginx
  annotations:
    nginx.ingress.kubernetes.io/rate-limit: "100"
  paths:
  - path: /
    pathType: Prefix
    serviceName: mcp-service
    servicePort: 8080

# ServiceAccount definitions
serviceAccounts:
- name: nexus-service-sa
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/NexusServiceAccountRole-tools
  automountServiceAccountToken: true
