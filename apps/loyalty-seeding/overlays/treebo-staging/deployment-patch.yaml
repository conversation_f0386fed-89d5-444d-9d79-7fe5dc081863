apiVersion: apps/v1
kind: Deployment
metadata:
  name: loyalty-seeding-app-server
spec:
  selector:
    matchLabels:
      app: loyalty-seeding-app-server
      app.kubernetes.io/name: loyalty-seeding
      app.kubernetes.io/part-of: loyalty-seeding
      environment: staging
  template:
    metadata:
      labels:
        app: loyalty-seeding-app-server
        app.kubernetes.io/name: loyalty-seeding
        app.kubernetes.io/part-of: loyalty-seeding
        environment: staging
    spec:
      serviceAccountName: external-secrets-sa
      containers:
        - name: app-server
          image: ************.dkr.ecr.ap-south-1.amazonaws.com/loyalty-seeding-v2:staging
          command: ["/bin/bash", "/usr/src/app/scripts/gunicorn_start"]
          imagePullPolicy: Always
          env:
            - name: ENV
              value: "staging"
            - name: LOG_ROOT
              value: "/var/log/loyalty-seeding/"
            - name: FLASK_APP
              value: "autoapp.py"
            - name: APP_ENV
              valueFrom:
                configMapKeyRef:
                  name: loyalty-seeding-app-env
                  key: APP_ENV
          ports:
            - containerPort: 8001
              name: http
          resources:
            limits:
              cpu: "500m"
              memory: "1Gi"
            requests:
              cpu: "500m"
              memory: "1Gi"
          livenessProbe:
            httpGet:
              path: /api/health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 20
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /loyalty-seeding/api/health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 20
            failureThreshold: 3
      imagePullSecrets:
        - name: ecr-registry-secret
      volumes:
        - name: log-volume
          emptyDir: {}
