apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: loyalty-seeding-env
  namespace: loyalty-seeding-staging
  labels:
    argocd.argoproj.io/instance: loyalty-seeding-staging
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/sync-options: SkipDiff
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: loyalty-seeding-env
    creationPolicy: Orphan
    deletionPolicy: Retain
  data:
    - secretKey: RMQ_USER
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: RMQ_USER
    - secretKey: RMQ_PASS
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: RMQ_PASS
    - secretKey: AWS_ACCESS_KEY_ID
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: AWS_ACCESS_KEY_ID
    - secretKey: AWS_SECRET_ACCESS_KEY
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: AWS_SECRET_ACCESS_KEY
    - secretKey: GPG_KEY
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: GPG_KEY
    - secretKey: NEW_RELIC_LICENSE_KEY
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: NEW_RELIC_LICENSE_KEY
    - secretKey: SQLALCHEMY_DATABASE_URI
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: SQLALCHEMY_DATABASE_URI
    - secretKey: RABBIT_MQ_URL
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: RABBIT_MQ_URL
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: APP_ENV
      remoteRef:
        key: staging/loyalty-seeding-v2
        property: APP_ENV
        conversionStrategy: Default
        decodingStrategy: None
