apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: loyalty-seeding-ingress
  namespace: loyalty-seeding-staging
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/backend-protocol: "HTTP"
    alb.ingress.kubernetes.io/group.name: "staging-shared-alb-1"
    alb.ingress.kubernetes.io/healthcheck-protocol: "HTTP"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-name: "eks-staging-shared-alb-1"
    alb.ingress.kubernetes.io/scheme: "internal"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/healthcheck-path: "/api/health"
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "30"
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "20"
    alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    alb.ingress.kubernetes.io/unhealthy-threshold-count: "2"
    alb.ingress.kubernetes.io/namespace: "alb-ingress"
  labels:
    app: loyalty-seeding
spec:
  ingressClassName: alb
  rules:
  - host: loyalty-seeding.eksdose7983.treebo.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: loyalty-seeding-service
            port:
              number: 80
