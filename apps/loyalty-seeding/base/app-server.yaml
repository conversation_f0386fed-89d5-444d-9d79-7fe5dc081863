# Deployment configuration for loyalty-seeding app server
apiVersion: apps/v1
kind: Deployment
metadata:
  name: loyalty-seeding-app-server
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: loyalty-seeding-app-server
  template:
    metadata:
      labels:
        app: loyalty-seeding-app-server
        app.kubernetes.io/name: loyalty-seeding
        app.kubernetes.io/part-of: loyalty-seeding
        environment: staging
    spec:
      serviceAccountName: external-secrets-sa
      containers:
      - name: app-server
        image: ************.dkr.ecr.ap-south-1.amazonaws.com/loyalty-seeding-v2:staging
        command: ["/bin/bash", "/usr/src/app/scripts/gunicorn_start"]
        imagePullPolicy: Always
        env:
        - name: ENVIRONMENT
          value: "staging"
        - name: LOG_ROOT
          value: "/var/log/loyalty-seeding/"
        - name: FLASK_APP
          value: "autoapp.py"
        - name: APP_NAME
          value: "loyalty-seeding-v2"
        - name: AWS_SECRET_PREFIX
          value: "apps/loyalty-seeding-v2"
        - name: DEBUG
          value: "false"
        - name: TEMPLATE_DEBUG
          value: "false"
        - name: DEVELOPMENT
          value: "false"
        - name: GUNICORN_LOG_ROOT
          value: "-"
        - name: LOG_ROOT
          value: "/var/log/"
        - name: AUTH_SERVER_HOST
          value: "http://auth.treebo.be"
        - name: SQLALCHEMY_DATABASE_URI
          value: "postgresql://web_loyalty:f1L5*<EMAIL>:6432/loyalty_seeding"
        - name: NOTIFICATION_SERVICE_HOST
          value: "http://notification.treebo.be"
        - name: NOTIFICATION_EMAIL_API
          value: "/v1/notification/email/"
        - name: REDIS_CACHE_TYPE
          value: "redis"
        - name: REDIS_CACHE_HOST
          value: "redis"
        - name: REDIS_CACHE_DB
          value: "1"
        - name: RABBIT_MQ_URL
          valueFrom:
            secretKeyRef:
              name: loyalty-seeding-env
              key: RABBIT_MQ_URL
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: loyalty-seeding-env
              key: AWS_ACCESS_KEY_ID
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: loyalty-seeding-env
              key: AWS_SECRET_ACCESS_KEY
        - name: REDIS_CACHE_PORT
          value: "6379"
        - name: REDIS_CACHE_KEY_PREFIX
          value: "loyalty_v2_"
        - name: LOG_FORMAT
          value: "%(asctime)s:%(name)s:%(levelname)s:%(message)s"
        - name: LOG_FILE
          value: "loyalty_v2.log"
        - name: LOG_LEVEL
          value: "INFO"
        - name: GUNICORN_WORKER
          value: "3"
        - name: LOYALTY_SEEDING_EMAIL_SENDER
          value: "<EMAIL>"
        - name: AUTH_USER_DETAIL_API
          value: "/treeboauth/profile/v1/user/detail/"
        - name: AUTH_VALIDATE_USER_PHONE_API
          value: "/treeboauth/profile/v1/user/validation/phone-number"
        - name: AUTH_USER_REGISTRATION_PHONE_API
          value: "/treeboauth/profile/v1/user/register/phone-number"
        - name: REWARDS_EXCEL_BASE_PATH
          value: "/tmp/loyalty_v2/loyalty_seeding/static/"
        - name: SUB_JOBS_PER_PAGE
          value: "10"
        - name: SQLALCHEMY_POOL_SIZE
          value: "5"
        - name: SQLALCHEMY_MAX_OVERFLOW
          value: "10"
        - name: SQLALCHEMY_TRACK_MODIFICATIONS
          value: "True"
        - name: SQLALCHEMY_POOL_RECYCLE
          value: "20"
        - name: RABBIT_MQ_START_INTERVAL
          value: "0"
        - name: RABBIT_MQ_STEP_INTERVAL
          value: "2"
        - name: RABBIT_MQ_MAX_INTERVAL
          value: "30"
        - name: RABBIT_MQ_MAX_RETRIES
          value: "3"
        - name: RABBIT_MQ_HEARTBEAT_INTERVAL
          value: "10"
        - name: RABBIT_MQ_TRANSPORT_OPTIONS_KEY
          value: "confirm_publish"
        - name: RABBIT_MQ_TRANSPORT_OPTIONS_VALUE
          value: "True"
        - name: RABBIT_MQ_PREFETCH_COUNT
          value: "50"
        - name: SEEDING_QUEUE_NAME
          value: "loyalty_seeding_queue"
        - name: SEEDING_ROUTING_KEY
          value: "loyalty_seeding"
        - name: SEEDING_QUEUE_EXCHANGE
          value: "loyalty_seeding_exchange"
        - name: AWS_STORAGE_BUCKET_NAME
          value: "treebo"
        - name: AWS_S3_REGION
          value: "s3-ap-southeast-1.amazonaws.com"
        - name: REWARD_EARNING_SOURCE_NAME
          value: "loyalty-service"
        - name: REWARD_EARNING_SOURCE
          value: "earning_source"
        - name: LOYALTY_ADHOC_EVENT_TYPE
          value: "adhoc_credit"
        - name: TREEBO_REWARDS_WALLET_TYPE
          value: "TP"
        - name: SEEDING_CREDIT_AMOUNT_TYPE
          value: "ABSOLUTE"
        - name: PYTHON_VERSION
          value: "3.5.3"
        - name: PYTHON_PIP_VERSION
          value: "9.0.1"
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: loyalty-seeding-app-env
              key: APP_ENV
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: log-volume
          mountPath: /var/log/loyalty-seeding/
        livenessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 1
          failureThreshold: 3
          successThreshold: 1
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: log-volume
        emptyDir: {}
      imagePullSecrets:
      - name: ecr-registry-secret