apiVersion: batch/v1
kind: Job
metadata:
  name: loyalty-seeding-migration
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  backoffLimit: 1
  template:
    metadata:
      labels:
        app: loyalty-seeding-migration
    spec:
      restartPolicy: Never
      containers:
        - name: migration
          image: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/loyalty-seeding-v2:staging
          command: ["/bin/sh", "flask db migrate"]
          env:
            - name: ENVIRONMENT
              value: "staging"
            - name: LOG_ROOT
              value: "/var/log/loyalty-seeding/"
            - name: SQLALCHEMY_DATABASE_URI
              valueFrom:
                secretKeyRef:
                  name: loyalty-seeding-env
                  key: SQLALCHEMY_DATABASE_URI
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: loyalty-seeding-env
                  key: AWS_ACCESS_KEY_ID
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: loyalty-seeding-env
                  key: AWS_SECRET_ACCESS_KEY
            - name: APP_ENV
              valueFrom:
                configMapKeyRef:
                  name: loyalty-seeding-app-env
                  key: APP_ENV
          volumeMounts:
            - name: log-volume
              mountPath: /var/log/loyalty-seeding/
      volumes:
        - name: log-volume
          emptyDir: {}
      imagePullSecrets:
        []
