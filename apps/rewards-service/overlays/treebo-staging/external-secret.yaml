apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: rewards-service-env
  namespace: rewards-service-staging
  labels:
    argocd.argoproj.io/instance: rewards-service-staging
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/sync-options: SkipDiff
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: aws-secretsmanager
    kind: SecretStore
  target:
    name: rewards-service-env
    creationPolicy: Orphan
    deletionPolicy: Retain
  data:
    - secretKey: GPG_KEY
      remoteRef:
        key: staging/rewards-service
        property: GPG_KEY
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: NEW_RELIC_LICENSE_KEY
      remoteRef:
        key: staging/rewards-service
        property: NEW_RELIC_LICENSE_KEY
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: DB
      remoteRef:
        key: staging/rewards-service
        property: DB
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: ATLAS_DB
      remoteRef:
        key: staging/rewards-service
        property: ATLAS_DB
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: SECRET_KEY
      remoteRef:
        key: staging/rewards-service
        property: SECRET_KEY
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: SENTRY_DSN
      remoteRef:
        key: staging/rewards-service
        property: SENTRY_DSN
        conversionStrategy: Default
        decodingStrategy: None
