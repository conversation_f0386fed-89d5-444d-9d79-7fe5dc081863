apiVersion: v1
kind: Namespace
metadata:
  name: alb-ingress
  annotations:
    alb.ingress.kubernetes.io/scheme: "internal"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/healthcheck-path: "/health"
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "30"
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "5"
    alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    alb.ingress.kubernetes.io/unhealthy-threshold-count: "2"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: alb-ingress-controller
  namespace: rewards-service-staging
  labels:
    app.kubernetes.io/name: ingress-aws-alb
    app.kubernetes.io/instance: ingress-aws-alb
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: alb-ingress-controller
  labels:
    app.kubernetes.io/name: ingress-aws-alb
    app.kubernetes.io/instance: ingress-aws-alb
rules:
- apiGroups: [""]
  resources: ["configmaps", "endpoints", "events", "nodes", "pods", "secrets", "services"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["nodes", "pods", "secrets", "services"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["nodes", "pods", "secrets", "services"]
  verbs: ["update"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["ingresses/status"]
  verbs: ["update"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses/status"]
  verbs: ["update"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses/status"]
  verbs: ["patch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["create", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: alb-ingress-controller
  labels:
    app.kubernetes.io/name: ingress-aws-alb
    app.kubernetes.io/instance: ingress-aws-alb
subjects:
- kind: ServiceAccount
  name: alb-ingress-controller
  namespace: rewards-service-staging
roleRef:
  kind: ClusterRole
  name: alb-ingress-controller
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: nginx-ingress-nginx-admission
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: ""
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: nginx-ingress-nginx-controller-admission
      namespace: nginx
      path: /networking/v1/ingresses
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: validate.nginx.ingress.kubernetes.io
  namespaceSelector:
    matchExpressions:
    - key: kubernetes.io/metadata.name
      operator: NotIn
      values:
      - refund-staging
  objectSelector: {}
  rules:
  - apiGroups:
    - networking.k8s.io
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - ingresses
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
