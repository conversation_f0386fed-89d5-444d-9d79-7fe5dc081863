apiVersion: apps/v1
kind: Deployment
metadata:
  name: rewards-service-app-server
spec:
  selector:
    matchLabels:
      app: rewards-service-app-server
      app.kubernetes.io/name: rewards-service
      app.kubernetes.io/part-of: rewards-service
      environment: staging
  template:
    metadata:
      labels:
        app: rewards-service-app-server
        app.kubernetes.io/name: rewards-service
        app.kubernetes.io/part-of: rewards-service
        environment: staging
    spec:
      serviceAccountName: external-secrets-sa
      containers:
      - name: app-server
        image: ************.dkr.ecr.ap-south-1.amazonaws.com/reward:facets-d65074b-s-5498-aps1-01
        command: ["/usr/src/app/rewards/gunicorn_start"]
        imagePullPolicy: Always
        env:
        - name: ENV
          value: "staging"
        - name: LOG_ROOT
          value: "/var/log/rewards-service/"
        - name: FLASK_APP
          value: "autoapp.py"
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: rewards-service-app-env
              key: APP_ENV
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "250m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /rewards-service/api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /rewards-service/api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
      imagePullSecrets:
      - name: ecr-registry-secret
      volumes:
      - name: log-volume
        emptyDir: {}
