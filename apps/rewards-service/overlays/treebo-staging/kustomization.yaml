apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: rewards-service-staging

resources:
  - ../../base
  - external-secret.yaml
  - env-configmap.yaml
  - hpa.yaml
  - secretstore.yaml
  - aws-service-account.yaml
  - secret.yaml
  - service.yaml
  - alb-config.yaml
  - ingress.yaml

patches:
  - path: deployment-patch.yaml
    target:
      kind: Deployment
      name: rewards-service-app-server

commonLabels:
  environment: staging