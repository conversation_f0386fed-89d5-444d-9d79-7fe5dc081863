apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rewards-service-ingress
  namespace: rewards-service-staging
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/backend-protocol: "HTTP"
    alb.ingress.kubernetes.io/group.name: "staging-shared-alb-1"
    alb.ingress.kubernetes.io/healthcheck-protocol: "HTTP"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-name: "eks-staging-shared-alb-1"
    alb.ingress.kubernetes.io/scheme: "internal"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/healthcheck-path: "/health"
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "30"
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "5"
    alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    alb.ingress.kubernetes.io/unhealthy-threshold-count: "2"
    alb.ingress.kubernetes.io/namespace: "kube-system"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-south-1:605536185498:certificate/697530ff-2f29-4808-b96e-71fb638acf9b"
  labels:
    app: rewards-service
spec:
  ingressClassName: alb
  rules:
  - host: rewards.eksdose7983.treebo.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rewards-service-app-server
            port:
              number: 80
