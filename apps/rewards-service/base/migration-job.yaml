apiVersion: batch/v1
kind: Job
metadata:
  name: rewards-service-migration
  namespace: rewards-service-staging
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app: rewards-service-app-server
spec:
  backoffLimit: 1
  template:
    metadata:
      labels:
        app: rewards-service-app-server
    spec:
      restartPolicy: Never
      containers:
        - name: migration
          image: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/reward:facets-d65074b-s-5498-aps1-01
          command: ["flask", "db", "migrate"]
          env:
            - name: APP_NAME
              value: "reward"
            - name: AWS_SECRET_PREFIX
              value: "apps/reward"
            - name: FLASK_APP
              value: "reward_app:app"
            - name: DEBUG
              value: "false"
            - name: TEMPLATE_DEBUG
              value: "false"
            - name: DEVELOPMENT
              value: "false"
            - name: LOG_REQUEST_ID_HEADER
              value: "HTTP_REQUESTID"
            - name: GUNICORN_LOG_ROOT
              value: "-"
            - name: LOG_ROOT
              value: "/tmp"
            - name: NOTIFICATION_EMAIL_DEFAULT_SENDER_NAME
              value: "Treebo Rewards"
            - name: REWARDS_SERVICE_URL
              value: "https://rewards-staging.treebo.be/"
            - name: NOTIFICATION_SERVICE_DEFAULT_CONSUMER
              value: "rewards"
            - name: SQLALCHEMY_POOL_RECYCLE
              value: "60"
            - name: WALLET_SERVICE_ENDPOINT
              value: "https://wallet-staging.treebo.be"
            - name: AUTH_SERVER_HOST
              value: "https://auth.treebo.be"
            - name: SQLALCHEMY_MAX_OVERFLOW
              value: "10"
            - name: PYTHON_VERSION
              value: "3.5.3"
            - name: B2B_BASE_URL
              value: "http://corporates.treebo.be"
            - name: SECRET_KEY
              value: "dummy"
            - name: NOTIFICATION_EMAIL_DEFAULT_SENDER
              value: "<EMAIL>"
            - name: PYTHON_PIP_VERSION
            - name: PROWL_BASE_URL
              value: "http://prowl.treebo.be"
            - name: SQLALCHEMY_TRACK_MODIFICATIONS
              value: "True"
            - name: GPG_KEY
              valueFrom:
                secretKeyRef:
                  name: rewards-service-env
                  key: GPG_KEY
            - name: NEW_RELIC_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: rewards-service-env
                  key: NEW_RELIC_LICENSE_KEY
            - name: DB
              valueFrom:
                secretKeyRef:
                  name: rewards-service-env
                  key: DB
            - name: ATLAS_DB
              valueFrom:
                secretKeyRef:
                  name: rewards-service-env
                  key: ATLAS_DB
            - name: SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: rewards-service-env
                  key: SECRET_KEY
            - name: SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: rewards-service-env
                  key: SENTRY_DSN
            - name: APP_ENV
              valueFrom:
                configMapKeyRef:
                  name: rewards-service-app-env
                  key: APP_ENV
          volumeMounts:
            - name: log-volume
              mountPath: /tmp
      volumes:
        - name: log-volume
          emptyDir: {}
      imagePullSecrets:
        []
