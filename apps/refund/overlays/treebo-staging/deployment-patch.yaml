apiVersion: apps/v1
kind: Deployment
metadata:
  name: refund-app-server
spec:
  selector:
    matchLabels:
      app: refund-app-server
      app.kubernetes.io/name: refund
      app.kubernetes.io/part-of: refund
      environment: staging
  template:
    metadata:
      labels:
        app: refund-app-server
        app.kubernetes.io/name: refund
        app.kubernetes.io/part-of: refund
        environment: staging
    spec:
      serviceAccountName: external-secrets-sa
      containers:
      - name: app-server
        image: ************.dkr.ecr.ap-south-1.amazonaws.com/refund:staging
        command: ["/usr/src/scripts/gunicorn_start"]
        imagePullPolicy: Always
        env:
        - name: ENV
          value: "staging"
        - name: LOG_ROOT
          value: "/var/log/refund/"
        - name: FLASK_APP
          value: "autoapp.py"
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: refund-app-env
              key: APP_ENV
        ports:
        - containerPort: 8000
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "250m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /refund/api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /refund/api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
      imagePullSecrets:
      - name: ecr-registry-secret
      volumes:
      - name: log-volume
        emptyDir: {}
