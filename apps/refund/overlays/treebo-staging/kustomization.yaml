apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: refund-staging

resources:
  - ../../base
  - external-secret.yaml
  - env-configmap.yaml
  - hpa.yaml
  - secretstore.yaml
  - aws-service-account.yaml
  - secret.yaml
  - service.yaml
  - alb-namespace.yaml
  - alb-serviceaccount.yaml
  - alb-clusterrole.yaml
  - alb-clusterrolebinding.yaml
  - nginx-validating-webhook.yaml
  - ingress.yaml

patches:
  - path: deployment-patch.yaml
    target:
      kind: Deployment
      name: refund-app-server

commonLabels:
  environment: staging
