apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: refund-env
  namespace: refund-staging
  labels:
    argocd.argoproj.io/instance: refund-staging
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/sync-options: SkipDiff
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: refund-env
    creationPolicy: Orphan
    deletionPolicy: Retain
  data:
    - secretKey: RR_DOMAIN
      remoteRef:
        key: staging/refund
        property: RR_DOMAIN
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: DISCOUNT_DOMAIN
      remoteRef:
        key: staging/refund
        property: DISCOUNT_DOMAIN
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: TAX_DOMAIN
      remoteRef:
        key: staging/refund
        property: TAX_DOMAIN
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: CATALOG_DOMAIN
      remoteRef:
        key: staging/refund
        property: CATALOG_DOMAIN
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: SLACK_URL
      remoteRef:
        key: staging/refund
        property: SLACK_URL
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: APP_ENV
      remoteRef:
        key: staging/refund
        property: APP_ENV
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: GPG_KEY
      remoteRef:
        key: staging/refund
        property: REFUND_GPG_KEY
        conversionStrategy: Default
        decodingStrategy: None
    - secretKey: NEW_RELIC_LICENSE_KEY
      remoteRef:
        key: staging/refund
        property: REFUND_NEW_RELIC_LICENSE_KEY
        conversionStrategy: Default
        decodingStrategy: None
