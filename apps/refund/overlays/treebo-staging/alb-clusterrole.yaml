apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: alb-ingress-controller
  labels:
    app.kubernetes.io/name: ingress-aws-alb
    app.kubernetes.io/instance: ingress-aws-alb
rules:
- apiGroups: [""]
  resources: ["configmaps", "endpoints", "events", "nodes", "pods", "secrets", "services"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["nodes", "pods", "secrets", "services"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["nodes", "pods", "secrets", "services"]
  verbs: ["update"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["ingresses/status"]
  verbs: ["update"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses/status"]
  verbs: ["update"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses/status"]
  verbs: ["patch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["create", "update"]
