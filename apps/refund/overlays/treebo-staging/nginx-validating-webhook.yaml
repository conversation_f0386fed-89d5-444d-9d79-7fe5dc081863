apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: nginx-ingress-nginx-admission
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: ""
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: nginx-ingress-nginx-controller-admission
      namespace: nginx
      path: /networking/v1/ingresses
      port: 443
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: validate.nginx.ingress.kubernetes.io
  namespaceSelector:
    matchExpressions:
    - key: kubernetes.io/metadata.name
      operator: NotIn
      values:
      - refund-staging
  objectSelector: {}
  rules:
  - apiGroups:
    - networking.k8s.io
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - ingresses
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
