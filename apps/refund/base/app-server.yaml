# Deployment configuration for refund app server
apiVersion: apps/v1
kind: Deployment
metadata:
  name: refund-app-server
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: refund-app-server
  template:
    metadata:
      labels:
        app: refund-app-server
    spec:
      containers:
      - name: app-server
        image: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/refund:staging
        command: ["/usr/src/scripts/gunicorn_start"]
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 5
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 3
        env:
        - name: APP_NAME
          value: "refund"
        - name: AWS_SECRET_PREFIX
          value: "apps/refund"
        - name: FLASK_APP
          value: "autoapp.py"
        - name: DEBUG
          value: "false"
        - name: TEMPLATE_DEBUG
          value: "false"
        - name: DEVELOPMENT
          value: "false"
        - name: LOG_REQUEST_ID_HEADER
          value: "HTTP_REQUESTID"
        - name: GUNICORN_LOG_ROOT
          value: "-"
        - name: LOG_ROOT
          value: "/var/log/refund/"
        - name: EXT_EXCHANGE_TYPE
          value: "exchange_type"
        - name: PYTHON_VERSION
          value: "3.6.14"
        - name: TAXATION_SERVICE_API
          value: "/tax/v3/calculate_tax"
        - name: CRS_ROUTING_KEY
          value: "booking"
        - name: TZ
          value: "Asia/Kolkata"
        - name: CRS_EXCHANGE_NAME
          value: "auto_refund_integration_event_exchange"
        - name: POILCY_SERVICE_API
          value: "room-booking/refund/amount/"
        - name: EXT_EXCHANGE_NAME
          value: "exchange_name"
        - name: SENTRY_DSN
          value: "dummy"
        - name: LANG
          value: "C.UTF-8"
        - name: EXT_BROKER_URL_KEY
          value: "broker_url"
        - name: MANUAL_REFUND_EMAIL
          value: "<EMAIL>"
        - name: PAYMENT_SERVICE_URL
          value: "https://payments.treebo.com/api/"
        - name: CRS_EXCHANGE_TYPE
          value: "topic"
        - name: AUTO_REFUND_LIMIT
          value: "1000"
        - name: CRS_INTEGRATION_EVENT
          value: "crs_integration_event"
        - name: WEBPACK_MANIFEST_PATH
          value: "webpack/manifest.json"
        - name: THSC_ENVIRONMENT
          value: "staging"
        - name: PAYMENT_SERVICE_API
          value: "paynow/v2/refund/"
        - name: EXT_QUEUE_NAME
          value: "queue_name"
        - name: NEW_RELIC_CONFIG_FILE
          value: "/usr/src/auto-refund-engine/newrelic.ini"
        - name: NEW_RELIC_ENVIRONMENT
          value: "staging"
        - name: TAXATION_SERVICE_URL
          value: "http://tax-staging.treebo.be"
        - name: CATALOG_SERVICE_URL
          value: "https://catalog.treebo.be/cataloging-service/api/v1/properties/"
        - name: POILCY_SERVICE_URL
          value: "https://growth.treebohotels.be/growth/api/v1/policy_service/"
        - name: PYTHON_PIP_VERSION
          value: "21.2.4"
        - name: NOTIFICATION_SERVICE_URL
          value: "http://notification.treebo.be/"
        - name: CRS_QUEUE_NAME
          value: "auto_refund_engine"
        - name: NOTIFICATION_SERVICE_API
          value: "v1/notification/sms/"
        - name: EXT_QUEUE_ROUTING_KEY
          value: "queue_routing_key"
        - name: PYTHON_GET_PIP_SHA256
          value: "fa6f3fb93cce234cd4e8dd2beb547653b52855a48dd44e6b21ff28b"
        - name: SLACK_ALERTS_URL
          value: "*****************************************************************************"
        - name: PYTHON_GET_PIP_URL
          value: "https://github.com/pypa/get-pip/raw/c20b0cfd643cd4a19246ccf204e2997af70f6b21/public/get-pip.py"
        - name: SQLALCHEMY_TRACK_MODIFICATIONS
          value: "False"
        - name: DOMAIN_EVENT_PRODUCER_THREADS
          value: "10"
        - name: ENVIRONMENT
          value: "staging"
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: refund-app-env
              key: APP_ENV
        ports:
        - containerPort: 8000
          name: http
        volumeMounts:
        - name: log-volume
          mountPath: /var/log/refund/

        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: log-volume
        emptyDir: {}
      imagePullSecrets:
      - name: ecr-registry-secret