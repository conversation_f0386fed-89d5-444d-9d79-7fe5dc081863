apiVersion: batch/v1
kind: Job
metadata:
  name: refund-migration
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  backoffLimit: 1
  template:
    metadata:
      labels:
        app: refund-migration
    spec:
      restartPolicy: Never
      containers:
        - name: migration
          image: ************.dkr.ecr.ap-south-1.amazonaws.com/refund:staging
          command: ["flask", "db", "migrate"]
          env:
            - name: APP_ENV
              valueFrom:
                secretKeyRef:
                  name: refund-env
                  key: APP_ENV
            - name: LOG_ROOT
              value: "/tmp"
          volumeMounts:
            - name: log-volume
              mountPath: /var/log/refund/
      serviceAccountName: external-secrets-sa
      volumes:
        - name: log-volume
          emptyDir: {}
