apiVersion: batch/v1
kind: Job
metadata:
  name: wallet-migration
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  backoffLimit: 0
  template:
    metadata:
      labels:
        app: wallet-migration
    spec:
      restartPolicy: Never
      containers:
        - name: migration
          image: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/wallet:staging
          command: ["/bin/sh", "-c", "echo Migration runs here"]
          env:
            - name: LOG_ROOT
              value: "/var/log/wallet/"
            - name: GUNICORN_ERRORLOG
              value: "/var/log/"
            - name: GUNICORN_ACCESSLOG
              value: "/var/log/"
            - name: APP_ENV
              valueFrom:
                configMapKeyRef:
                  name: wallet-app-env
                  key: APP_ENV
            - name: DEBUG
              valueFrom:
                configMapKeyRef:
                  name: wallet-app-env
                  key: DEBUG
            - name: TESTING
              valueFrom:
                configMapKeyRef:
                  name: wallet-app-env
                  key: TESTING
            - name: SQLALCHEMY_ECHO
              valueFrom:
                configMapKeyRef:
                  name: wallet-app-env
                  key: SQLALCHEMY_ECHO
            - name: SQLALCHEMY_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: wallet-app-env
                  key: SQLALCHEMY_POOL_SIZE
            - name: SQLALCHEMY_MAX_OVERFLOW
              valueFrom:
                configMapKeyRef:
                  name: wallet-app-env
                  key: SQLALCHEMY_MAX_OVERFLOW
            - name: AUTH_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: wallet-env
                  key: AUTH_BASE_URL
            - name: RABBITMQ_HOST
              valueFrom:
                secretKeyRef:
                  name: wallet-env
                  key: RABBITMQ_HOST
            - name: DB
              valueFrom:
                secretKeyRef:
                  name: wallet-env
                  key: DB
          volumeMounts:
            - name: log-volume
              mountPath: /var/log/wallet/
      volumes:
        - name: log-volume
          emptyDir: {}
      imagePullSecrets: []
