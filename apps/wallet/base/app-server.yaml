# Deployment configuration for wallet app server
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet-app-server
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wallet-app-server
  template:
    metadata:
      labels:
        app: wallet-app-server
    spec:
      containers:
      - name: app-server
        image: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/wallet:staging
        command: ["/usr/src/app/scripts/gunicorn_start"]
        imagePullPolicy: Always
        env:
        - name: LOG_ROOT
          value: "/var/log/wallet/"
        - name: GUNICORN_ERRORLOG
          value: "/var/log/"
        - name: GUNICORN_ACCESSLOG
          value: "/var/log/"
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: wallet-app-env
              key: APP_ENV
        - name: DEBUG
          valueFrom:
            configMapKeyRef:
              name: wallet-app-env
              key: DEBUG
        - name: TESTING
          valueFrom:
            configMapKeyRef:
              name: wallet-app-env
              key: TESTING
        - name: SQLALCHEMY_ECHO
          valueFrom:
            configMapKeyRef:
              name: wallet-app-env
              key: SQLALCHEMY_ECHO
        - name: SQLALCHEMY_POOL_SIZE
          valueFrom:
            configMapKeyRef:
              name: wallet-app-env
              key: SQLALCHEMY_POOL_SIZE
        - name: SQLALCHEMY_MAX_OVERFLOW
          valueFrom:
            configMapKeyRef:
              name: wallet-app-env
              key: SQLALCHEMY_MAX_OVERFLOW
        - name: AUTH_BASE_URL
          valueFrom:
            secretKeyRef:
              name: wallet-env
              key: AUTH_BASE_URL
        - name: RABBITMQ_HOST
          valueFrom:
            secretKeyRef:
              name: wallet-env
              key: RABBITMQ_HOST
        - name: DB
          valueFrom:
            secretKeyRef:
              name: wallet-env
              key: DB
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: log-volume
          mountPath: /var/log/wallet/
        livenessProbe:
          httpGet:
            path: /wallet/api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /wallet/api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: log-volume
        emptyDir: {}
      imagePullSecrets:
      - name: ecr-registry-secret