apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: wallet-env
  namespace: wallet-staging
  labels:
    argocd.argoproj.io/instance: wallet-staging
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/sync-options: SkipDiff
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: wallet-env
    creationPolicy: Orphan
    deletionPolicy: Retain
  data:
    - secretKey: AUTH_BASE_URL
      remoteRef:
        key: staging/wallet
        property: AUTH_BASE_URL
    - secretKey: RABBITMQ_HOST
      remoteRef:
        key: staging/wallet
        property: RABBITMQ_HOST
    - secretKey: DB
      remoteRef:
        key: staging/wallet
        property: DB
