apiVersion: external-secrets.io/v1alpha1
kind: ExternalSecret
metadata:
  name: wallet-env
  namespace: wallet-prod
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-ssm-parameterstore
    kind: SecretStore
  target:
    name: wallet-env
    creationPolicy: Owner
  data:
    - secretKey: RR_DOMAIN
      remoteRef:
        key: /prod/wallet/RR_DOMAIN
    - secretKey: DISCOUNT_DOMAIN
      remoteRef:
        key: /prod/wallet/DISCOUNT_DOMAIN
    - secretKey: TAX_DOMAIN
      remoteRef:
        key: /prod/wallet/TAX_DOMAIN
    - secretKey: CATALOG_DOMAIN
      remoteRef:
        key: /prod/wallet/CATALOG_DOMAIN
