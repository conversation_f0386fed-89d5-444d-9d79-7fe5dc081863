{"name": "devops_treebo_infra_1", "version": "1.0.0", "description": "Infrastructure and tools management for Treebo using AWS CDK and related tooling.", "main": "index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "cdk": "cdk", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "prettier --write .", "format:check": "prettier --check .", "biome:check": "biome check .", "biome:fix": "biome check --apply .", "biome:format": "biome format --write .", "biome:lint": "biome lint .", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "dev": "ts-node", "setup-service-accounts": "ts-node clusters/setup-service-accounts.ts", "setup-s3-buckets": "ts-node clusters/setup-s3-buckets.ts"}, "author": "", "license": "MIT", "dependencies": {"@aws-cdk/lambda-layer-kubectl-v32": "^2.1.0", "@aws-sdk/client-ec2": "^3.787.0", "@aws-sdk/client-efs": "^3.828.0", "@aws-sdk/client-eks": "^3.797.0", "@aws-sdk/client-iam": "^3.816.0", "@aws-sdk/client-s3": "^3.808.0", "@aws-sdk/client-secrets-manager": "^3.787.0", "@aws-sdk/client-sts": "^3.828.0", "@kubernetes/client-node": "^1.1.2", "aws-cdk-lib": "^2.190.0", "bcryptjs": "^3.0.2", "constructs": "^10.4.2", "zod": "^3.25.23"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "ts-node": "^10.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "resolutions": {"aws-cdk-lib": "2.87.0"}}