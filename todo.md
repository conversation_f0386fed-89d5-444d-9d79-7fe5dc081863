1. Retention period for monitoring
2. IRSA for all the services
3. Check the PR lints and fix. Fix the pipeline if its broken
4. Secret creation management
5. use EFS for applications and EBS for tools
6. configure loki s3 versioning? and auth is disabled
7. configure drone

8. Tag security group with karpenter.sh/discovery: cluster-treebo-tools-common
9. <PERSON><PERSON><PERSON> marked drift in a nodepool vs ec2 node class and kept disrupting pods very frequently every 5mins. Create an alert for this but also use kubernetes events exporter
10. Sometimes argo cd stateful sets or pods can be terminating for ever. Setup alerts
11. Argocd drift alerts

12. evaluate github app instead of github oauth app for drone
13. add uptime kuma
14. use public alb for drone io hooks
15. enhance treebo service helm chart rolling update strategy configuration
    - add configurable deployment strategy options (maxUnavailable, maxSurge)
    - provide sensible defaults for different workload criticality levels
    - allow per-service deployment strategy overrides
    - maintain backward compatibility with existing deployments
16. add aws-node-termination-handler
17. add botkube
18. add opencost
19. add kubescape
