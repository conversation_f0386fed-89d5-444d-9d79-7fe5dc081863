{"$schema": "https://biomejs.dev/schemas/1.8.3/schema.json", "organizeImports": {"enabled": true}, "files": {"include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.json", "**/*.jsonc"], "ignore": ["node_modules/**", "dist/**", "build/**", "coverage/**", "**/*.generated.*", "**/*.gen.*", "charts/**", "vendor/**", "cdk.out/**", ".git/**"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noBannedTypes": "error", "noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noStaticOnlyClass": "error", "noUselessCatch": "error", "noUselessConstructor": "error", "noUselessLoneBlockStatements": "error", "noUselessRename": "error", "noUselessSwitchCase": "error", "noUselessTernary": "error", "noWith": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidNewBuiltin": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "error", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "style": {"noArguments": "error", "noNonNullAssertion": "off", "noVar": "error", "useConst": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useExponentiationOperator": "error", "useFragmentSyntax": "error", "useLiteralEnumMembers": "error", "useNumericLiterals": "error", "useSelfClosingElements": "error", "useShorthandArrayType": "error", "useShorthandAssign": "error", "useSingleVarDeclarator": "error", "useTemplate": "error"}, "suspicious": {"noArrayIndexKey": "error", "noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}, "globals": ["console", "process", "<PERSON><PERSON><PERSON>", "__dirname", "__filename", "global", "module", "require", "exports"]}, "json": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "trailingCommas": "none"}, "parser": {"allowComments": true, "allowTrailingCommas": false}}, "overrides": [{"include": ["**/*.json", "**/*.jsonc"], "json": {"parser": {"allowComments": true, "allowTrailingCommas": false}}}, {"include": ["package.json"], "json": {"parser": {"allowComments": false, "allowTrailingCommas": false}}}, {"include": ["tsconfig*.json"], "json": {"parser": {"allowComments": true, "allowTrailingCommas": true}}}]}