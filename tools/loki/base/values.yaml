loki:
  auth_enabled: false
  storage:
    type: s3
    s3:
      region: ap-south-1
    bucketNames:
      chunks: s3-loki-treebo-v1
      ruler: s3-loki-treebo-v1
      admin: s3-loki-treebo-v1
  schemaConfig:
    configs:
      - from: 2025-05-01
        store: tsdb
        object_store: s3
        schema: v13
        index:
          prefix: loki_index_
          period: 24h

test:
  enabled: false

lokiCanary:
  enabled: false

backend:
  persistence:
    storageClass: ebs-sc
    size: 5Gi

write:
  persistence:
    storageClass: ebs-sc
    size: 10Gi

read:
  persistence:
    storageClass: ebs-sc
    size: 10Gi

chunksCache:
  persistence:
    storageClass: ebs-sc
    size: 5Gi
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi

resultsCache:
  persistence:
    storageClass: ebs-sc
    size: 5Gi
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: $(loki_role_arn)

gateway:
  ingress:
    enabled: true
    ingressClassName: nginx
    hosts:
      - host: $(loki_host)
        paths:
          - path: /
            pathType: Prefix
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      nginx.ingress.kubernetes.io/enable-access-log: "true"
