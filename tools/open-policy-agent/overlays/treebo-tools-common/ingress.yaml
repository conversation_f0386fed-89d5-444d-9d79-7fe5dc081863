apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: opa-ingress
  namespace: opa-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: opa-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - OPA'
spec:
  ingressClassName: nginx
  rules:
  - host: opa.ekscraving1775.treebo.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: opa
            port:
              number: 8181
  tls:
  - hosts:
    - opa.ekscraving1775.treebo.com
    secretName: opa-tls
