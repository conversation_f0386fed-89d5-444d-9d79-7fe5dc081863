apiVersion: v1
kind: ConfigMap
metadata:
  name: opa-policies
  namespace: opa-system
data:
  example-policy.rego: |
    package kubernetes.admission
    
    import rego.v1
    
    # Deny pods without resource limits
    deny contains msg if {
        input.request.kind.kind == "Pod"
        input.request.operation == "CREATE"
        container := input.request.object.spec.containers[_]
        not container.resources.limits
        msg := sprintf("Container '%s' is missing resource limits", [container.name])
    }
    
    # Deny pods with privileged containers
    deny contains msg if {
        input.request.kind.kind == "Pod"
        input.request.operation == "CREATE"
        container := input.request.object.spec.containers[_]
        container.securityContext.privileged == true
        msg := sprintf("Container '%s' is running in privileged mode", [container.name])
    }
    
    # Require specific labels
    deny contains msg if {
        input.request.kind.kind == "Pod"
        input.request.operation == "CREATE"
        required_labels := {"app", "version", "environment"}
        provided_labels := object.get(input.request.object.metadata, "labels", {})
        missing_labels := required_labels - set(object.keys(provided_labels))
        count(missing_labels) > 0
        msg := sprintf("Pod is missing required labels: %v", [missing_labels])
    }
