# Open Policy Agent (OPA)

This tool deploys Open Policy Agent (OPA) for policy-based control and governance in Kubernetes.

## Overview

Open Policy Agent (OPA) is an open-source, general-purpose policy engine that unifies policy enforcement across the stack. It provides a high-level declarative language (Rego) for authoring policies and simple APIs to offload policy decision-making.

## Prerequisites

- Kubernetes cluster
- Ingress controller (nginx)
- Basic authentication secret (optional)

## Setup

1. Deploy using ArgoCD or kubectl:
   ```bash
   kubectl apply -k tools/open-policy-agent/overlays/treebo-tools-common/
   ```

2. (Optional) Create basic auth secret for ingress:
   ```bash
   htpasswd -c auth admin
   kubectl create secret generic opa-basic-auth --from-file=auth -n opa-system
   ```

## Configuration

### Base Configuration
- **Namespace**: opa-system
- **Helm Chart**: opa from open-policy-agent.github.io/helm-charts
- **Version**: 0.17.1
- **Replicas**: 2

### Features Enabled
- **Decision Logs**: Console logging of policy decisions
- **Status Reporting**: Health and status reporting
- **Bundle Management**: Policy bundle loading and updates
- **Metrics**: Prometheus metrics on port 8181

## Access

The OPA API is accessible via:
- **URL**: https://opa.ekscraving1775.treebo.com
- **Authentication**: Basic auth (if configured)
- **API Endpoint**: `/v1/data`

## Policies

### Example Policies Included

The deployment includes example policies for Kubernetes admission control:

1. **Resource Limits Policy**: Denies pods without resource limits
2. **Privileged Container Policy**: Denies privileged containers
3. **Required Labels Policy**: Enforces required labels (app, version, environment)

### Policy Structure

Policies are written in Rego and stored in ConfigMaps:

```rego
package kubernetes.admission

import rego.v1

# Deny pods without resource limits
deny contains msg if {
    input.request.kind.kind == "Pod"
    input.request.operation == "CREATE"
    container := input.request.object.spec.containers[_]
    not container.resources.limits
    msg := sprintf("Container '%s' is missing resource limits", [container.name])
}
```

## Usage

### Querying Policies

Query OPA for policy decisions:

```bash
# Check if a pod is allowed
curl -X POST https://opa.ekscraving1775.treebo.com/v1/data/kubernetes/admission \
  -H 'Content-Type: application/json' \
  -d @pod-request.json
```

### Adding Custom Policies

1. Create a new ConfigMap with your Rego policies:
   ```yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: custom-policies
     namespace: opa-system
   data:
     custom-policy.rego: |
       package kubernetes.admission
       # Your custom policy here
   ```

2. Mount the ConfigMap in the OPA deployment

### Admission Controller Integration

To use OPA as an admission controller:

1. Enable admission controller in values.yaml:
   ```yaml
   admissionController:
     enabled: true
   ```

2. Configure webhook:
   ```yaml
   webhook:
     enabled: true
     failurePolicy: Fail
     rules:
       - operations: ["CREATE", "UPDATE"]
         apiGroups: [""]
         apiVersions: ["v1"]
         resources: ["pods"]
   ```

## Monitoring

### Metrics

OPA exposes Prometheus metrics at `/metrics`:
- Policy evaluation metrics
- Decision log metrics
- Bundle status metrics
- HTTP API metrics

### Health Checks

- **Health endpoint**: `/health`
- **Ready endpoint**: `/health?bundle=true`

### Logs

Check OPA logs:
```bash
kubectl logs -n opa-system deployment/opa
```

## API Examples

### Data API

```bash
# Get all data
curl https://opa.ekscraving1775.treebo.com/v1/data

# Get specific policy data
curl https://opa.ekscraving1775.treebo.com/v1/data/kubernetes/admission
```

### Policy API

```bash
# List policies
curl https://opa.ekscraving1775.treebo.com/v1/policies

# Get specific policy
curl https://opa.ekscraving1775.treebo.com/v1/policies/example-policy
```

## Security

- **RBAC**: Service account with minimal required permissions
- **Security Context**: Runs as non-root user (1000)
- **Network Policies**: Restrict network access (if configured)
- **TLS**: HTTPS termination at ingress

## Troubleshooting

### Common Issues

1. **Policy compilation errors**: Check Rego syntax
2. **Bundle loading failures**: Verify bundle configuration
3. **Admission webhook failures**: Check webhook configuration

### Debug Commands

```bash
# Check pod status
kubectl get pods -n opa-system

# Check logs
kubectl logs -n opa-system deployment/opa

# Check policies
kubectl get configmap -n opa-system

# Test policy evaluation
curl -X POST https://opa.ekscraving1775.treebo.com/v1/data/kubernetes/admission \
  -H 'Content-Type: application/json' \
  -d '{"input": {"request": {...}}}'
```

## Best Practices

1. **Policy Testing**: Test policies thoroughly before deployment
2. **Gradual Rollout**: Start with warn mode before enforcing
3. **Monitoring**: Monitor policy decisions and performance
4. **Documentation**: Document policy intent and usage
5. **Version Control**: Store policies in version control
