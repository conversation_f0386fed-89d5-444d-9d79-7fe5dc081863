# Open Policy Agent configuration
image:
  repository: openpolicyagent/opa
  tag: 0.70.0-envoy
  pullPolicy: IfNotPresent

replicaCount: 2

# OPA configuration
opa:
  # Enable decision logs
  decisionLogs:
    enabled: true
    console: true
  
  # Enable status reporting
  status:
    enabled: true
    console: true
  
  # Bundle configuration
  bundles:
    authz:
      resource: "bundle.tar.gz"
      persist: true
      polling:
        min_delay_seconds: 10
        max_delay_seconds: 20

# Service configuration
service:
  type: ClusterIP
  port: 8181
  targetPort: 8181

# Resources
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 1000

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 1000

# Service account
serviceAccount:
  create: true
  name: opa

# RBAC
rbac:
  create: true

# Ingress
ingress:
  enabled: false

# Monitoring
monitoring:
  enabled: true
  port: 8181
  path: /metrics

# Admission controller webhook (disabled by default)
admissionController:
  enabled: false
