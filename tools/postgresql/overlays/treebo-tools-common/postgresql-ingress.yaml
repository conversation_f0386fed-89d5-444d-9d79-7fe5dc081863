apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: postgresql-ingress
  namespace: postgresql
  annotations:
    nginx.ingress.kubernetes.io/tcp-services-configmap: "postgresql/postgresql-tcp"
    nginx.ingress.kubernetes.io/backend-protocol: "TCP"
spec:
  ingressClassName: nginx
  rules:
    - host: postgresql.ekscraving1775.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: postgresql
                port:
                  number: 5432
