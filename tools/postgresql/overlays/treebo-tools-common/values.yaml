# PostgreSQL configuration for treebo-tools-common
# Simple PostgreSQL deployment for tools cluster

# Authentication with external secrets
auth:
  existingSecret: "postgresql-secrets"
  secretKeys:
    adminPasswordKey: "postgres-password"
    userPasswordKey: "password"

primary:
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 500m
      memory: 1Gi

  persistence:
    storageClass: ebs-sc
    size: 5Gi

metrics:
  enabled: true
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

networkPolicy:
  enabled: false

volumePermissions:
  enabled: false
