apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: postgresql-external-secret
  namespace: postgresql
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: postgresql-secrets
    creationPolicy: Owner
  data:
    - secretKey: postgres-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/postgresql-secrets
        property: postgresPassword
    - secretKey: password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/postgresql-secrets
        property: userPassword
