```
-- 1. Create user
CREATE USER myapp_user WITH PASSWORD 'your_password';

-- 2. Create database
CREATE DATABASE myapp_db OWNER myapp_user;

-- 3. Grant all privileges
GRANT ALL PRIVILEGES ON DAT<PERSON><PERSON>E myapp_db TO myapp_user;

-- 4. Connect to the database
\c myapp_db;

-- 5. <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO myapp_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO myapp_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO myapp_user;

-- 6. Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO myapp_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO myapp_user;
```
