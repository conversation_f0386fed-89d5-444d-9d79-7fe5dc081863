{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["autoscaling:DescribeAutoScalingGroups", "autoscaling:DescribeAutoScalingInstances", "autoscaling:DescribeLaunchConfigurations", "autoscaling:DescribeScalingActivities", "autoscaling:DescribeTags", "ec2:DescribeImages", "ec2:DescribeInstanceTypes", "ec2:DescribeLaunchTemplateVersions", "ec2:GetInstanceTypesFromInstanceRequirements"], "Resource": "*"}, {"Effect": "Allow", "Action": ["autoscaling:SetDesiredCapacity", "autoscaling:TerminateInstanceInAutoScalingGroup", "ec2:DescribeInstanceAttribute"], "Resource": "*"}, {"Effect": "Allow", "Action": ["autoscaling:CreateOrUpdateTags"], "Resource": "*", "Condition": {"StringEquals": {"autoscaling:ResourceTag/kubernetes.io/cluster/ekscraving1775": "owned"}}}]}