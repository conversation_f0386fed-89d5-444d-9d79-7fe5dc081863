autoDiscovery:
  clusterName: cluster-treebo-tools-common
  enabled: false # Disable auto-discovery to manage only specific node groups

# Explicitly specify only the SystemNodeGroup for cluster autoscaler management
autoscalingGroupsnamePrefix:
  - name: eks-clustertreebotoolscommon # SystemNodeGroup ASG pattern
    minSize: 1
    maxSize: 3

# AWS region
awsRegion: ap-south-1

deployment:
  annotations:
    cluster-autoscaler.kubernetes.io/safe-to-evict: "false"

# Service account configuration
rbac:
  create: true
  serviceAccount:
    create: true
    name: cluster-autoscaler-sa
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/role-cluster-autoscaler-treebo-tools-common

# Deployment configuration
replicaCount: 1

# Resource configuration
resources:
  limits:
    cpu: 100m
    memory: 300Mi
  requests:
    cpu: 100m
    memory: 300Mi

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 65534
  fsGroup: 65534

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 65534
  fsGroup: 65534

# Node selector to run on system nodes that are safe for cluster autoscaler
nodeSelector:
  cluster-autoscaler-safe: "true"

# Tolerations for system workloads
tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/master

# Affinity rules
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: cluster-autoscaler-safe
              operator: In
              values:
                - "true"
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - cluster-autoscaler
          topologyKey: kubernetes.io/hostname

# Cluster autoscaler configuration
extraArgs:
  # Scaling behavior
  scale-down-enabled: true
  scale-down-delay-after-add: 10m
  scale-down-delay-after-delete: 10s
  scale-down-delay-after-failure: 3m
  scale-down-unneeded-time: 10m
  scale-down-utilization-threshold: 0.5

  # Node group balancing
  balance-similar-node-groups: true
  skip-nodes-with-system-pods: true # CRITICAL: Don't scale down nodes with system pods
  skip-nodes-with-local-storage: false

  # Expander strategy for mixed instance types
  expander: priority

  # Logging
  v: 4
  logtostderr: true

  # Performance - limited to SystemNodeGroup only
  max-nodes-total: 3 # SystemNodeGroup maxSize
  cores-total: 0:6 # 3 nodes * 2 cores (t3a.medium)
  memory-total: 0:12 # 3 nodes * 4GB (t3a.medium)

  # Node startup time
  max-node-provision-time: 15m

  # Spot instance handling
  ignore-daemonsets-utilization: true
  ignore-mirror-pods-utilization: true

  # Additional safety measures
  scale-down-non-empty-candidates-count: 30
  scale-down-candidates-pool-ratio: 0.1
  scale-down-candidates-pool-min-count: 50

# Priority expander configuration - managed via configmap for SystemNodeGroup only
# expanderPriorities: |-
#   1:
#     - .*SystemNodeGroup.*

# Monitoring
serviceMonitor:
  enabled: true
  namespace: kube-system
  interval: 30s
  path: /metrics

# Pod disruption budget - disabled because we have a manual PDB in overlays
podDisruptionBudget: null

# Update strategy
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1
