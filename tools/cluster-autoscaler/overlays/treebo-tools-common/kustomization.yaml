namespace: kube-system

helmCharts:
  - name: cluster-autoscaler
    repo: https://kubernetes.github.io/autoscaler
    version: 9.43.2
    releaseName: cluster-autoscaler
    namespace: kube-system
    valuesFile: values.yaml
    includeCRDs: true

resources:
  - configmap-expander-priorities.yaml
  - pdb.yaml

patches:
  - target:
      kind: ServiceAccount
      name: cluster-autoscaler-sa
    patch: |-
      - op: replace
        path: /metadata/annotations/eks.amazonaws.com~1role-arn
        value: arn:aws:iam::************:role/role-cluster-autoscaler-treebo-tools-common
