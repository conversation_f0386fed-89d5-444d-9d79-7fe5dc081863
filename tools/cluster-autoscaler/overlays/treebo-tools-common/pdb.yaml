apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cluster-autoscaler-pdb
  namespace: kube-system
  annotations:
    # Prevent cluster autoscaler from scaling down nodes with this PDB
    cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
spec:
  maxUnavailable: 0 # Never allow cluster autoscaler to be unavailable
  selector:
    matchLabels:
      app.kubernetes.io/name: cluster-autoscaler
