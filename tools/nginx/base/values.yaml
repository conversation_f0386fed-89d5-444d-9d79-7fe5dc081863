controller:
  service:
    type: ClusterIP

  # allowing snippet annotations is a security risk but gives some flexibility
  # eg: disabling cors in typesense etc
  # since we own the IAC for this its okay to enable this
  # still there's a risk level lever which prevents critical annotations
  # from being added like configuration snippets etc
  allowSnippetAnnotations: true
  # Affinity and tolerations will be added via kustomization patches
  metrics:
    enabled: true
  progressDeadlineSeconds: 600
  podAnnotations:
    prometheus.io/port: "10254"
    prometheus.io/scrape: "true"

defaultBackend:
  enabled: true
  # Affinity and tolerations will be added via kustomization patches
