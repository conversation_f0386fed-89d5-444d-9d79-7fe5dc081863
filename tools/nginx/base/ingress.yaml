apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: alb-to-nginx
  labels:
    external-dns: "enabled"
  annotations:
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz"
    alb.ingress.kubernetes.io/healthcheck-protocol: "HTTP"
    alb.ingress.kubernetes.io/healthcheck-port: "10254"
spec:
  ingressClassName: alb # This is the only class that refers alb. rest should be nginx
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nginx-ingress-nginx-controller
                port:
                  number: 80
    - http: # duplicated entry for two hosts entry
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nginx-ingress-nginx-controller
                port:
                  number: 80
