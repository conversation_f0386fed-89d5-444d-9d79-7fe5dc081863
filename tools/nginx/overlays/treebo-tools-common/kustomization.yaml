resources:
  - ../../base

patches:
  - target:
      kind: Ingress
      name: alb-to-nginx
    # If the annotation name itself has / replace it with ~1
    # https://github.com/kubernetes-sigs/kustomize/issues/1439#issuecomment-1294919898
    patch: |-
      - op: add
        path: /spec/rules/0/host
        value: "*.ekscraving1775.treebo.com"
      - op: add
        path: /spec/rules/1/host
        value: "ekscraving1775.treebo.com"
      - op: add
        path: /metadata/annotations/alb.ingress.kubernetes.io~1load-balancer-name
        value: "eks-tools-common-shared-alb-1" # cannot be more than 32 chars
      - op: add
        path: /metadata/annotations/alb.ingress.kubernetes.io~1group.name
        value: "shared-alb-1"
      - op: add
        path: /metadata/annotations/external-dns.alpha.kubernetes.io~1hostname
        value: "*.ekscraving1775.treebo.com,ekscraving1775.treebo.com"