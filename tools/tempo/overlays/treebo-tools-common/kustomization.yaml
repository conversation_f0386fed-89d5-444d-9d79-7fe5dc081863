resources:
  - ../../base
  # - ingress.yaml

namespace: monitoring

patches:
  - target:
      kind: ServiceAccount
      name: tempo
    patch: |-
      - op: replace
        path: /metadata/annotations/eks.amazonaws.com~1role-arn
        value: "arn:aws:iam::************:role/role-eks-s3-tempo-treebo-v1"
  - target:
      kind: Ingress
      name: tempo
    patch: |-
      - op: add
        path: /spec/rules/0/host
        value: tempo.ekscraving1775.treebo.com
