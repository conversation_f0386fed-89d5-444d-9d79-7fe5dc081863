serviceAccount:
  name: tempo
  annotations:
    eks.amazonaws.com/role-arn: $(placeholder)

ingester:
  persistence:
    enabled: true
    size: 2Gi
    storageClass: ebs-sc

metricsGenerator:
  #  remoteWriteUrl: "http://prometheus-kube-prometheus-prometheus.monitoring:9090/api/v1/write"
  enabled: true
  config:
    storage:
      remote_write:
        - url: http://mimir-gateway.monitoring.svc.cluster.local/api/v1/push
          headers:
            X-Scope-OrgID: "tempo-metrics-generator"
  persistence:
    enabled: true
    size: 2Gi
    storageClass: ebs-sc

queryFrontend:
  config:
    metrics:
      max_duration: 0

distributor:
  replicas: 2
  # # todo: disable in production
  # config:
  #   log_received_spans:
  #     enabled: true
  #     include_all_attributes: false
  #     filter_by_status_error: false
  #   log_discarded_spans:
  #     enabled: true
  #     include_all_attributes: false
  #     filter_by_status_error: false

compactor:
  replicas: 2
  config:
    compaction:
      # https://community.grafana.com/t/how-to-enable-data-retention-for-30-days-in-grafana-tempo/88484/2
      block_retention: 2160h
  persistence:
    enabled: true
    size: 2Gi
    storageClass: ebs-sc

querier:
  replicas: 2

storage:
  trace:
    backend: s3
    s3:
      bucket: s3-tempo-treebo-v1
      endpoint: s3.ap-south-1.amazonaws.com
      region: ap-south-1

ingress:
  enabled: true
  ingressClassName: nginx
  hosts:
    - $(placeholder)

traces:
  otlp:
    http:
      enabled: true
    grpc:
      enabled: true
