vpa:
  enabled: true
  updater:
    enabled: false # Disable updater to prevent automatic changes

controller:
  enabled: true
  resources:
    requests:
      cpu: 25m
      memory: 256Mi
    limits:
      cpu: 100m
      memory: 512Mi

dashboard:
  enabled: true

  ingress:
    enabled: true
    ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    hosts:
      - host: goldilocks.ekscraving1775.treebo.com
        paths:
          - path: /
            type: Prefix

  resources:
    requests:
      cpu: 25m
      memory: 256Mi
    limits:
      cpu: 100m
      memory: 512Mi
