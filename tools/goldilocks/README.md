# Goldilocks

Goldilocks is a utility that can help you identify a starting point for resource requests and limits by using the Vertical Pod Autoscaler (VPA) in recommendation mode. It provides a dashboard to visualize VPA recommendations for your workloads.

## Overview

Goldilocks helps you:

- **Right-size workloads**: Get VPA-based recommendations for CPU and memory
- **Optimize resource allocation**: Identify over/under-provisioned workloads  
- **Improve cluster efficiency**: Better resource utilization across the cluster
- **Cost optimization**: Reduce waste from over-provisioned resources
- **Performance optimization**: Prevent under-provisioning issues

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Metrics Server │◄───│  VPA Recommender│◄───│   Goldilocks    │
│                 │    │  (recommendation │    │   Controller    │
└─────────────────┘    │     mode only)   │    └─────────────────┘
                       └─────────────────┘              │
                                 │                      ▼
                                 ▼               ┌─────────────────┐
                       ┌─────────────────┐       │   Goldilocks    │
                       │ VPA Recommendations│     │   Dashboard     │
                       │   (CRDs)        │       │ (Web Interface) │
                       └─────────────────┘       └─────────────────┘
```

## Directory Structure

```
tools/goldilocks/
├── base/                           # Base Goldilocks configuration
│   ├── kustomization.yaml         # Base kustomization
│   ├── namespace.yaml             # Goldilocks namespace
│   └── values.yaml                # Base Helm values
├── overlays/
│   └── treebo-tools-common/       # Tools cluster configuration
│       ├── kustomization.yaml    # Overlay kustomization
│       └── values.yaml           # Environment-specific values
└── README.md                      # This file
```

## Configuration

### Base Configuration

The base configuration (`base/values.yaml`) provides:
- VPA recommender installation (updater and admission controller disabled for safety)
- Goldilocks controller and dashboard
- Basic resource allocation and security contexts
- Metrics export configuration

### Environment-Specific Configuration

The treebo-tools-common overlay (`overlays/treebo-tools-common/values.yaml`) adds:
- Enhanced resource allocation for tools cluster
- Ingress configuration with basic auth
- TLS termination with cert-manager
- ServiceMonitor for Prometheus integration
- Environment-specific labels and annotations

## Prerequisites

1. **Metrics Server**: Required for VPA to function
2. **Cert-manager**: For TLS certificate management
3. **Nginx Ingress**: For web UI access
4. **External Secrets**: For basic auth credentials (optional)

## VPA Safety Configuration

**Important**: This installation only includes the VPA recommender component:
- ✅ **VPA Recommender**: Enabled (provides recommendations)
- ❌ **VPA Updater**: Disabled (would automatically update resource requests)
- ❌ **VPA Admission Controller**: Disabled (would modify pods at creation time)

This ensures VPA only provides recommendations without automatically modifying your workloads.

## Usage

### 1. Enable Namespace Monitoring

To get recommendations for a namespace, label it:

```bash
kubectl label namespace <namespace-name> goldilocks.fairwinds.com/enabled=true
```

### 2. Access Dashboard

Once deployed, Goldilocks will be available at:
- **URL**: https://goldilocks.ekscraving1775.treebo.com
- **Authentication**: Basic auth (configured via external secret)

### 3. View Recommendations

The dashboard shows:
- Current resource requests vs recommendations
- QoS (Quality of Service) class recommendations
- Historical resource usage patterns
- Cost impact of recommendations

### 4. Apply Recommendations

Review recommendations and manually apply them to your workloads:

```yaml
# Example: Apply VPA recommendations to a deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  template:
    spec:
      containers:
      - name: app
        resources:
          requests:
            cpu: 100m      # From VPA recommendation
            memory: 256Mi  # From VPA recommendation
          limits:
            cpu: 500m      # Based on recommendation + buffer
            memory: 512Mi  # Based on recommendation + buffer
```

## Monitoring

Goldilocks exports metrics to Prometheus:
- VPA recommendation accuracy
- Resource recommendation trends
- Dashboard usage metrics

## Common Workflows

### 1. New Application Sizing
1. Deploy application with minimal resources
2. Label namespace for Goldilocks monitoring
3. Wait 24-48 hours for VPA to collect data
4. Review recommendations in dashboard
5. Apply recommended resource requests/limits

### 2. Existing Application Optimization
1. Label existing application namespaces
2. Compare current allocations vs recommendations
3. Identify over/under-provisioned workloads
4. Gradually apply recommendations during maintenance windows

### 3. Cost Optimization
1. Use dashboard to identify over-provisioned workloads
2. Calculate potential cost savings
3. Apply recommendations to reduce resource waste
4. Monitor application performance after changes

## Troubleshooting

### Common Issues

1. **No recommendations**: Ensure namespace is labeled and has running workloads
2. **VPA not working**: Check metrics-server is running and healthy
3. **Dashboard not accessible**: Verify ingress configuration and DNS

### Useful Commands

```bash
# Check VPA recommendations
kubectl get vpa -A

# Check Goldilocks controller logs
kubectl logs -n goldilocks deployment/goldilocks-controller

# Check VPA recommender logs
kubectl logs -n goldilocks deployment/goldilocks-vpa-recommender

# List enabled namespaces
kubectl get namespaces -l goldilocks.fairwinds.com/enabled=true
```
