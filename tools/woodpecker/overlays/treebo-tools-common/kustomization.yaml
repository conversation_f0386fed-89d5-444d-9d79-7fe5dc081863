apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: woodpecker

resources:
  - ../../base
  - external-secret.yaml
  - custom-css-configmap.yaml

patches:
  # Set agent service account role ARN for ECR access
  - target:
      kind: ServiceAccount
      name: woodpecker-agent-sa
    patch: |-
      - op: replace
        path: /metadata/annotations/eks.amazonaws.com~1role-arn
        value: "arn:aws:iam::************:role/role-woodpecker-agent-treebo-tools-common"

  # Mount custom CSS ConfigMap to server
  - path: server-css-patch.yaml
