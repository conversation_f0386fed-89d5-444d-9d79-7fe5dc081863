apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: woodpecker-secrets
  namespace: woodpecker
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: woodpecker-secrets
    creationPolicy: Owner
  data:
    - secretKey: WOODPECKER_AGENT_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/woodpecker-secrets
        property: agentSecret
    - secretKey: WOODPECKER_GITHUB_CLIENT
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/woodpecker-secrets
        property: githubClientId
    - secretKey: WOODPECKER_GITHUB_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/woodpecker-secrets
        property: githubClientSecret
    - secretKey: WOODPECKER_DATABASE_DATASOURCE
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/woodpecker-secrets
        property: databaseUrl
