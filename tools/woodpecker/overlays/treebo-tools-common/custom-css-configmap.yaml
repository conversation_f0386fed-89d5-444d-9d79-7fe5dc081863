apiVersion: v1
kind: ConfigMap
metadata:
  name: woodpecker-custom-css
  namespace: woodpecker
  labels:
    app.kubernetes.io/name: woodpecker
    app.kubernetes.io/component: server
data:
  custom.css: |
    /* Treebo Woodpecker Custom Theme Variables */
    @layer theme {
      :root, :host {
        /* Treebo Custom Color Palette */
        --wp-background-100: #FFFFFF !important;
        --wp-background-200: #F0FDF4 !important;
        --wp-background-300: #FFFFFF !important;
        --wp-background-400: #F3F4F6 !important;
        --wp-text-100: #14532D !important;
        --wp-text-alt-100: #374151 !important;
        --wp-text-200: #6B7280 !important;

        /* Material Design Text Colors - Tailwind Slate */
        --color-wp-text-100: #0f172a !important;  /* slate-900 - Primary text */
        --color-wp-text-200: #334155 !important;  /* slate-700 - Secondary text */
        --color-wp-text-300: #64748b !important;  /* slate-500 - Disabled text */
        --color-wp-text-hint: #94a3b8 !important; /* slate-400 - Hint text */
        --color-wp-text-divider: #e2e8f0 !important; /* slate-200 - Dividers */
        --wp-primary-200: #059669 !important;
        --wp-primary-300: #059669 !important;
        --wp-primary-text-100: #FFFFFF !important;
        --wp-control-neutral-100: #F3F4F6 !important;
        --wp-control-neutral-200: #D1D5DB !important;
        --wp-control-neutral-300: #6B7280 !important;
        --wp-control-info-100: #059669 !important;
        --wp-control-info-200: #047857 !important;
        --wp-control-info-300: #065F46 !important;
        --wp-control-ok-100: #10B981 !important;
        --wp-control-ok-200: #059669 !important;
        --wp-control-ok-300: #047857 !important;
        --wp-error-100: #DC2626 !important;
        --wp-error-200: #B91C1C !important;
        --wp-error-300: #991B1B !important;
        --wp-state-neutral-100: #6B7280 !important;
        --wp-state-ok-100: #10B981 !important;
        --wp-state-info-100: #059669 !important;
        --wp-state-warn-100: #F59E0B !important;
        --wp-hint-warn-100: #FEF3C7 !important;
        --wp-hint-warn-200: #FDE047 !important;
        --wp-code-100: #F3F4F6 !important;
        --wp-code-text-alt-100: #374151 !important;
        --wp-link-100: #059669 !important;
        --wp-link-200: #047857 !important;

        /* Override internal Woodpecker variables with Treebo colors */
        --color-int-wp-primary-300: #10B981 !important;
        --color-int-wp-primary-400: #059669 !important;
        --color-int-wp-primary-500: #047857 !important;
        --color-int-wp-control-ok-100: #10B981 !important;
        --color-int-wp-control-ok-200: #059669 !important;
        --color-int-wp-control-ok-300: #047857 !important;
        --color-int-wp-control-info-100: #059669 !important;
        --color-int-wp-control-info-200: #047857 !important;
        --color-int-wp-control-info-300: #065F46 !important;
        --color-int-wp-error-100: #DC2626 !important;
        --color-int-wp-error-200: #B91C1C !important;
        --color-int-wp-error-300: #991B1B !important;
        --color-int-wp-state-ok-100: #10B981 !important;
        --color-int-wp-state-info-100: #059669 !important;
        --color-int-wp-state-warn-100: #F59E0B !important;
      }
    }

    /* Force override with higher specificity */
    html:root, html:host, body:root, body:host {
      /* Font override */
      --font-sans: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Noto Sans, Liberation Sans, Arial, sans-serif !important;
      --default-font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Noto Sans, Liberation Sans, Arial, sans-serif !important;

      /* Material Design Text Colors - Tailwind Slate - High Priority Override */
      --color-wp-text-100: #0f172a !important;  /* slate-900 */
      --color-wp-text-200: #334155 !important;  /* slate-700 */
      --color-wp-text-300: #64748b !important;  /* slate-500 */
      --color-wp-text-hint: #94a3b8 !important; /* slate-400 */
      --color-wp-text-divider: #e2e8f0 !important; /* slate-200 */

      /* Core color overrides */
      --color-blue-400: #10B981 !important;
      --color-blue-500: #059669 !important;
      --color-blue-600: #047857 !important;
      --color-blue-700: #065F46 !important;
      --color-green-400: #10B981 !important;
      --color-green-500: #059669 !important;
      --color-green-600: #047857 !important;

      /* Common text color overrides - Tailwind Slate */
      --color-gray-900: #0f172a !important;  /* slate-900 */
      --color-gray-800: #1e293b !important;  /* slate-800 */
      --color-gray-700: #334155 !important;  /* slate-700 */
      --color-gray-600: #475569 !important;  /* slate-600 */
      --color-gray-500: #64748b !important;  /* slate-500 */
      --color-gray-400: #94a3b8 !important;  /* slate-400 */
      --color-gray-300: #cbd5e1 !important;  /* slate-300 */
      --color-gray-200: #e2e8f0 !important;  /* slate-200 */
      --color-gray-100: #f1f5f9 !important;  /* slate-100 */

      /* Woodpecker theme variables with maximum priority */
      --wp-background-100: #FFFFFF !important;
      --wp-background-200: #F0FDF4 !important;
      --wp-background-300: #D1FAE5 !important;
      --wp-primary-200: #059669 !important;
      --wp-primary-300: #059669 !important;
      --wp-control-ok-100: #10B981 !important;
      --wp-control-info-100: #059669 !important;
      --wp-state-ok-100: #10B981 !important;
      --wp-state-info-100: #059669 !important;
      --wp-error-100: #DC2626 !important;
      --wp-state-warn-100: #F59E0B !important;
    }

    main {
      background: #f8f9fa !important;
    }

    /* Typography - Headings */
    h1 {
      font-weight: var(--font-weight-semi-bold, 600) !important;
    }

    h2 {
      font-weight: var(--font-weight-semi-bold, 600) !important;
      text-transform: uppercase !important;
    }

    /* Base styles for all buttons and anchor links */
    button,
    {
      /* Display & Layout */
      display: inline-flex; /* Allows content to be centered and padding to work */
      align-items: center;
      justify-content: center;
      padding: 10px 20px; /* Standard padding */
      border: 1px solid var(--wp-control-neutral-100) !important; /* Border matching primary color */
      border-radius: 5px; /* Slightly rounded corners */

      /* Text & Font */
      font-family: sans-serif; /* Common clean font */
      text-decoration: none; /* Remove underline for links by default */

      /* Colors - Default State */
      background-color: var(--wp-background-100) !important; /* White background */
      color: var(--color-wp-text-100) !important; /* Primary text color */

      /* Transitions for smooth hover effects */
      transition: background-color 0.2s ease-in-out,
                  color 0.2s ease-in-out,
                  border-color 0.2s ease-in-out;

      /* Cursor */
      cursor: pointer !important; /* Indicate clickability */
      user-select: none; /* Prevent text selection on click */
    }

    /* Hover state for buttons and anchor links */
    button:hover {
      background-color: var(--wp-control-neutral-100) !important; /* Darker primary background on hover */
      color: var(--color-wp-text-100) !important; /* White text on hover (inverted) */
      border-color: var(--wp-control-neutral-200) !important; /* Border matches hover background */
    }

    /* Focus state for accessibility */
    button:focus {
      outline: none; /* Remove default outline */
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); /* Primary color glow */
    }

    /* Active/Clicked state */
    button:active{
      transform: translateY(1px); /* Slight press effect */
    }
