# Woodpecker CI Helm values
# Configuration for both server and agent components

server:
  statefulSet:
    replicaCount: 1

  env:
    WOODPECKER_ADMIN: "arunmiddha,thulasi-ram"
    WOODPECKER_ORGS: treebo-noss
    WOODPECKER_HOST: "https://woodpecker.ekscraving1775.treebo.com"
    WOODPECKER_OPEN: true
    WOODPECKER_LOGS_DEBUG: "true"
    WOODPECKER_LOGS_TRACE: "true"
    WOODPECKER_LOGS_COLOR: "true"
    WOODPECKER_DATABASE_DRIVER: postgres
    WOODPECKER_GITHUB: true
    WOODPECKER_REPO_OWNERS: treebo-noss
    # Trust thulasi503/docker-buildx until https://codeberg.org/woodpecker-plugins/docker-buildx/issues/266 is fixed
    WOODPECKER_PLUGINS_PRIVILEGED: woodpeckerci/plugin-docker-buildx,thulasi503/docker-buildx
    WOODPECKER_CUSTOM_CSS_FILE: "/var/lib/woodpecker/custom.css"

  extraSecretNamesForEnvFrom:
    - woodpecker-secrets

  persistentVolume:
    enabled: true
    size: 10Gi
    storageClass: ebs-sc

  ingress:
    enabled: true
    ingressClassName: nginx
    hosts:
      - host: woodpecker.ekscraving1775.treebo.com
        paths:
          - path: /
            backend:
              serviceName: woodpecker-server
              servicePort: 80
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      nginx.ingress.kubernetes.io/enable-access-log: "true"
      nginx.ingress.kubernetes.io/enable-rewrite-log: "true"

  serviceAccount:
    create: true
    name: woodpecker-server-sa

  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

agent:
  replicaCount: 2

  env:
    WOODPECKER_SERVER: "woodpecker-server:9000"
    WOODPECKER_BACKEND: kubernetes
    WOODPECKER_BACKEND_K8S_NAMESPACE: woodpecker
    WOODPECKER_BACKEND_K8S_STORAGE_CLASS: "ebs-sc" # EFS seems to be the better opion
    WOODPECKER_BACKEND_K8S_VOLUME_SIZE: 1G
    WOODPECKER_BACKEND_K8S_STORAGE_RWX: false # ebs-sc does not support RWX - ReadWriteMany

  extraSecretNamesForEnvFrom:
    - woodpecker-secrets

  persistence:
    enabled: true
    size: 2Gi
    storageClass: ebs-sc
    mountPath: "/etc/woodpecker"

  serviceAccount:
    create: true
    name: woodpecker-agent-sa

  rbac:
    create: true

  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 128Mi
