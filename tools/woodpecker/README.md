# Woodpecker CI

Woodpecker is a simple, yet powerful CI/CD engine with great extensibility. It focuses on executing pipelines inside containers, either locally or on any container platform like Kubernetes.

## Overview

This setup includes:
- **Woodpecker Server**: Web interface and API server
- **Woodpecker Agent**: Executes CI/CD pipelines in Kubernetes pods
- **Kubernetes Backend**: Runs pipeline steps as Kubernetes jobs
- **ECR Integration**: Supports pushing/pulling from AWS ECR

## Architecture

- **Server Component**: StatefulSet with persistent storage for data
- **Agent Component**: Deployment with multiple replicas for scalability
- **RBAC**: Proper Kubernetes permissions for agent to create pods/jobs
- **Ingress**: External access via nginx ingress controller
- **External Secrets**: Integration with Vault for sensitive configuration

## Configuration

### Server Configuration
- Runs on port 8000 (web UI) and 9000 (gRPC for agents)
- Persistent storage for database and configuration
- Ingress configured for external access
- Admin users configurable via environment variables

### Agent Configuration
- Connects to server via gRPC on port 9000
- Uses Kubernetes backend to execute pipelines
- Configurable storage class and volume sizes
- RBAC permissions to create pods and jobs in the namespace

### Secrets Required

The following secrets need to be configured in Vault:

```bash
# Agent secret for server-agent communication
WOODPECKER_AGENT_SECRET=<random-secret>

# GitHub OAuth (if using GitHub)
WOODPECKER_GITHUB_CLIENT=<github-oauth-client-id>
WOODPECKER_GITHUB_SECRET=<github-oauth-client-secret>

# Gitea OAuth (if using Gitea)
WOODPECKER_GITEA_CLIENT=<gitea-oauth-client-id>
WOODPECKER_GITEA_SECRET=<gitea-oauth-client-secret>
WOODPECKER_GITEA_URL=<gitea-instance-url>
```

## Deployment

1. **Configure secrets in Vault** under `secret/data/woodpecker`
2. **Apply the configuration**:
   ```bash
   kubectl apply -k tools/woodpecker/overlays/treebo-tools-common
   ```
3. **Access the web interface** at the configured ingress URL
4. **Configure your first repository** through the web UI

## IAM Permissions

The included `policy.json` provides ECR access permissions for:
- Authenticating with ECR
- Pulling base images
- Pushing built images

Attach this policy to the appropriate IAM role for the agent service account.

## Monitoring

Woodpecker supports Prometheus metrics on port 9001. To enable:

1. Add metrics configuration to values.yaml
2. Configure ServiceMonitor for Prometheus discovery
3. Set up alerts for pipeline failures and agent health

## Troubleshooting

### Agent Connection Issues
- Check agent logs: `kubectl logs -n woodpecker -l app.kubernetes.io/name=woodpecker-agent`
- Verify server accessibility from agent pods
- Ensure WOODPECKER_AGENT_SECRET matches between server and agent

### Pipeline Execution Issues
- Check RBAC permissions for agent service account
- Verify storage class availability
- Check resource quotas in the namespace

### Storage Issues
- Ensure EBS CSI driver is installed
- Verify storage class `ebs-sc` exists
- Check PVC status and events

## Links

- [Woodpecker CI Documentation](https://woodpecker-ci.org/docs/)
- [Helm Chart Repository](https://github.com/woodpecker-ci/helm)
- [Kubernetes Backend Documentation](https://woodpecker-ci.org/docs/administration/backends/kubernetes)
