```
aws ssm put-parameter \
  --name "/treebo/production/eks/cluster-treebo-tools-common/mariadb" \
  --type "SecureString" \
  --value "{\"root_password\":\"$(openssl rand -base64 16)\",\"user_password\":\"$(openssl rand -base64 16)\"}" \
  --description "MariaDB credentials for tools cluster"
```

** IMPORTANT **

Mysql does not come configured by default with the passwords set. You will have to connect to the pod and set the passwords manually.
exec into pod

```
mysql -u root
```

Initially root doesnt have a password and follow next steps. Keep the passwords ready from secrets

1. Set Root Password:

```
-- Set root password for localhost
ALTER USER 'root'@'localhost' IDENTIFIED BY 'your-root-password-here';

-- Set root password for remote connections (if root@'%' exists)
ALTER USER 'root'@'%' IDENTIFIED BY 'your-root-password-here';

-- Alternative method using SET PASSWORD
SET PASSWORD FOR 'root'@'localhost' = PASSWORD('your-root-password-here');
```

2. <PERSON><PERSON> and Set tools_user Password:

```
-- Create tools_user if it doesn't exist
CREATE USER IF NOT EXISTS 'tools_user'@'%' IDENTIFIED BY 'your-user-password-here';

-- If user already exists, change password
ALTER USER 'tools_user'@'%' IDENTIFIED BY 'your-user-password-here';

-- Alternative method
SET PASSWORD FOR 'tools_user'@'%' = PASSWORD('your-user-password-here');
```

3. Create Databases and Grant Permissions:

```
-- Create databases for your tools
CREATE DATABASE IF NOT EXISTS tools_shared CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS devlake CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS metabase CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Grant permissions to tools_user
GRANT ALL PRIVILEGES ON tools_shared.* TO 'tools_user'@'%';
GRANT ALL PRIVILEGES ON devlake.* TO 'tools_user'@'%';
GRANT ALL PRIVILEGES ON metabase.* TO 'tools_user'@'%';

-- Apply all changes
FLUSH PRIVILEGES;
```
