apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: mariadb-secrets
  namespace: mariadb
  labels:
    app.kubernetes.io/name: mariadb
    app.kubernetes.io/component: credentials
    environment: production
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: mariadb-credentials
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        mariadb-root-password: "{{ .root_password }}"
        mariadb-password: "{{ .user_password }}"
        mariadb-replication-password: "{{ .user_password }}"
  data:
    - secretKey: root_password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/mariadb
        property: root_password
    - secretKey: user_password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/mariadb
        property: user_password
