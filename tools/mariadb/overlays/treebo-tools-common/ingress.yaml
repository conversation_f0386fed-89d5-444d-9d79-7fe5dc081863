apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mariadb-ingress
  namespace: mariadb
  labels:
    app.kubernetes.io/name: mariadb
    app.kubernetes.io/component: ingress
    environment: tools
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    # nginx.ingress.kubernetes.io/rate-limit: "100"
    # nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - mariadb.ekscraving1775.treebo.com
      secretName: mariadb-tls
  rules:
    - host: mariadb.ekscraving1775.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mariadb
                port:
                  number: 3306
