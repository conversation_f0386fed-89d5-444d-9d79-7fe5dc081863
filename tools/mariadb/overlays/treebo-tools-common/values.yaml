auth:
  username: "tools_user"
  database: "tools_shared"
  existingSecret: "mariadb-credentials"

architecture: standalone

serviceBindings:
  enabled: false

networkPolicy:
  enabled: false

primary:
  persistence:
    enabled: true
    storageClass: "ebs-sc"
    size: 5Gi

  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

  # Custom liveness probe to fix deprecated mysqladmin warning
  customLivenessProbe:
    exec:
      command:
        - /bin/bash
        - -ec
        - /opt/bitnami/mariadb/bin/mariadb-admin ping -u root -p"${MARIADB_ROOT_PASSWORD}"
    initialDelaySeconds: 120
    periodSeconds: 10
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 3

  # Custom readiness probe to match
  customReadinessProbe:
    exec:
      command:
        - /bin/bash
        - -ec
        - /opt/bitnami/mariadb/bin/mariadb-admin ping -u root -p"${MARIADB_ROOT_PASSWORD}"
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 3

metrics:
  # metrics has some issue with helmfile where it hardcodes address as localhost
  enabled: true

  resources:
    requests:
      memory: "64Mi"
      cpu: "25m"
    limits:
      memory: "256Mi"
      cpu: "100m"
  serviceMonitor:
    enabled: true
    interval: 60s
    scrapeTimeout: 30s
    labels:
      app.kubernetes.io/name: mariadb
      environment: production

  extraArgs:
    primary:
      - --collect.info_schema.tables
      - --collect.global_status
      - --collect.global_variables
  # prometheusRule:
  #   enabled: true
  #   rules:
  #     - alert: MariaDBDown
  #       expr: mysql_up == 0
  #       for: 5m
  #       labels:
  #         severity: critical
  #       annotations:
  #         summary: "MariaDB instance is down"
  #     - alert: MariaDBHighConnections
  #       expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
  #       for: 10m
  #       labels:
  #         severity: warning
  #       annotations:
  #         summary: "MariaDB high connection usage"
