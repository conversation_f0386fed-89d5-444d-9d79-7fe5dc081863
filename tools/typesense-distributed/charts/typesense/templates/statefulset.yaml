apiVersion: apps/v1
kind: StatefulSet 
metadata:
  name: {{ template "typesense.fullname" . }}
  labels: {{- include "typesense.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels: {{- include "typesense.matchLabels" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  podManagementPolicy: Parallel
  serviceName: {{ template "typesense.fullname" . }}-cluster
  template:
    metadata:
      labels: {{- include "typesense.labels" . | nindent 8 }}
    spec:
      volumes:
        - name: config-nodes
          configMap:
            name: {{ template "typesense.fullname" . }}
            items:
              - key: nodes
                path: nodes
      affinity: {{- toYaml .Values.affinity | nindent 8 }}
      containers:
        - name: typesense
          image: {{ template "typesense.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: TYPESENSE_DATA_DIR
              value: {{ .Values.persistence.path }}
            # TreeboEdit: now is set in patch
            # This conflicts with patch, such that value and valueFrom are both set in out manifest
            # and kustomize fails
            # - name: TYPESENSE_API_KEY
            #   value: {{ .Values.apiKey }}
          ports:
            - name: http
              containerPort: {{ .Values.applicationPort }}
            - name: peering
              containerPort: {{ .Values.peeringPort }}
          args:
            - --data-dir
            - /data
            - --api-key
            - {{ .Values.apiKey }}
            - --listen-port
            - "{{ .Values.applicationPort }}"
            {{- if ne (toString .Values.replicas) "1" }}
            - --nodes
            - /etc/typesense/nodes/nodes
            - --reset-peers-on-error
            - --healthy-read-lag
            - '3600'
            {{- end }}
          livenessProbe:
              httpGet:
                path: /health
                port: http
              initialDelaySeconds: 120 
              timeoutSeconds: 5
              failureThreshold: 6
          readinessProbe:
              httpGet:
                path: /health
                port: http
              initialDelaySeconds: 30
              timeoutSeconds: 5
              failureThreshold: 6
          volumeMounts:
            - name: data  
              mountPath: {{ .Values.persistence.path }}
            - name: config-nodes
              mountPath: /etc/typesense/nodes
  volumeClaimTemplates:  
  # The below nodes are modified and not from the github repository
    - metadata:
        name: data  
      spec:
        accessModes: [ "ReadWriteOnce" ]
        resources:
          requests:
            storage: {{ .Values.persistence.size }}
        storageClassName: {{ .Values.persistence.storageClass }}

