apiVersion: v1
kind: Service
metadata:
  name: {{ template "typesense.fullname" . }}-cluster
  labels: {{- include "typesense.labels" . | nindent 4 }}
  {{- if .Values.service.annotations }}
  annotations: {{- include "typesense.tplValue" ( dict "value" .Values.service.annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  clusterIP: "None"
  {{- if and (eq .Values.service.type "LoadBalancer") (not (empty .Values.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  publishNotReadyAddresses: true
  ports:
    - name: http
      port: {{ .Values.service.port }}
      targetPort: http
      {{- if and (or (eq .Values.service.type "nodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePort)) }}
      nodePort: {{ .Values.service.nodePort  }}
      {{- else if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    - name: peering
      port: {{ .Values.service.peeringPort }}
      targetPort: peering
      {{- if and (or (eq .Values.service.type "nodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.peeringNodePort)) }}
      nodePort: {{ .Values.service.peeringNodePort  }}
      {{- else if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
  selector: {{- include "typesense.matchLabels" . | nindent 4 }}