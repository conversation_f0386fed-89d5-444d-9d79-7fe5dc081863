apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: typesense-secrets
  namespace: typesense
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: typesense-secret-store
    kind: ClusterSecretStore
  target:
    name: typesense-secrets
    creationPolicy: Owner
  data:
    - secretKey: TYPESENSE_API_KEY
      remoteRef:
        key: treebo/staging/apse1-cluster/apps/authz/postgres
        property: schema_name
