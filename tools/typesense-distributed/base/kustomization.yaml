metadata:
  annotations:
    argocd.argoproj.io/sync-wave: "5"

helmGlobals:
  chartHome: ../charts

namespace: typesense-distributed

helmCharts:
  # from docs: https://kubectl.docs.kubernetes.io/references/kustomize/builtins/#usage-via-kustomizationyaml-11
  # helmGlobals.chartHome: a file path, relative to the kustomization root, to a directory containing a subdirectory for each chart to be included in the kustomization. 
  # The default value of this field is “charts”. 
  # So, for example, kustomize looks for the minecraft chart at {kustomizationRoot}/{ChartHome}/minecraft. 
  # If the chart is there at build time, kustomize will use it as found, and not check version numbers or dates. 
  # If the chart is not there, kustomize will attempt to pull it using the version number specified in the kustomization file, and put it there. 
  # To suppress the pull attempt, simply assure that the chart is already there.
  - name: typesense
    # repo: https://marta-barea.github.io/typesense-helm/
    version: 1.0.14
    releaseName: typesense
    valuesFile: values.yaml
resources:
  - namespace.yaml
  - ingress.yaml

patchesStrategicMerge:
  - patch-env.yaml