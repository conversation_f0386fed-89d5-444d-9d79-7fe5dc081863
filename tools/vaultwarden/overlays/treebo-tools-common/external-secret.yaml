apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: vaultwarden-external-secret
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: vaultwarden-secrets
    creationPolicy: Owner
  data:
    - secretKey: ADMIN_TOKEN
      remoteRef:
        key: treebo/production/eks/cluster-treebo-tools-common/vaultwarden-secrets
        property: adminTokenHashed

