#!/bin/bash

# Script to build Kubernetes manifests using Kustomize and environment variable substitution.
# Requires an environment name as the first argument.

set -u
set -o pipefail

# Define constants for clarity and maintainability
readonly SCRIPT_NAME="$0"
readonly ENV_NAME="$1"
readonly ENV_SUBST_DIR="/tmp/go-envsubst"
readonly ENV_SUBST_EXECUTABLE="$ENV_SUBST_DIR/genvsubst"
readonly KUSTOMIZE_BUILD_FLAGS="--enable-helm --load-restrictor=LoadRestrictionsNone"
readonly ARGOCD_ENV_PREFIX="ARGOCD_"

# Check if the environment name is provided
if [[ -z "$ENV_NAME" ]]; then
  echo "Error: No environment name provided." >&2
  echo "Usage: $SCRIPT_NAME <environment-name>" >&2
  exit 1
fi

# Ensure the environment substitution tool exists
if [[ ! -x "$ENV_SUBST_EXECUTABLE" ]]; then
  echo "Downloading and installing envsubst..."
  mkdir -p "$ENV_SUBST_DIR"
  curl -sSL "https://github.com/a8m/envsubst/releases/download/v1.4.3/envsubst-$(uname -s)-$(uname -m)" -o "$ENV_SUBST_EXECUTABLE"
  chmod +x "$ENV_SUBST_EXECUTABLE"
  if [[ ! -x "$ENV_SUBST_EXECUTABLE" ]]; then
    echo "Error: Failed to download or execute envsubst." >&2
    exit 1
  fi
fi

# Determine the parent directory of the script
readonly PARENT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
readonly KUSTOMIZE_OVERLAY_PATH="$PARENT_PATH/overlays/$ENV_NAME"
readonly KUSTOMIZE_OVERLAY_INIT_PATH="$PARENT_PATH/init-setup"

# Attempt to build and substitute the manifests, capturing stderr
echo "Building Kubernetes manifests for environment: $ENV_NAME"
ERROR=$(kustomize build $KUSTOMIZE_BUILD_FLAGS "$KUSTOMIZE_OVERLAY_INIT_PATH" 2>&1 | "$ENV_SUBST_EXECUTABLE" -no-unset -no-empty 2>&1 > /dev/null)
PIPELINE_EXIT_CODE="$?"

echo "$ERROR" >&2

if [[ "$PIPELINE_EXIT_CODE" -ne 0 ]]; then
  # Check if any Argo CD environment variables (matching ARGOCD_) are present in the error output
  # -q is "quiet" mode: grep will not output matches, just return exit status
  if echo "$ERROR" | grep "$ARGOCD_ENV_PREFIX" >/dev/null; then
    echo "Error: Kustomize build or envsubst failed while Argo CD environment variables are present." >&2
    exit 1
  else
    echo "envsubst failed but no Argo CD environment variables are present. Proceeding..."
  fi
fi

echo "Applying initial setup manifests..."
kustomize build $KUSTOMIZE_BUILD_FLAGS "$KUSTOMIZE_OVERLAY_INIT_PATH" | "$ENV_SUBST_EXECUTABLE" | kubectl apply -f -

echo "Applying actual setup manifests..."
MAX_RETRIES=5
RETRY_COUNT=0

# The while loop is necessary because:
# 1. CRDs might not be immediately available after installation
# 2. There may be race conditions with webhook configurations
# 3. Some resources depend on others being fully established
# 4. Network or API server issues might cause temporary failures
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if kustomize build $KUSTOMIZE_BUILD_FLAGS "$KUSTOMIZE_OVERLAY_PATH" | kubectl apply -f -; then
    echo "✅ Successfully applied ArgoCD resources"
    break
  else
    RETRY_COUNT=$((RETRY_COUNT+1))
    if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
      echo "❌ Failed to apply ArgoCD resources after $MAX_RETRIES attempts"
      exit 1
    fi
    echo "Retrying to apply ArgoCD resources ($RETRY_COUNT/$MAX_RETRIES)..."
    sleep 5
  fi
done
