apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
helmCharts:
  - name: argo-cd
    repo: https://argoproj.github.io/argo-helm
    version: 7.8.28
    releaseName: argocd
    namespace: argocd
    valuesFile: ../../common/values.yaml
    includeCRDs: true
  - name: argo-rollouts
    repo: https://argoproj.github.io/argo-helm
    version: 2.39.5
    releaseName: argo-rollouts
    namespace: argocd
    valuesFile: ../../common/values-rollouts.yaml
    includeCRDs: true

resources:
  - ../../common

# ArgoCD is critical infrastructure - must run on reliable on-demand system nodes
components:
  - ../../../lib/common/components/system-critical

patches:
  - target:
      kind: Ingress
      name: argocd-ingress
    patch: |-
      - op: add
        path: /spec/rules/0/host
        value: argocd.ekscraving1775.treebo.com
  - target:
      kind: Ingress
      name: argocd-grpc-ingress
    patch: |-
      - op: add
        path: /spec/rules/0/host
        value: argocd-grpc.ekscraving1775.treebo.com
      - op: add
        path: /metadata/annotations/alb.ingress.kubernetes.io~1load-balancer-name
        value: "eks-tools-common-shared-alb-1"
      - op: add
        path: /metadata/annotations/alb.ingress.kubernetes.io~1group.name
        value: "shared-alb-1"
  - target:
      kind: Application
      name: app-of-apps
    patch: |-
      - op: replace
        path: /spec/source/path
        value: "manifests/argocd/cluster-treebo-tools-common"
  - target:
      kind: ExternalSecret
      name: argocd-external-secret
    patch: |-
      - op: replace
        path: /spec/data/0/remoteRef/key
        value: treebo/production/eks/cluster-treebo-tools-common/argocd-secrets
  - target:
      kind: Ingress
      name: argo-rollouts-dashboard
    patch: |-
      - op: replace
        path: /spec/rules/0/host
        value: "argo-rollouts.ekscraving1775.treebo.com"
