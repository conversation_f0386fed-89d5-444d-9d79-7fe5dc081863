apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: treebo-default-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: treebo-default-project
    app.kubernetes.io/part-of: argocd
    app.kubernetes.io/managed-by: kustomize
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
spec:
  description: "Default project for all Treebo applications with maintenance window at 2:00 AM"

  # Source repositories that applications in this project can pull from
  sourceRepos:
    - "https://github.com/treebo-noss/devops-gitops.git"
    - "https://github.com/treebo-noss/*" # Allow all Treebo repositories
    # - "https://charts.bitnami.com/bitnami"  # Bitnami Helm charts
    # - "https://kubernetes-sigs.github.io/metrics-server/"  # Metrics server
    # - "https://aws.github.io/eks-charts"  # AWS EKS charts
    # - "https://kubernetes.github.io/ingress-nginx"  # Nginx ingress
    # - "https://charts.external-secrets.io"  # External secrets
    # - "https://prometheus-community.github.io/helm-charts"  # Prometheus charts
    # - "https://grafana.github.io/helm-charts"  # Grafana charts
    # - "https://kedacore.github.io/charts"  # KEDA charts
    # - "https://vmware-tanzu.github.io/helm-charts"  # Velero charts
    # - "https://argoproj.github.io/argo-helm"  # ArgoCD charts
    # - "https://cert-manager.io/charts"  # Cert-manager charts
    # - "https://helm.releases.hashicorp.com"  # HashiCorp charts
    # - "https://charts.jetstack.io"  # Jetstack charts
    # - "https://kubernetes-sigs.github.io/aws-load-balancer-controller"  # AWS LB controller
    # - "https://kubernetes-sigs.github.io/external-dns/"  # External DNS
    - "*" # Allow all repositories (can be restricted later if needed)

  destinations:
    - namespace: "*"
      server: "https://kubernetes.default.svc"
    - namespace: "*"
      name: "in-cluster"

  clusterResourceWhitelist:
    - group: "*"
      kind: "*"

  namespaceResourceWhitelist:
    - group: "*"
      kind: "*"

  roles:
    - name: admin
      description: "Full access to all applications in this project"
      policies:
        - p, proj:treebo-default-project:admin, applications, *, treebo-default-project/*, allow
        - p, proj:treebo-default-project:admin, repositories, *, *, allow
        - p, proj:treebo-default-project:admin, clusters, *, *, allow
      groups:
        - treebo:platform-team
        - treebo:devops-team

    - name: developer
      description: "Read access to applications and ability to sync"
      policies:
        - p, proj:treebo-default-project:developer, applications, get, treebo-default-project/*, allow
        - p, proj:treebo-default-project:developer, applications, sync, treebo-default-project/*, allow
        - p, proj:treebo-default-project:developer, applications, action/*, treebo-default-project/*, allow
        - p, proj:treebo-default-project:developer, repositories, get, *, allow
      groups:
        - treebo:developers
        - treebo:backend-team
        - treebo:frontend-team

    - name: readonly
      description: "Read-only access to applications"
      policies:
        - p, proj:treebo-default-project:readonly, applications, get, treebo-default-project/*, allow
        - p, proj:treebo-default-project:readonly, repositories, get, *, allow
      groups:
        - treebo:qa-team
        - treebo:support-team

  # Sync windows - maintenance window at 2:00 AM
  syncWindows:
    - kind: deny
      schedule: "0 2 * * *" # Every day at 2:00 AM
      duration: 2h # 2-hour maintenance window (2:00 AM - 4:00 AM)
      applications:
        - "*" # Apply to all applications
      manualSync: true # Allow manual sync during maintenance window
      clusters:
        - "*" # Apply to all clusters
      namespaces:
        - "*" # Apply to all namespaces
      timeZone: "UTC" # Use UTC timezone

  # Signature keys (if using signed commits)
  signatureKeys: []

  # Orphaned resources monitoring
  orphanedResources:
    warn: true
    ignore:
      - group: ""
        kind: "Secret"
        name: "argocd-*"
      - group: ""
        kind: "ConfigMap"
        name: "argocd-*"
