apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: argocd-grpc-ingress
  namespace: argocd
  annotations:
    alb.ingress.kubernetes.io/backend-protocol: HTTPS
    # condition name must match the service name
    alb.ingress.kubernetes.io/conditions.argocd-grpc-service: >
      [{"field":"http-header","httpHeaderConfig":{"httpHeaderName": "Content-Type", "values":["application/grpc"]}}]
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/healthcheck-protocol: "HTTP"
    alb.ingress.kubernetes.io/load-balancer-name: $(load_balancer_name)
    alb.ingress.kubernetes.io/group.name: $(load_balancer_group_name)

spec:
  # This is an exception to use alb and not use nginx.
  # Because GRPC requires HTTPS and we terminate it at ALB and nginx uses only HTTP
  ingressClassName: alb
  rules:
    - host: $(argocd_host)
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: argocd-grpc-service
              port:
                number: 443