apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  name: argocd-ingress
  namespace: argocd
spec:
  ingressClassName: nginx
  rules:
    - host: $(argocd_host)
      http:
        paths:
          - path: /
            backend:
              service:
                name: argocd-server
                port:
                  number: 80
            pathType: Prefix
