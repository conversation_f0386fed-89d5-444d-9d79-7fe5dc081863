apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: argocd-external-secret
  namespace: argocd
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: argocd-secret
    creationPolicy: Merge
  data:
    - secretKey: admin.password
      remoteRef:
        key: "var_path_to_secret"
        property: adminPasswordHashed
