apiVersion: batch/v1
kind: CronJob
metadata:
  name: karpenter-scale-up
  namespace: karpenter
spec:
  schedule: "0 8 * * 1-5"   # Default: 8 AM weekdays, will be overridden by configmap
  timeZone: "Asia/Kolkata"  # Default timezone, will be overridden by configmap
  jobTemplate:
    spec:
      template:
        spec:
          priorityClassName: system-node-critical
          serviceAccount: karpenter-pauser
          tolerations:
            - key: CriticalAddonsOnly
              operator: Exists
            - key: "arch"
              operator: "Equal"
              value: "arm64"
              effect: "NoSchedule"
          volumes:
            - name: scale-script
              configMap:
                name: karpenter-scale-script
                defaultMode: 0777
          containers:
            - name: kubectl
              image: bitnami/kubectl:latest
              imagePullPolicy: IfNotPresent
              env:
                - name: NODEPOOL_PREFIXES
                  valueFrom:
                    configMapKeyRef:
                      name: karpenter-pauser
                      key: nodepool_prefixes
                - name: ORIGINAL_CPU_LIMITS
                  valueFrom:
                    configMapKeyRef:
                      name: karpenter-pauser
                      key: original_cpu_limits
                - name: NODE_DELETION_WAIT_SECONDS
                  valueFrom:
                    configMapKeyRef:
                      name: karpenter-pauser
                      key: node_deletion_wait_seconds
                - name: POD_DELETION_GRACE_PERIOD
                  valueFrom:
                    configMapKeyRef:
                      name: karpenter-pauser
                      key: pod_deletion_grace_period
              volumeMounts:
                - name: scale-script
                  mountPath: "/scripts"
              command:
                - /bin/sh
                - -c
                - /scripts/scale.sh up
          restartPolicy: OnFailure
