apiVersion: apps/v1
kind: Deployment
metadata:
  name: karpenter-pauser-api
  namespace: karpenter
  labels:
    app: karpenter-pauser-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: karpenter-pauser-api
  template:
    metadata:
      labels:
        app: karpenter-pauser-api
    spec:
      serviceAccount: karpenter-pauser
      containers:
        - name: api
          image: karpenter-pauser-api:latest # Replace with your actual image
          ports:
            - containerPort: 8080
          env:
            - name: PORT
              valueFrom:
                configMapKeyRef:
                  name: karpenter-pauser
                  key: api_port
            - name: NODEPOOL_PREFIXES
              valueFrom:
                configMapKeyRef:
                  name: karpenter-pauser
                  key: nodepool_prefixes
            - name: ORIGINAL_CPU_LIMITS
              valueFrom:
                configMapKeyRef:
                  name: karpenter-pauser
                  key: original_cpu_limits
            - name: NODE_DELETION_WAIT_SECONDS
              valueFrom:
                configMapKeyRef:
                  name: karpenter-pauser
                  key: node_deletion_wait_seconds
            - name: POD_DELETION_GRACE_PERIOD
              valueFrom:
                configMapKeyRef:
                  name: karpenter-pauser
                  key: pod_deletion_grace_period
          volumeMounts:
            - name: scale-script
              mountPath: "/scripts"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 200m
              memory: 256Mi
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: scale-script
          configMap:
            name: karpenter-scale-script
            defaultMode: 0777
