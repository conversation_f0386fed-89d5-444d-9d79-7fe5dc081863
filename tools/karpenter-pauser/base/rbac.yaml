---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: karpenter-pauser
  namespace: karpenter
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: karpenter-pauser
rules:
  # Permissions for managing nodepools
  - apiGroups: ["karpenter.sh"]
    resources: ["nodepools"]
    verbs: ["get", "list", "watch", "patch", "update"]
  # Permissions for managing nodeclaims
  - apiGroups: ["karpenter.sh"]
    resources: ["nodeclaims"]
    verbs: ["get", "list", "watch", "delete"]
  # Permissions for managing pods (for force deletion during scaling)
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "delete"]
  # Permissions for reading nodes (for monitoring)
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "watch"]
  # Permissions for reading configmaps (for configuration)
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: karpenter-pauser
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: karpenter-pauser
subjects:
  - kind: ServiceAccount
    name: karpenter-pauser
    namespace: karpenter
