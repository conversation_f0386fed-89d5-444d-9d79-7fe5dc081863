apiVersion: v1
kind: ConfigMap
metadata:
  name: karpenter-pauser
  namespace: karpenter
data:
  # Enable/disable the scaledown feature
  enabled: "true"
  
  # Nodepool configuration
  nodepool_prefixes: "application-critical-nodepool,application-semi-critical-nodepool,application-sub-critical-nodepool,application-non-critical-nodepool"
  
  # Original nodepool CPU limits (when scaling up)
  original_cpu_limits: "40,30,20,10"  # Corresponds to critical,semi-critical,sub-critical,non-critical
  
  # CronJob schedules
  scale_down_schedule: "0 20 * * 1-5"  # 8 PM weekdays
  scale_up_schedule: "0 8 * * 1-5"     # 8 AM weekdays
  timezone: "Asia/Kolkata"
  
  # Container image for kubectl operations
  kubectl_image: "bitnami/kubectl:latest"
  
  # Grace periods and timeouts
  node_deletion_wait_seconds: "300"
  pod_deletion_grace_period: "0"
  
  # API configuration
  api_enabled: "true"
  api_port: "8080"
