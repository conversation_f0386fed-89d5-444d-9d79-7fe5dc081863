apiVersion: v1
kind: ConfigMap
metadata:
  name: karpenter-scale-script
  namespace: karpenter
data:
  scale.sh: |
    #!/bin/sh
    
    # Load configuration from environment variables (set from ConfigMap)
    NODEPOOL_PREFIXES=${NODEPOOL_PREFIXES:-"application-critical-nodepool,application-semi-critical-nodepool,application-sub-critical-nodepool,application-non-critical-nodepool"}
    ORIGINAL_CPU_LIMITS=${ORIGINAL_CPU_LIMITS:-"40,30,20,10"}
    NODE_DELETION_WAIT=${NODE_DELETION_WAIT_SECONDS:-300}
    POD_GRACE_PERIOD=${POD_DELETION_GRACE_PERIOD:-0}
    
    # Convert comma-separated values to arrays
    IFS=',' read -ra NODEPOOL_ARRAY <<< "$NODEPOOL_PREFIXES"
    IFS=',' read -ra CPU_LIMITS_ARRAY <<< "$ORIGINAL_CPU_LIMITS"
    
    log() {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    }
    
    scale_down() {
        log "Starting scale down operation"
        log "Nodepool prefixes: $NODEPOOL_PREFIXES"
        
        # Scale down all configured nodepools
        for nodepool in "${NODEPOOL_ARRAY[@]}"; do
            nodepool=$(echo "$nodepool" | xargs)  # trim whitespace
            log "Scaling down nodepool: $nodepool"
            
            if kubectl get nodepool "$nodepool" -n karpenter >/dev/null 2>&1; then
                kubectl patch nodepool "$nodepool" -n karpenter --type merge --patch '{"spec": {"limits": {"cpu": "0"}}}'
                log "Successfully patched $nodepool to scale down"
            else
                log "Warning: Nodepool $nodepool not found, skipping"
            fi
        done
        
        # Delete all node claims
        log "Deleting all node claims"
        kubectl delete nodeclaims --all -n karpenter &
        
        log "Waiting ${NODE_DELETION_WAIT} seconds for node claims to be deleted"
        sleep "$NODE_DELETION_WAIT"
        
        log "Removing straggler pods that block node deletions"
        kubectl get pods --no-headers -A -o custom-columns=NAMESPACE:.metadata.namespace,NAME:.metadata.name | grep -v karpenter | \
        while read -r namespace name; do
            if [ -n "$namespace" ] && [ -n "$name" ]; then
                log "Force deleting pod $name in namespace $namespace"
                kubectl delete pod "$name" -n "$namespace" --grace-period="$POD_GRACE_PERIOD" --force 2>/dev/null || true
            fi
        done
        
        log "Scale down operation completed"
    }
    
    scale_up() {
        log "Starting scale up operation"
        log "Nodepool prefixes: $NODEPOOL_PREFIXES"
        log "CPU limits: $ORIGINAL_CPU_LIMITS"
        
        # Scale up all configured nodepools with their original limits
        for i in "${!NODEPOOL_ARRAY[@]}"; do
            nodepool=$(echo "${NODEPOOL_ARRAY[$i]}" | xargs)  # trim whitespace
            cpu_limit="${CPU_LIMITS_ARRAY[$i]}"
            
            log "Scaling up nodepool: $nodepool to CPU limit: $cpu_limit"
            
            if kubectl get nodepool "$nodepool" -n karpenter >/dev/null 2>&1; then
                kubectl patch nodepool "$nodepool" -n karpenter --type merge --patch "{\"spec\": {\"limits\": {\"cpu\": \"$cpu_limit\"}}}"
                log "Successfully patched $nodepool to scale up with CPU limit $cpu_limit"
            else
                log "Warning: Nodepool $nodepool not found, skipping"
            fi
        done
        
        log "Waiting ${NODE_DELETION_WAIT} seconds for node claims to be created"
        sleep "$NODE_DELETION_WAIT"
        
        log "Deleting all pods to force fair scheduling"
        kubectl get pods --no-headers -A -o custom-columns=NAMESPACE:.metadata.namespace,NAME:.metadata.name | grep -v karpenter | \
        while read -r namespace name; do
            if [ -n "$namespace" ] && [ -n "$name" ]; then
                log "Deleting pod $name in namespace $namespace for rescheduling"
                kubectl delete pod "$name" -n "$namespace" --grace-period="$POD_GRACE_PERIOD" --force 2>/dev/null || true
            fi
        done
        
        log "Scale up operation completed"
    }
    
    # Main execution
    case "$1" in
        "down")
            scale_down
            ;;
        "up")
            scale_up
            ;;
        *)
            log "Usage: $0 {up|down}"
            exit 1
            ;;
    esac
