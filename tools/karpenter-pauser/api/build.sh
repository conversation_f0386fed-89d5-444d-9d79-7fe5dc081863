#!/bin/bash

# Build script for Karpenter Pauser API
set -e

# Configuration
IMAGE_NAME="karpenter-pauser-api"
IMAGE_TAG="${1:-latest}"
REGISTRY="${REGISTRY:-}"  # Set this to your container registry

# Build the Docker image
echo "Building Docker image: ${IMAGE_NAME}:${IMAGE_TAG}"
docker build -t "${IMAGE_NAME}:${IMAGE_TAG}" .

# Tag for registry if specified
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    echo "Tagging image for registry: ${FULL_IMAGE_NAME}"
    docker tag "${IMAGE_NAME}:${IMAGE_TAG}" "${FULL_IMAGE_NAME}"
    
    echo "Pushing to registry: ${FULL_IMAGE_NAME}"
    docker push "${FULL_IMAGE_NAME}"
    
    echo "Image pushed successfully: ${FULL_IMAGE_NAME}"
else
    echo "Image built successfully: ${IMAGE_NAME}:${IMAGE_TAG}"
    echo "To push to a registry, set the REGISTRY environment variable and run again"
fi

echo "Build complete!"
