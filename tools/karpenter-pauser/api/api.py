#!/usr/bin/env python3
import os
import subprocess
import json
import logging
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KarpenterPauserHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"status": "healthy", "timestamp": datetime.now().isoformat()}).encode())
            
        elif path == '/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            status = self.get_nodepool_status()
            self.wfile.write(json.dumps(status).encode())
            
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Not found"}).encode())
    
    def do_POST(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/scale':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action', '').lower()
                
                if action not in ['up', 'down']:
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Invalid action. Use 'up' or 'down'"}).encode())
                    return
                
                logger.info(f"Received scale {action} request")
                result = self.execute_scale_operation(action)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(result).encode())
                
            except json.JSONDecodeError:
                self.send_response(400)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Invalid JSON"}).encode())
                
            except Exception as e:
                logger.error(f"Error processing scale request: {str(e)}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": str(e)}).encode())
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Not found"}).encode())
    
    def get_nodepool_status(self):
        try:
            nodepool_prefixes = os.getenv('NODEPOOL_PREFIXES', '').split(',')
            status = {"nodepools": [], "timestamp": datetime.now().isoformat()}
            
            for nodepool in nodepool_prefixes:
                nodepool = nodepool.strip()
                if not nodepool:
                    continue
                    
                try:
                    result = subprocess.run([
                        'kubectl', 'get', 'nodepool', nodepool, '-n', 'karpenter', 
                        '-o', 'jsonpath={.spec.limits.cpu}'
                    ], capture_output=True, text=True, timeout=30)
                    
                    cpu_limit = result.stdout.strip() if result.returncode == 0 else "unknown"
                    status["nodepools"].append({
                        "name": nodepool,
                        "cpu_limit": cpu_limit,
                        "status": "scaled_down" if cpu_limit == "0" else "scaled_up"
                    })
                except subprocess.TimeoutExpired:
                    status["nodepools"].append({
                        "name": nodepool,
                        "cpu_limit": "timeout",
                        "status": "unknown"
                    })
                except Exception as e:
                    status["nodepools"].append({
                        "name": nodepool,
                        "cpu_limit": "error",
                        "status": f"error: {str(e)}"
                    })
            
            return status
        except Exception as e:
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def execute_scale_operation(self, action):
        try:
            logger.info(f"Executing scale {action} operation")
            result = subprocess.run([
                '/bin/sh', '/scripts/scale.sh', action
            ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
            
            return {
                "action": action,
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode,
                "timestamp": datetime.now().isoformat()
            }
        except subprocess.TimeoutExpired:
            return {
                "action": action,
                "success": False,
                "error": "Operation timed out",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "action": action,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def log_message(self, format, *args):
        logger.info(f"{self.address_string()} - {format % args}")

def main():
    port = int(os.getenv('PORT', 8080))
    server = HTTPServer(('0.0.0.0', port), KarpenterPauserHandler)
    logger.info(f"Starting Karpenter Pauser API server on port {port}")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Shutting down server")
        server.shutdown()

if __name__ == '__main__':
    main()
