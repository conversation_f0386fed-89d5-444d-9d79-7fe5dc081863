apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

patches:
  # Customize the configuration for treebo-tools-common cluster
  - target:
      kind: ConfigMap
      name: karpenter-pauser
    patch: |-
      - op: replace
        path: /data/nodepool_prefixes
        value: "application-critical-nodepool,application-semi-critical-nodepool,application-sub-critical-nodepool,application-non-critical-nodepool"
      - op: replace
        path: /data/original_cpu_limits
        value: "40,30,20,10"
      - op: replace
        path: /data/scale_down_schedule
        value: "0 20 * * 1-5"  # 8 PM weekdays
      - op: replace
        path: /data/scale_up_schedule
        value: "0 8 * * 1-5"   # 8 AM weekdays
      - op: replace
        path: /data/timezone
        value: "Asia/Kolkata"
  
  # Update CronJob schedules to match ConfigMap
  - target:
      kind: CronJob
      name: karpenter-scale-down
    patch: |-
      - op: replace
        path: /spec/schedule
        value: "0 20 * * 1-5"
      - op: replace
        path: /spec/timeZone
        value: "Asia/Kolkata"
  
  - target:
      kind: CronJob
      name: karpenter-scale-up
    patch: |-
      - op: replace
        path: /spec/schedule
        value: "0 8 * * 1-5"
      - op: replace
        path: /spec/timeZone
        value: "Asia/Kolkata"
