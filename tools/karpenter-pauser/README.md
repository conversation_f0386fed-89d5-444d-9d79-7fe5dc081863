# Karpenter Pauser Extension

This extension provides automated scaling capabilities for Karpenter nodepools, allowing you to scale down clusters during off-hours and scale them back up during business hours. It also provides an HTTP API for manual scaling operations.

## Features

- **Automated Scheduling**: CronJobs to automatically scale down/up nodepools based on configurable schedules
- **Multi-Nodepool Support**: Supports all nodepool types (critical, semi-critical, sub-critical, non-critical)
- **Configurable Limits**: Each nodepool can have different CPU limits when scaling up
- **HTTP API**: REST API endpoints for manual scaling operations and status monitoring
- **Comprehensive Logging**: Detailed logging for all scaling operations
- **Graceful Pod Handling**: Properly handles pod deletion during scaling operations

## Configuration

All configuration is managed through the `karpenter-pauser` ConfigMap:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: karpenter-pauser
  namespace: karpenter
data:
  enabled: "true"
  nodepool_prefixes: "application-critical-nodepool,application-semi-critical-nodepool,application-sub-critical-nodepool,application-non-critical-nodepool"
  original_cpu_limits: "40,30,20,10"  # Corresponds to critical,semi-critical,sub-critical,non-critical
  scale_down_schedule: "0 20 * * 1-5"  # 8 PM weekdays
  scale_up_schedule: "0 8 * * 1-5"     # 8 AM weekdays
  timezone: "Asia/Kolkata"
  kubectl_image: "bitnami/kubectl:latest"
  node_deletion_wait_seconds: "300"
  pod_deletion_grace_period: "0"
  api_enabled: "true"
  api_port: "8080"
```

### Configuration Parameters

- `enabled`: Enable/disable the scaling feature
- `nodepool_prefixes`: Comma-separated list of nodepool names to manage
- `original_cpu_limits`: Comma-separated list of CPU limits for each nodepool when scaling up
- `scale_down_schedule`: Cron schedule for scaling down (default: 8 PM weekdays)
- `scale_up_schedule`: Cron schedule for scaling up (default: 8 AM weekdays)
- `timezone`: Timezone for cron schedules
- `kubectl_image`: Container image for kubectl operations
- `node_deletion_wait_seconds`: Wait time after deleting node claims
- `pod_deletion_grace_period`: Grace period for pod deletion
- `api_enabled`: Enable/disable the HTTP API
- `api_port`: Port for the HTTP API

## Usage

### Using as a Kustomize Component

1. **Base Installation**: Include the base component in your kustomization:

```yaml
resources:
  - path/to/karpenter-pauser/base
```

2. **Overlay Customization**: Create an overlay to customize for your cluster:

```yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

patches:
  - target:
      kind: ConfigMap
      name: karpenter-pauser
    patch: |-
      - op: replace
        path: /data/nodepool_prefixes
        value: "your-nodepool-1,your-nodepool-2"
      - op: replace
        path: /data/original_cpu_limits
        value: "50,25"
```

### HTTP API Endpoints

The extension provides a REST API for manual operations:

#### Health Check
```bash
GET /health
```
Returns the health status of the API service.

#### Get Nodepool Status
```bash
GET /status
```
Returns the current status of all managed nodepools.

#### Manual Scaling
```bash
POST /scale
Content-Type: application/json

{
  "action": "down"  # or "up"
}
```

### API Usage Examples

```bash
# Check API health
curl http://karpenter-pauser-api.karpenter.svc.cluster.local/health

# Get nodepool status
curl http://karpenter-pauser-api.karpenter.svc.cluster.local/status

# Scale down manually
curl -X POST http://karpenter-pauser-api.karpenter.svc.cluster.local/scale \
  -H "Content-Type: application/json" \
  -d '{"action": "down"}'

# Scale up manually
curl -X POST http://karpenter-pauser-api.karpenter.svc.cluster.local/scale \
  -H "Content-Type: application/json" \
  -d '{"action": "up"}'
```

## How It Works

### Scale Down Process
1. Patches all configured nodepools to set CPU limit to 0
2. Deletes all existing node claims
3. Waits for nodes to be terminated
4. Force deletes any remaining pods that might block node deletion

### Scale Up Process
1. Patches all configured nodepools to restore their original CPU limits
2. Waits for new node claims to be created
3. Deletes all pods to trigger fair rescheduling across new nodes

## Security

The extension uses a dedicated ServiceAccount (`karpenter-pauser`) with minimal required permissions:
- Read/write access to Karpenter nodepools and nodeclaims
- Read/delete access to pods (for graceful scaling)
- Read access to nodes and configmaps

## Monitoring

- All operations are logged with timestamps
- API provides status endpoints for monitoring
- CronJobs can be monitored through standard Kubernetes mechanisms

## Troubleshooting

1. **Check CronJob Status**:
   ```bash
   kubectl get cronjobs -n karpenter
   kubectl describe cronjob karpenter-scale-down -n karpenter
   ```

2. **Check API Logs**:
   ```bash
   kubectl logs -l app=karpenter-pauser-api -n karpenter
   ```

3. **Verify Configuration**:
   ```bash
   kubectl get configmap karpenter-pauser -n karpenter -o yaml
   ```

4. **Check RBAC Permissions**:
   ```bash
   kubectl auth can-i --as=system:serviceaccount:karpenter:karpenter-pauser get nodepools
   ```

## Customization for Different Environments

Create environment-specific overlays by customizing:
- Cron schedules for different timezones
- Different nodepool names and CPU limits
- API access controls
- Resource requests and limits for the API deployment
