# Unleash configuration for treebo-tools-common
# Feature flag management platform

# Deployment configuration
replicaCount: 1
deploymentType: "Deployment"

image:
  tag: "6.10"
  pullPolicy: IfNotPresent

# Container configuration
containerPort: 4242

env:
  - name: SEND_TELEMETRY
    value: "false"
  - name: CHECK_VERSION
    value: "false"
  - name: UNLEASH_URL
    value: "https://unleash.ekscraving1775.treebo.com"

# Database configuration
dbConfig:
  database: unleash
  schema: "public"
  host: postgresql.postgresql.svc.cluster.local
  port: 5432
  user: unleash
  # Password will be set via external secret
  pass: ""
  useExistingSecret:
    name: "unleash-secrets"
    key: "postgres-password"

# Ingress configuration
ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  hosts:
    - host: unleash.ekscraving1775.treebo.com
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Resource configuration
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 200m
    memory: 512Mi

# PostgreSQL dependency configuration
postgresql:
  enabled: false

autoscaling:
  enabled: false
