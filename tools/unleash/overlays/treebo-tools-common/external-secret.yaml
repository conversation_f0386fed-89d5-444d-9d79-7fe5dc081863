apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: unleash-external-secret
  namespace: unleash
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: unleash-secrets
    creationPolicy: Owner
  data:
    - secretKey: postgres-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/unleash-secrets
        property: postgresPassword
