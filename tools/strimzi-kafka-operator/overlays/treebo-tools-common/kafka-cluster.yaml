apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: kafka-cluster
  namespace: kafka
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: 4.0.0
    metadataVersion: 4.0-IV3
    template:
      pod:
        affinity:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - application-semi-critical
        tolerations:
          - key: workload-type
            operator: Equal
            value: "application-semi-critical"
            effect: NoSchedule
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
      - name: tls
        port: 9093
        type: internal
        tls: true
    config:
      auto.create.topics.enable: "false"
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 2
      default.replication.factor: 3
      min.insync.replicas: 2
    resources:
      requests:
        memory: 2Gi
        cpu: 1000m
      limits:
        memory: 4Gi
        cpu: 2000m
    # jvmOptions:
    #   -Xms: 1024m
    #   -Xmx: 2048m
    # metricsConfig:
    #   type: jmxPrometheusExporter
    #   valueFrom:
    #     configMapKeyRef:
    #       name: kafka-metrics
    #       key: kafka-metrics-config.yml
  entityOperator:
    topicOperator: {}
    userOperator: {}
  # kafkaExporter:
  #   topicRegex: ".*"
  #   groupRegex: ".*"
  #   resources:
  #     requests:
  #       memory: 64Mi
  #       cpu: 100m
  #     limits:
  #       memory: 128Mi
  #       cpu: 200m
