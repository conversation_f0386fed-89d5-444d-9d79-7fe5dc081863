apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: kafka-cluster-pool-a
  namespace: kafka
  labels:
    strimzi.io/cluster: kafka-cluster
spec:
  replicas: 3
  roles:
    - controller
    - broker
  resources:
    requests:
      memory: 1Gi
      cpu: "500m"
    limits:
      memory: 4Gi
      cpu: "2"
  storage:
    type: persistent-claim
    size: 5Gi
    class: ebs-sc
    deleteClaim: false
  template:
    pod:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                  - key: workload-type
                    operator: In
                    values:
                      - application-semi-critical
      tolerations:
        - key: workload-type
          operator: Equal
          value: "application-semi-critical"
          effect: NoSchedule
