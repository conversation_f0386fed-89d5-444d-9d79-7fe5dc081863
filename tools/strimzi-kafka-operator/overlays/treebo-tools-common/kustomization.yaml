apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

helmCharts:
  - name: strimzi-kafka-operator
    repo: https://strimzi.io/charts/
    version: 0.46.0
    releaseName: strimzi-kafka-operator
    namespace: kube-system
    valuesFile: values.yaml
    includeCRDs: true

resources:
  - namespace.yaml
  - kafka-cluster.yaml
  - kafka-node-pool.yaml

# this applies for operator. for CRDS has to be applied separately
components:
  - ../../../lib/common/components/application-critical
