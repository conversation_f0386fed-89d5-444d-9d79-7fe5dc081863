# Stakater Reloader

Stakater Reloader is a Kubernetes controller that watches changes in ConfigMaps and Secrets and performs rolling upgrades on associated Deployments, StatefulSets, DaemonSets, and Rollouts.

## Features

- Automatically restart pods when ConfigMaps or Secrets change
- Supports Deployments, StatefulSets, DaemonSets, and Rollouts
- Global watching capability across all namespaces
- Annotation-based configuration for selective reloading

## Usage

To enable automatic reloading for a deployment when a ConfigMap or Secret changes, add annotations:

```yaml
# Reload when any ConfigMap or Secret changes
metadata:
  annotations:
    reloader.stakater.com/auto: "true"

# Reload when specific ConfigMap changes
metadata:
  annotations:
    configmap.reloader.stakater.com/reload: "my-configmap"

# Reload when specific Secret changes
metadata:
  annotations:
    secret.reloader.stakater.com/reload: "my-secret"
```

## Documentation

For more information, visit: https://docs.stakater.com/reloader/
