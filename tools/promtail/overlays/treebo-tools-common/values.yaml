# Promtail configuration for treebo-tools-common
# Promtail must run on ALL nodes to collect logs, including tainted nodes

config:
  clients:
    - url: http://loki-gateway/loki/api/v1/push

priorityClassName: system-cluster-critical # important for karpenter

# Universal tolerations to run on all tainted nodes
tolerations:
  - operator: Exists
    effect: NoExecute
  - operator: Exists
    effect: NoSchedule

# Explicitly disable node affinity to ensure it runs on all nodes
affinity:
  nodeAffinity: {}

resources:
  limits:
    cpu: 200m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Service monitor for Prometheus scraping
serviceMonitor:
  enabled: true
  interval: 30s
  scrapeTimeout: 10s
