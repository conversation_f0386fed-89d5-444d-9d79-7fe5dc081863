# Metabase Configuration for treebo-tools-common
# Business intelligence and analytics platform

# Metabase image configuration
image:
  repository: metabase/metabase
  tag: v0.50.26
  pullPolicy: IfNotPresent

# Replica count for tools cluster
replicaCount: 2

# PostgreSQL database configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: ""  # Will be set via external secret
    username: metabase
    password: ""  # Will be set via external secret
    database: metabase
  primary:
    persistence:
      enabled: true
      storageClass: ebs-sc
      size: 50Gi  # Larger for tools cluster
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi

# Metabase configuration
metabase:
  # Database configuration
  database:
    type: postgres
    host: metabase-postgresql
    port: 5432
    dbname: metabase
    username: metabase
    password: ""  # Will be set via external secret
  
  # JVM options for tools cluster
  javaOpts: "-Xmx2g -Xms1g"
  
  # Resources for tools cluster
  resources:
    limits:
      cpu: 4000m
      memory: 8Gi
    requests:
      cpu: 2000m
      memory: 4Gi

# Service configuration
service:
  type: ClusterIP
  port: 3000

# Ingress configuration
ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  hosts:
    - host: metabase.ekscraving1775.treebo.com
      paths:
        - path: /
          pathType: Prefix

# Persistence for uploads and plugins
persistence:
  enabled: true
  storageClass: ebs-sc
  size: 10Gi
  accessMode: ReadWriteOnce

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 2000
  fsGroup: 2000

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 2000
  fsGroup: 2000

# Environment variables
env:
  - name: MB_DB_TYPE
    value: postgres
  - name: MB_DB_HOST
    value: metabase-postgresql
  - name: MB_DB_PORT
    value: "5432"
  - name: MB_DB_DBNAME
    value: metabase
  - name: MB_DB_USER
    value: metabase
  - name: ENVIRONMENT
    value: "tools"

# Health checks
livenessProbe:
  httpGet:
    path: /api/health
    port: 3000
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 10

readinessProbe:
  httpGet:
    path: /api/health
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
