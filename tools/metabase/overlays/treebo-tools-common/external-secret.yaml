apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: metabase-secrets
  namespace: metabase
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: metabase-secrets
    creationPolicy: Owner
  data:
    - secretKey: postgresql-postgres-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/metabase
        property: postgresqlPostgresPassword
    - secretKey: postgresql-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/metabase
        property: postgresqlPassword
