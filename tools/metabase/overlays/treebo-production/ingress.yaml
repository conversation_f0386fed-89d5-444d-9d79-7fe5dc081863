apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: metabase-ingress
  namespace: metabase
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  ingressClassName: nginx
  rules:
    - host: metabase.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: metabase
                port:
                  number: 3000
