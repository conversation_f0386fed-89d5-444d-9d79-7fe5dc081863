#!/bin/bash
# Create DevLake configuration in AWS Parameter Store

echo "Creating DevLake configuration..."

# Get MariaDB password
MARIADB_PASSWORD=$(aws ssm get-parameter \
  --name "/treebo/production/eks/cluster-treebo-tools-common/mariadb" \
  --with-decryption \
  --query 'Parameter.Value' \
  --output text | jq -r '.user_password')

# Generate encryption secret
ENCRYPTION_SECRET=$(openssl rand -hex 32)

# Create DB URL with actual password
DB_URL="mysql://tools_user:${MARIADB_PASSWORD}@mariadb.mariadb.svc.cluster.local:3306/devlake?charset=utf8mb4&parseTime=True&loc=UTC"

echo "Generated values:"
echo "Encryption Secret: $ENCRYPTION_SECRET"
echo "DB URL: $DB_URL"

# Create the parameter
aws ssm put-parameter \
  --name "/treebo/production/eks/cluster-treebo-tools-common/devlake" \
  --type "SecureString" \
  --value "{
    \"dbUrl\": \"$DB_URL\",
    \"encryptionSecret\": \"$ENCRYPTION_SECRET\"
  }" \
  --description "Apache DevLake configuration for tools cluster" \
  --tags Key=Environment,Value=tools Key=Service,Value=devlake

echo "✅ DevLake parameters created successfully!"
