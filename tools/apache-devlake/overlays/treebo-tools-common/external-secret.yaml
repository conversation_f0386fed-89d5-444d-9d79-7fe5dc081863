apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: devlake-secrets
  namespace: devlake
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: devlake-secrets
    creationPolicy: Owner
  data:
    - secretKey: DB_URL
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/devlake
        property: dbUrl
    - secretKey: ENCRYPTION_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/devlake
        property: encryptionSecret
