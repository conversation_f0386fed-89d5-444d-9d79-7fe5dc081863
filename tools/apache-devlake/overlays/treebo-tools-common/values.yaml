# Apache DevLake Configuration for treebo-tools-common
# Data engineering platform for DevOps analytics

# Use external MariaDB (disable built-in MySQL)
mysql:
  enabled: false # Disable built-in MySQL
  useExternal: true
  externalPort: 3306
  externalServer: "mariadb.mariadb.svc.cluster.local"

grafana:
  enabled: false # Disable built-in Grafana - use existing one
  external:
    url: "http://grafana.monitoring.svc.cluster.local:3000"
  envFromSecrets:
    - name: "devlake-secrets"

lake:
  encryptionSecret:
    secretName: "devlake-secrets"
    autoCreateSecret: false
  ui:
    basicAuth:
      enabled: true
      username: "devlake"
      password: "devlake"
      autoCreateSecret: false

service:
  type: ClusterIP
  api:
    port: 8080
  ui:
    port: 4000

ingress:
  enabled: false

option:
  database: mysql
  connectionSecretName: "devlake-secrets"
  autoCreateSecret: false
