apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: devlake-ingress
  namespace: devlake
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  ingressClassName: nginx
  rules:
    - host: devlake.ekscraving1775.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: devlake-ui
                port:
                  number: 4000
