package main

import (
	"context"
	"flag"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

func init() {
	_ = clientgoscheme.AddToScheme(scheme)
}

func main() {
	var (
		port               = flag.Int("port", 9443, "Webhook server port")
		certDir            = flag.String("cert-dir", "/tmp/k8s-webhook-server/serving-certs", "Certificate directory")
		logLevel           = flag.String("log-level", "info", "Log level (debug, info, warn, error)")
		configMapName      = flag.String("config-map-name", "", "ConfigMap name for webhook configuration")
		configMapNamespace = flag.String("config-map-namespace", "", "ConfigMap namespace for webhook configuration")
	)
	flag.Parse()

	// Setup logging
	opts := zap.Options{
		Development: false,
	}
	// switch *logLevel {
	// case "debug":
	// 	opts.Level = zap
	// case "info":
	// 	opts.Level = zap.InfoLevel
	// case "warn":
	// 	opts.Level = zap.WarnLevel
	// case "error":
	// 	opts.Level = zap.ErrorLevel
	// }

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))

	setupLog.Info("Starting Woodpecker Toleration Webhook",
		"port", *port,
		"cert-dir", *certDir,
		"log-level", *logLevel,
		"config-map-name", *configMapName,
		"config-map-namespace", *configMapNamespace)

	// Create Kubernetes client
	config := ctrl.GetConfigOrDie()
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		setupLog.Error(err, "Failed to create Kubernetes client")
		os.Exit(1)
	}

	// Create webhook server
	webhookServer := webhook.NewServer(webhook.Options{
		Port:    *port,
		CertDir: *certDir,
	})

	// Register the mutating webhook
	mutator := &WoodpeckerTolerationMutator{
		Client:             clientset,
		Logger:             ctrl.Log.WithName("mutator"),
		ConfigMapName:      *configMapName,
		ConfigMapNamespace: *configMapNamespace,
	}

	webhookServer.Register("/mutate", &webhook.Admission{
		Handler: mutator,
	})

	// Register health check endpoints
	webhookServer.Register("/healthz", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("ok"))
	}))

	webhookServer.Register("/readyz", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("ready"))
	}))

	// Setup signal handling for graceful shutdown
	ctx, cancel := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer cancel()

	// Start the webhook server
	go func() {
		setupLog.Info("Starting webhook server")
		if err := webhookServer.Start(ctx); err != nil {
			setupLog.Error(err, "Failed to start webhook server")
			os.Exit(1)
		}
	}()

	// Wait for shutdown signal
	<-ctx.Done()
	setupLog.Info("Shutting down webhook server")

	// Give the server time to shutdown gracefully
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// The webhook server will shutdown when the context is cancelled
	<-shutdownCtx.Done()
	setupLog.Info("Webhook server stopped")
}
