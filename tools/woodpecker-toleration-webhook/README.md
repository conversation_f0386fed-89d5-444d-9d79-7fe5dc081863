# Woodpecker Toleration Webhook

A Kubernetes mutating admission webhook that automatically adds tolerations to Woodpecker CI pods, allowing them to run on tainted nodepools.

## Problem Statement

Woodpecker CI with Kubernetes backend creates pods for each pipeline step. In environments with tainted nodepools (like <PERSON><PERSON><PERSON>), these pods cannot be scheduled without appropriate tolerations. Previously, tolerations had to be defined manually in each pipeline step, which was cumbersome and error-prone.

## Solution

This webhook automatically detects Woodpecker CI pods and adds the required toleration:

```yaml
tolerations:
- key: workload-type
  value: application-sub-critical
  effect: NoSchedule
```

## How It Works

1. **Pod Detection**: The webhook identifies Woodpecker pods by checking for labels with the `woodpecker-ci.org/` prefix
2. **Toleration Injection**: Automatically adds the `application-sub-critical` toleration to allow scheduling on tainted nodes
3. **Namespace Targeting**: Only processes pods in the `woodpecker` namespace
4. **Idempotent**: Skips pods that already have the required toleration

## Woodpecker Pod Identification

The webhook detects Woodpecker pods by looking for these labels:
- `woodpecker-ci.org/forge-id`
- `woodpecker-ci.org/repo-forge-id`
- `woodpecker-ci.org/repo-id`
- `woodpecker-ci.org/repo-name`
- `woodpecker-ci.org/repo-full-name`
- `woodpecker-ci.org/branch`
- `woodpecker-ci.org/org-id`
- Any label starting with `woodpecker-ci.org/`

## Architecture

- **Language**: Go
- **Framework**: controller-runtime webhook framework
- **Security**: TLS certificates managed by cert-manager
- **RBAC**: Minimal permissions (read pods, handle admission reviews)
- **High Availability**: 2 replicas with proper resource limits

## Deployment

### Prerequisites

1. **cert-manager installed in the cluster**:
   ```bash
   kubectl apply -k tools/cert-manager/overlays/treebo-tools-common
   ```
2. **Woodpecker CI configured with Kubernetes backend**
3. **Karpenter nodepools with `workload-type: application-sub-critical` taints**

### Build and Deploy

1. **Build and push the Docker image**:
   ```bash
   cd tools/woodpecker-toleration-webhook
   ./build.sh
   ```

2. **Deploy to cluster**:
   ```bash
   kubectl apply -k tools/woodpecker-toleration-webhook/overlays/treebo-tools-common
   ```

3. **Or use Woodpecker CI pipeline**:
   - The `.woodpecker/woodpecker-toleration-webhook.yml` pipeline will automatically build and push the image
   - Manual deployment steps are available in the pipeline for staging and production

### Verification

1. **Check webhook deployment**:
   ```bash
   kubectl get pods -n woodpecker-toleration-webhook
   kubectl logs -n woodpecker-toleration-webhook -l app.kubernetes.io/name=woodpecker-toleration-webhook
   ```

2. **Verify webhook configuration**:
   ```bash
   kubectl get mutatingwebhookconfiguration woodpecker-toleration-webhook -o yaml
   ```

3. **Test with a Woodpecker pipeline**:
   - Trigger a pipeline in Woodpecker
   - Check that the created pods have the toleration:
   ```bash
   kubectl get pods -n woodpecker -o yaml | grep -A 10 tolerations
   ```

## Configuration

### Command Line Arguments

- `--port`: Webhook server port (default: 9443)
- `--cert-dir`: TLS certificate directory (default: /tmp/k8s-webhook-server/serving-certs)
- `--log-level`: Log level (debug, info, warn, error)
- `--config-map-name`: ConfigMap name for webhook configuration (optional)
- `--config-map-namespace`: ConfigMap namespace for webhook configuration (optional)

### ConfigMap Configuration

The webhook can be configured using a ConfigMap with the following keys:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: woodpecker-toleration-webhook-config
  namespace: woodpecker-toleration-webhook
data:
  # Toleration configuration
  toleration.key: "workload-type"
  toleration.operator: "Equal"  # Equal or Exists
  toleration.value: "application-sub-critical"
  toleration.effect: "NoSchedule"  # NoSchedule, PreferNoSchedule, or NoExecute

  # Service account configuration
  serviceAccount: ""  # Optional service account name to set on Woodpecker pods
```

If no ConfigMap is specified or the ConfigMap is not found, the webhook uses default values:
- Toleration: `workload-type=application-sub-critical:NoSchedule`
- Service Account: (unchanged)

### Webhook Configuration

The webhook is configured to:
- Target only the `woodpecker` namespace
- Process only `CREATE` operations on pods
- Use `Fail` policy for security (webhook must be available)
- Timeout after 10 seconds

## Security

- Runs as non-root user (65532)
- Read-only root filesystem
- Minimal RBAC permissions
- TLS encryption with cert-manager
- Resource limits to prevent resource exhaustion

## Troubleshooting

### Webhook Not Working

1. Check webhook pod logs:
   ```bash
   kubectl logs -n woodpecker-toleration-webhook -l app.kubernetes.io/name=woodpecker-toleration-webhook
   ```

2. Verify certificate:
   ```bash
   kubectl get certificate -n woodpecker-toleration-webhook
   kubectl describe certificate woodpecker-toleration-webhook-certs -n woodpecker-toleration-webhook
   ```

3. Check webhook configuration:
   ```bash
   kubectl get mutatingwebhookconfiguration woodpecker-toleration-webhook -o yaml
   ```

4. Verify ConfigMap configuration:
   ```bash
   kubectl get configmap woodpecker-toleration-webhook-config -n woodpecker-toleration-webhook -o yaml
   ```

### Pods Still Not Scheduled

1. Verify nodepool taints:
   ```bash
   kubectl get nodes -o custom-columns=NAME:.metadata.name,TAINTS:.spec.taints
   ```

2. Check pod tolerations:
   ```bash
   kubectl get pod <woodpecker-pod> -n woodpecker -o yaml | grep -A 10 tolerations
   ```

3. Verify Woodpecker labels:
   ```bash
   kubectl get pod <woodpecker-pod> -n woodpecker -o yaml | grep -A 10 labels
   ```

## Monitoring

The webhook exposes health check endpoints:
- `/healthz`: Liveness probe
- `/readyz`: Readiness probe

Monitor webhook performance and errors through:
- Pod logs
- Kubernetes events
- Admission controller metrics (if enabled)

## Development

### Local Testing

1. Run webhook locally:
   ```bash
   go run . --cert-dir=/tmp/certs --log-level=debug
   ```

2. Use kubectl to test admission:
   ```bash
   kubectl apply -f test-pod.yaml
   ```

### Code Structure

- `main.go`: Entry point and server setup
- `webhook.go`: Admission webhook logic
- `Dockerfile`: Container image definition
- `base/`: Kubernetes manifests
- `overlays/`: Environment-specific configurations
