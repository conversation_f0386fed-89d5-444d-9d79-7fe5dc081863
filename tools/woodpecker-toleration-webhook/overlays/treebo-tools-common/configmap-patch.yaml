apiVersion: v1
kind: ConfigMap
metadata:
  name: woodpecker-toleration-webhook-config
  namespace: woodpecker-toleration-webhook
data:
  # Custom toleration for treebo-tools-common environment
  toleration.key: "workload-type"
  toleration.operator: "Equal"
  toleration.value: "application-sub-critical"
  toleration.effect: "NoSchedule"

  # Optional: Set a specific service account for Woodpecker pods
  serviceAccount: "woodpecker-agent-sa"
