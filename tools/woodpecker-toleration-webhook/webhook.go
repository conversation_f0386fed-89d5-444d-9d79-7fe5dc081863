package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// WebhookConfig holds the configuration for the webhook
type WebhookConfig struct {
	Toleration         corev1.Toleration
	ServiceAccountName string
}

// WoodpeckerTolerationMutator implements the mutating webhook for adding tolerations to Woodpecker pods
type WoodpeckerTolerationMutator struct {
	Client              kubernetes.Interface
	Logger              logr.Logger
	Config              *WebhookConfig
	ConfigMapName       string
	ConfigMapNamespace  string
}

// Handle implements the admission.Handler interface
func (m *WoodpeckerTolerationMutator) Handle(ctx context.Context, req admission.Request) admission.Response {
	pod := &corev1.Pod{}

	// Decode the pod from the admission request
	if err := json.Unmarshal(req.Object.Raw, pod); err != nil {
		m.Logger.Error(err, "Failed to unmarshal pod")
		return admission.Errored(http.StatusBadRequest, err)
	}

	m.Logger.Info("Processing pod",
		"name", pod.Name,
		"namespace", pod.Namespace,
		"labels", pod.Labels)

	// Check if this is a Woodpecker pod
	if !m.isWoodpeckerPod(pod) {
		m.Logger.V(1).Info("Pod is not a Woodpecker pod, skipping",
			"name", pod.Name,
			"namespace", pod.Namespace)
		return admission.Allowed("Not a Woodpecker pod")
	}

	// Load configuration from ConfigMap
	if err := m.loadConfig(ctx); err != nil {
		m.Logger.Error(err, "Failed to load configuration from ConfigMap")
		return admission.Errored(http.StatusInternalServerError, err)
	}

	m.Logger.Info("Detected Woodpecker pod, adding toleration and updating service account",
		"name", pod.Name,
		"namespace", pod.Namespace)

	// Create a copy of the pod to modify
	modifiedPod := pod.DeepCopy()

	modified := false

	// Add the toleration if it doesn't already exist and is configured
	if m.Config != nil && !m.hasToleration(modifiedPod, m.Config.Toleration) {
		modifiedPod.Spec.Tolerations = append(modifiedPod.Spec.Tolerations, m.Config.Toleration)
		m.Logger.Info("Added toleration to Woodpecker pod",
			"name", pod.Name,
			"namespace", pod.Namespace,
			"toleration", m.Config.Toleration)
		modified = true
	}

	// Update service account if specified and different from current
	if m.Config != nil && m.Config.ServiceAccountName != "" && modifiedPod.Spec.ServiceAccountName != m.Config.ServiceAccountName {
		m.Logger.Info("Updating service account for Woodpecker pod",
			"name", pod.Name,
			"namespace", pod.Namespace,
			"oldServiceAccount", modifiedPod.Spec.ServiceAccountName,
			"newServiceAccount", m.Config.ServiceAccountName)
		modifiedPod.Spec.ServiceAccountName = m.Config.ServiceAccountName
		modified = true
	}

	if !modified {
		m.Logger.Info("No changes needed, skipping",
			"name", pod.Name,
			"namespace", pod.Namespace)
		return admission.Allowed("No changes needed")
	}

	// Create the patch
	originalBytes, err := json.Marshal(pod)
	if err != nil {
		m.Logger.Error(err, "Failed to marshal original pod")
		return admission.Errored(http.StatusInternalServerError, err)
	}

	modifiedBytes, err := json.Marshal(modifiedPod)
	if err != nil {
		m.Logger.Error(err, "Failed to marshal modified pod")
		return admission.Errored(http.StatusInternalServerError, err)
	}

	// Return the response with the patch
	return admission.PatchResponseFromRaw(originalBytes, modifiedBytes)
}

// isWoodpeckerPod checks if the pod has Woodpecker-specific labels
func (m *WoodpeckerTolerationMutator) isWoodpeckerPod(pod *corev1.Pod) bool {
	if pod.Labels == nil {
		return false
	}

	// Check for any Woodpecker-specific labels
	woodpeckerLabels := []string{
		"woodpecker-ci.org/forge-id",
		"woodpecker-ci.org/repo-forge-id",
		"woodpecker-ci.org/repo-id",
		"woodpecker-ci.org/repo-name",
		"woodpecker-ci.org/repo-full-name",
		"woodpecker-ci.org/branch",
		"woodpecker-ci.org/org-id",
		"woodpecker-ci.org/step", // Legacy label for backward compatibility
	}

	for _, label := range woodpeckerLabels {
		if _, exists := pod.Labels[label]; exists {
			m.Logger.V(1).Info("Found Woodpecker label",
				"label", label,
				"value", pod.Labels[label])
			return true
		}
	}

	// Also check for labels that start with woodpecker-ci.org/
	for key := range pod.Labels {
		if strings.HasPrefix(key, "woodpecker-ci.org/") {
			m.Logger.V(1).Info("Found Woodpecker label prefix",
				"label", key,
				"value", pod.Labels[key])
			return true
		}
	}

	return false
}

// hasToleration checks if the pod already has the specified toleration
func (m *WoodpeckerTolerationMutator) hasToleration(pod *corev1.Pod, targetToleration corev1.Toleration) bool {
	for _, toleration := range pod.Spec.Tolerations {
		if toleration.Key == targetToleration.Key &&
			toleration.Operator == targetToleration.Operator &&
			toleration.Value == targetToleration.Value &&
			toleration.Effect == targetToleration.Effect {
			return true
		}
	}
	return false
}

// loadConfig loads configuration from ConfigMap
func (m *WoodpeckerTolerationMutator) loadConfig(ctx context.Context) error {
	if m.ConfigMapName == "" || m.ConfigMapNamespace == "" {
		m.Logger.Info("ConfigMap name or namespace not specified, using default configuration")
		// Use default configuration
		m.Config = &WebhookConfig{
			Toleration: corev1.Toleration{
				Key:      "workload-type",
				Operator: corev1.TolerationOpEqual,
				Value:    "application-sub-critical",
				Effect:   corev1.TaintEffectNoSchedule,
			},
			ServiceAccountName: "",
		}
		return nil
	}

	configMap, err := m.Client.CoreV1().ConfigMaps(m.ConfigMapNamespace).Get(ctx, m.ConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get ConfigMap %s/%s: %w", m.ConfigMapNamespace, m.ConfigMapName, err)
	}

	config := &WebhookConfig{}

	// Parse toleration configuration
	if tolerationKey, exists := configMap.Data["toleration.key"]; exists {
		config.Toleration.Key = tolerationKey
	} else {
		config.Toleration.Key = "workload-type"
	}

	if tolerationOperator, exists := configMap.Data["toleration.operator"]; exists {
		switch tolerationOperator {
		case "Equal":
			config.Toleration.Operator = corev1.TolerationOpEqual
		case "Exists":
			config.Toleration.Operator = corev1.TolerationOpExists
		default:
			config.Toleration.Operator = corev1.TolerationOpEqual
		}
	} else {
		config.Toleration.Operator = corev1.TolerationOpEqual
	}

	if tolerationValue, exists := configMap.Data["toleration.value"]; exists {
		config.Toleration.Value = tolerationValue
	} else {
		config.Toleration.Value = "application-sub-critical"
	}

	if tolerationEffect, exists := configMap.Data["toleration.effect"]; exists {
		switch tolerationEffect {
		case "NoSchedule":
			config.Toleration.Effect = corev1.TaintEffectNoSchedule
		case "PreferNoSchedule":
			config.Toleration.Effect = corev1.TaintEffectPreferNoSchedule
		case "NoExecute":
			config.Toleration.Effect = corev1.TaintEffectNoExecute
		default:
			config.Toleration.Effect = corev1.TaintEffectNoSchedule
		}
	} else {
		config.Toleration.Effect = corev1.TaintEffectNoSchedule
	}

	// Parse service account configuration
	if serviceAccount, exists := configMap.Data["serviceAccount"]; exists {
		config.ServiceAccountName = serviceAccount
	}

	m.Config = config

	m.Logger.Info("Loaded configuration from ConfigMap",
		"configMap", fmt.Sprintf("%s/%s", m.ConfigMapNamespace, m.ConfigMapName),
		"toleration", config.Toleration,
		"serviceAccount", config.ServiceAccountName)

	return nil
}
