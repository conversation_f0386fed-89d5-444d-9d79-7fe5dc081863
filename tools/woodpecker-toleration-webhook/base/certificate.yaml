apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: woodpecker-toleration-webhook-certs
  namespace: woodpecker-toleration-webhook
  labels:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
spec:
  secretName: woodpecker-toleration-webhook-certs
  duration: 8760h # 1 year
  renewBefore: 720h # 30 days
  subject:
    organizations:
      - treebo
  commonName: woodpecker-webhook.woodpecker-toleration-webhook.svc
  dnsNames:
    - woodpecker-webhook.woodpecker-toleration-webhook.svc
    - woodpecker-toleration-webhook
    - woodpecker-toleration-webhook.woodpecker-toleration-webhook
    - woodpecker-toleration-webhook.woodpecker-toleration-webhook.svc
    - woodpecker-toleration-webhook.woodpecker-toleration-webhook.svc.cluster.local
    - localhost
  ipAddresses:
    - 127.0.0.1
  issuerRef:
    name: selfsigned-issuer
    kind: ClusterIssuer
    group: cert-manager.io
  usages:
    - digital signature
    - key encipherment
    - server auth
