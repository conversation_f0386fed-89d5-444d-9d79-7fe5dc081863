apiVersion: v1
kind: Service
metadata:
  name: woodpecker-toleration-webhook
  namespace: woodpecker-toleration-webhook
  labels:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
spec:
  type: ClusterIP
  ports:
  - name: webhook-api
    port: 443
    targetPort: 9443
    protocol: TCP
  selector:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
