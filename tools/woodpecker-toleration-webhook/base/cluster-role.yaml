apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: woodpecker-toleration-webhook
  labels:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
rules:
  # Minimal permissions needed for the webhook to function
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch"]
  # Permission to read ConfigMaps for configuration
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch"]
  # Permission to read admission reviews
  - apiGroups: ["admission.k8s.io"]
    resources: ["admissionreviews"]
    verbs: ["get", "create"]
