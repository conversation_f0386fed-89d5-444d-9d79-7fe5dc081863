apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: woodpecker-toleration-webhook

resources:
  - namespace.yaml
  - cluster-issuer.yaml
  - certificate.yaml
  - rbac.yaml
  - cluster-role.yaml
  - cluster-role-binding.yaml
  - service.yaml
  - configmap.yaml
  - deployment.yaml
  - mutating-webhook-config.yaml

components:
  - ../../lib/common/components/application-critical

# Ensure proper resource ordering
generatorOptions:
  disableNameSuffixHash: true

# Add patches to ensure proper ordering and dependencies
patches:
  - target:
      kind: Deployment
      name: woodpecker-toleration-webhook
    patch: |-
      - op: add
        path: /metadata/annotations
        value:
          cert-manager.io/revision: "1"
      - op: add
        path: /spec/template/metadata/annotations
        value:
          cert-manager.io/revision: "1"
