apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: woodpecker-toleration-webhook
  labels:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
  annotations:
    cert-manager.io/inject-ca-from: woodpecker-toleration-webhook/woodpecker-toleration-webhook-certs
    # Deploy after deployment is ready
    argocd.argoproj.io/sync-wave: "3"
webhooks:
  - name: woodpecker-toleration.treebo.com
    clientConfig:
      service:
        name: woodpecker-toleration-webhook
        namespace: woodpecker-toleration-webhook
        path: "/mutate"
        port: 443
    rules:
      - operations: ["CREATE"]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
    # Only target the woodpecker namespace where CI pods are created
    namespaceSelector:
      matchLabels:
        name: woodpecker
    # Let the webhook handle all pods in the woodpecker namespace
    # The webhook will filter for Woodpecker-specific labels
    objectSelector: {}
    admissionReviewVersions: ["v1", "v1beta1"]
    sideEffects: None
    failurePolicy: Fail
    timeoutSeconds: 10
