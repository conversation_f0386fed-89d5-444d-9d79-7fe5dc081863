apiVersion: v1
kind: ConfigMap
metadata:
  name: woodpecker-toleration-webhook-config
  namespace: woodpecker-toleration-webhook
  labels:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
data:
  # Toleration configuration
  toleration.key: "workload-type"
  toleration.operator: "Equal"
  toleration.value: "application-sub-critical"
  toleration.effect: "NoSchedule"
  
  # Service account configuration
  serviceAccount: ""
