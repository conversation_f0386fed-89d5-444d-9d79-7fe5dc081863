apiVersion: apps/v1
kind: Deployment
metadata:
  name: woodpecker-toleration-webhook
  namespace: woodpecker-toleration-webhook
  annotations:
    # Deploy after certificates are ready
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: woodpecker-toleration-webhook
    app.kubernetes.io/component: webhook
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: woodpecker-toleration-webhook
      app.kubernetes.io/component: webhook
  template:
    metadata:
      labels:
        app.kubernetes.io/name: woodpecker-toleration-webhook
        app.kubernetes.io/component: webhook
    spec:
      serviceAccountName: woodpecker-toleration-webhook
      securityContext:
        runAsNonRoot: true
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
      containers:
        - name: webhook
          image: ************.dkr.ecr.ap-south-1.amazonaws.com/eks-woodpecker-toleration-webhook:4ba99023
          imagePullPolicy: Always
          ports:
            - containerPort: 9443
              name: webhook-api
              protocol: TCP
          args:
            - --port=9443
            - --cert-dir=/tmp/k8s-webhook-server/serving-certs
            - --log-level=info
            - --config-map-name=woodpecker-toleration-webhook-config
            - --config-map-namespace=woodpecker-toleration-webhook
          env:
            - name: TLS_CERT_FILE
              value: /tmp/k8s-webhook-server/serving-certs/tls.crt
            - name: TLS_PRIVATE_KEY_FILE
              value: /tmp/k8s-webhook-server/serving-certs/tls.key
          volumeMounts:
            - name: webhook-certs
              mountPath: /tmp/k8s-webhook-server/serving-certs
              readOnly: true
          resources:
            limits:
              cpu: 500m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
          startupProbe:
            httpGet:
              path: /readyz
              port: 9443
              scheme: HTTPS
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 30
          livenessProbe:
            httpGet:
              path: /healthz
              port: 9443
              scheme: HTTPS
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /readyz
              port: 9443
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 65532
            runAsGroup: 65532
      volumes:
        - name: webhook-certs
          secret:
            secretName: woodpecker-toleration-webhook-certs
      terminationGracePeriodSeconds: 30
