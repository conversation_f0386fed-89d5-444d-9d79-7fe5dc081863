# Build the webhook binary
FROM golang:1.24 as builder

WORKDIR /workspace

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the binary
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o webhook .

# Use distroless as minimal base image to package the webhook binary
FROM gcr.io/distroless/static:nonroot

WORKDIR /

# Copy the binary from the builder stage
COPY --from=builder /workspace/webhook .

# Use nonroot user for security
USER 65532:65532

ENTRYPOINT ["/webhook"]
