#!/bin/bash

# Usage: ./delete-namespace-resources.sh <namespace>

NAMESPACE=$1

if [ -z "$NAMESPACE" ]; then
  echo "Error: Namespace not provided"
  echo "Usage: $0 <namespace>"
  exit 1
fi

# Delete all resources in the namespace
echo "Deleting all resources in namespace: $NAMESPACE"
kubectl delete all --all -n $NAMESPACE

# Delete additional resources that aren't caught by 'all'
echo "Deleting additional resources in namespace: $NAMESPACE"
kubectl delete pvc,configmap,secret,serviceaccount,rolebinding,role --all -n $NAMESPACE

# Find CRDs that have resources in this namespace
echo "Finding and deleting custom resources in namespace: $NAMESPACE"
for crd in $(kubectl get crd -o name); do
  # Extract the plural resource name from the CRD
  RESOURCE=$(kubectl get "$crd" -o jsonpath='{.spec.names.plural}')
  
  # Check if this CRD has resources in the target namespace
  if kube<PERSON>l get "$RESOURCE" -n "$NAMESPACE" &>/dev/null; then
    echo "Deleting $RESOURCE in namespace $NAMESPACE"
    kubectl delete "$RESOURCE" --all -n "$NAMESPACE"
  fi
done

# Optionally, delete the namespace itself
echo "Do you want to delete the namespace itself? (y/n)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
  kubectl delete namespace "$NAMESPACE"
  echo "Namespace $NAMESPACE deleted"
else
  echo "Namespace $NAMESPACE preserved"
fi