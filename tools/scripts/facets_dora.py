import requests
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import os
from typing import List, Optional, Any, Dict, Type, TypeVar
import numpy as np  # For statistical calculations
from pydantic import BaseModel, Field, field_validator, model_validator
from pydantic.alias_generators import to_camel

T = TypeVar('T', bound='CamelModel')

class CamelModel(BaseModel):
    """Base model that provides camelCase conversion utilities"""

    # Configure model to use camelCase for field aliases
    model_config = {
        "populate_by_name": True,
        "alias_generator": to_camel
    }

    @classmethod
    def model_validate_from_camel(cls: Type[T], data: Dict[str, Any]) -> Optional[T]:
        """Validate data with camelCase keys"""
        if not data:
            return None

        # Handle any special processing in subclasses
        processed_data = cls._preprocess_data(data)

        return cls.model_validate(processed_data)

    @classmethod
    def _preprocess_data(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Hook for subclasses to preprocess data before validation"""
        return data


"""
Response structure
```
{
    "content":
    [
        {
            "id": "682845bf6bdbea000734f260",
            "deploymentJobType": "CODEBUILD",
            "codebuildId": "92894e7c-5ed0-4298-a7d7-d5acd2883016",
            "description": null,
            "createdOn": "2025-05-17T08:15:59.963+00:00",
            "finishedOn": "2025-05-17T08:22:02.398+00:00",
            "timeTakenInSeconds": 362,
            "releaseType": "HOTFIX",
            "status": "SUCCEEDED",
            "changesApplied":
            [
                {
                    "resourcePath": "\u001b[0m\u001b[1mmodule.application.helm_release.application",
                    "resourceKey": "tg-crs-booking-consumer-su-treebo",
                    "type": "Modifications"
                }
            ],
            "appDeployments":
            [
                {
                    "appName": "tg-crs-booking-consumer-su-treebo",
                    "artifact":
                    {
                        "id": "62e97cf5ce29ec0001442456",
                        "createdBy": null,
                        "creationDate": null,
                        "lastModifiedDate": "2025-05-15T02:40:07.000+00:00",
                        "lastModifiedBy": "<EMAIL>",
                        "buildId": "1891",
                        "applicationName": "tenant-gateway",
                        "artifactUri": "************.dkr.ecr.ap-southeast-1.amazonaws.com/tenant-gateway:main-f8a7ce65-p-2621-aps1-01",
                        "clusterId": "62e8e0edce29ec00014423cb",
                        "releaseStream": null,
                        "releaseType": "RELEASE",
                        "artifactory": "facetsaccount",
                        "buildDescription": "tenant-gateway",
                        "createdOn": "2025-05-15T08:10:07.352",
                        "metadata": null,
                        "appliedRuleVersionId": null,
                        "appliedRuleId": null,
                        "appliedRuleName": null,
                        "repositoryName": null,
                        "tag": null,
                        "repository": "com.capillary.ops.cp.repository.ArtifactRepository",
                        "promoted": false,
                        "registrationType": "ENVIRONMENT",
                        "classified": true,
                        "registrationValue": "62e8e0edce29ec00014423cb",
                        "versioningKey": "com.capillary.ops.cp.bo.Artifact62e97cf5ce29ec0001442456",
                        "numberOfVersions": 50
                    }
                }
            ],
            "migrationScriptsRun": null,
            "errorLogs":
            [],
            "deploymentType": "REGULAR",
            "stackVersion": "b99a22326cbc7c925362bbedb6b4c1b3d0f2daeb",
            "tfVersion": "production/3.21.10.zip-1747408146000",
            "signedOff": false,
            "deploymentContextVersion": "43fce73ed3801e4f297e9d488fc04c2a",
            "triggeredBy": "<EMAIL>",
            "overrideBuildSteps":
            [
                "Hotfix :",
                "\n",
                "application : tg-crs-booking-consumer-su-treebo"
            ],
            "integrationTest": false,
            "withRefresh": false,
            "isDestroy": false,
            "allowDestroy": false,
            "forceRelease": false,
            "validationResult": null,
            "deploymentContextFilePath": "cf271ba8-6d32-42e6-854b-f83506df77223932348335885037877.zip",
            "releaseComment": "",
            "hotfixResources":
            [
                {
                    "resourceType": "application",
                    "resourceName": "tg-crs-booking-consumer-su-treebo"
                }
            ],
            "releaseReviewedBy": null,
            "approvedReleaseId": null,
            "validationResponses":
            [],
            "releaseTraceId": null,
            "parallelRelease": false,
            "statusDeprecated": "SUCCEEDED"
        }
    ],
    "pageable":
    {
        "sort":
        {
            "sorted": false,
            "unsorted": true,
            "empty": true
        },
        "pageSize": 15,
        "pageNumber": 1,
        "offset": 15,
        "paged": true,
        "unpaged": false
    },
    "last": false,
    "totalPages": 248,
    "totalElements": 3708,
    "first": false,
    "sort":
    {
        "sorted": false,
        "unsorted": true,
        "empty": true
    },
    "numberOfElements": 15,
    "size": 15,
    "number": 1,
    "empty": false
}
```
"""


# --- Pydantic Model Definitions ---

class DeploymentChangeApplied(CamelModel):
    resource_path: Optional[str] = None
    resource_key: Optional[str] = None
    type: Optional[str] = None

    @classmethod
    def model_validate_from_camel(cls, data: Dict[str, Any]):
        """Convert camelCase keys to snake_case and validate"""
        if not data:
            return cls()
        return super().model_validate_from_camel(data)

class AppDeploymentArtifact(CamelModel):
    id: Optional[str] = None
    created_by: Optional[str] = None
    creation_date: Optional[datetime] = None
    last_modified_date: Optional[datetime] = None
    last_modified_by: Optional[str] = None
    build_id: Optional[str] = None
    application_name: Optional[str] = None
    artifact_uri: Optional[str] = None
    cluster_id: Optional[str] = None
    release_stream: Optional[str] = None
    release_type: Optional[str] = None
    artifactory: Optional[str] = None
    build_description: Optional[str] = None
    created_on: Optional[datetime] = None  # Also exists here
    metadata: Optional[Any] = None
    applied_rule_version_id: Optional[str] = None
    applied_rule_id: Optional[str] = None
    applied_rule_name: Optional[str] = None
    repository_name: Optional[str] = None
    tag: Optional[str] = None
    repository: Optional[str] = None
    promoted: Optional[bool] = None
    registration_type: Optional[str] = None
    classified: Optional[bool] = None
    registration_value: Optional[str] = None
    versioning_key: Optional[str] = None
    number_of_versions: Optional[int] = None

    @field_validator('creation_date', 'last_modified_date', 'created_on', mode='before')
    @classmethod
    def parse_datetime(cls, value):
        """Parse ISO 8601 datetime strings to datetime objects"""
        if not value or isinstance(value, datetime):
            return value

        # Handle 'Z' timezone marker by replacing with +00:00
        if isinstance(value, str) and 'Z' in value:
            value = value.replace('Z', '+00:00')

        # Let Pydantic handle the parsing
        return value

class AppDeployment(CamelModel):
    app_name: Optional[str] = None
    artifact: Optional[AppDeploymentArtifact] = None

    @classmethod
    def _preprocess_data(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process nested artifact data"""
        if 'artifact' in data and data['artifact'] is not None:
            data['artifact'] = AppDeploymentArtifact.model_validate_from_camel(data['artifact'])
        return data

class HotfixResource(CamelModel):
    resource_type: Optional[str] = None
    resource_name: Optional[str] = None

class DeploymentRecord(CamelModel):
    # Required fields
    id: str
    created_on: datetime
    status: str

    # Optional fields
    deployment_job_type: Optional[str] = None
    codebuild_id: Optional[str] = None
    description: Optional[str] = None
    finished_on: Optional[datetime] = None
    time_taken_in_seconds: Optional[int] = None
    release_type: Optional[str] = None
    changes_applied: List[DeploymentChangeApplied] = Field(default_factory=list)
    app_deployments: List[AppDeployment] = Field(default_factory=list)
    migration_scripts_run: Optional[Any] = None  # Can be null or list
    error_logs: List[str] = Field(default_factory=list)
    deployment_type: Optional[str] = None
    stack_version: Optional[str] = None
    tf_version: Optional[str] = None
    signed_off: Optional[bool] = None
    deployment_context_version: Optional[str] = None
    triggered_by: Optional[str] = None
    override_build_steps: List[str] = Field(default_factory=list)
    integration_test: Optional[bool] = None
    with_refresh: Optional[bool] = None
    is_destroy: Optional[bool] = None
    allow_destroy: Optional[bool] = None
    force_release: Optional[bool] = None
    validation_result: Optional[Any] = None
    deployment_context_file_path: Optional[str] = None
    release_comment: Optional[str] = None
    hotfix_resources: List[HotfixResource] = Field(default_factory=list)
    release_reviewed_by: Optional[str] = None
    approved_release_id: Optional[str] = None
    validation_responses: List[Any] = Field(default_factory=list)
    release_trace_id: Optional[str] = None
    parallel_release: Optional[bool] = None
    status_deprecated: Optional[str] = None

    @field_validator('created_on', 'finished_on', mode='before')
    @classmethod
    def parse_datetime(cls, value):
        """Parse ISO 8601 datetime strings to datetime objects"""
        if not value or isinstance(value, datetime):
            return value

        # Handle 'Z' timezone marker by replacing with +00:00
        if isinstance(value, str) and 'Z' in value:
            value = value.replace('Z', '+00:00')

        # Let Pydantic handle the parsing
        return value

    @classmethod
    def _preprocess_data(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process nested data before validation"""
        # Validate required fields
        if 'id' not in data or 'createdOn' not in data or 'status' not in data:
            raise ValueError("Missing required fields for DeploymentRecord: id, createdOn, or status")

        # Process nested lists
        if 'changesApplied' in data and isinstance(data['changesApplied'], list):
            data['changesApplied'] = [
                DeploymentChangeApplied.model_validate_from_camel(item)
                for item in data['changesApplied']
                if isinstance(item, dict)
            ]

        if 'appDeployments' in data and isinstance(data['appDeployments'], list):
            data['appDeployments'] = [
                AppDeployment.model_validate_from_camel(item)
                for item in data['appDeployments']
                if isinstance(item, dict)
            ]
        else:
            data['appDeployments'] = []

        if 'hotfixResources' in data and isinstance(data['hotfixResources'], list):
            data['hotfixResources'] = [
                HotfixResource.model_validate_from_camel(item)
                for item in data['hotfixResources']
                if isinstance(item, dict)
            ]
        else:
            data['hotfixResources'] = []

        # Ensure errorLogs is a list of strings
        if 'errorLogs' in data:
            data['errorLogs'] = [str(item).strip() for item in data['errorLogs'] if item is not None]
        else:
            data['errorLogs'] = [] 

        return data

# --- Fetch Data Function (Modified to use Pydantic) ---

def fetch_deployment_data(base_url, total_records, page_size, username=None, token=None) -> List[DeploymentRecord]:
    """
    Fetches deployment data from the API, parses into DeploymentRecord models,
    by looping through pages, with optional authentication.

    Args:
        base_url (str): The base URL of the API endpoint.
        total_records (int): The total number of records to fetch.
        page_size (int): The number of records per page.
        username (str, optional): The username for basic authentication. Defaults to None.
        token (str, optional): The token/password for basic authentication. Defaults to None.

    Returns:
        list: A list of DeploymentRecord objects.
    """
    all_deployments: List[DeploymentRecord] = []
    num_pages = (total_records + page_size - 1) // page_size

    print(f"Fetching {total_records} records with a page size of {page_size}...")

    auth_tuple = (username, token) if username and token else None

    for page_number in range(1, num_pages + 1):
        url = f"{base_url.split('?')[0]}?pageNumber={page_number}&pageSize={page_size}&excludeStatus=STARTED,FAULT,IN_PROGRESS"
        print(f"  Fetching page {page_number}/{num_pages} from: {url}")
        try:
            response = requests.get(url, auth=auth_tuple)
            response.raise_for_status()
            data = response.json()

            if 'content' in data and isinstance(data['content'], list):
                for record_dict in data['content']:
                    try:
                        # Parse and validate each record into a DeploymentRecord model
                        deployment = DeploymentRecord.model_validate_from_camel(record_dict)
                        if deployment:
                            all_deployments.append(deployment)
                    except (ValueError, KeyError, TypeError) as e:
                        record_id = record_dict.get('id', 'N/A')
                        print(f"    Warning: Skipping malformed record (ID: {record_id}) from page {page_number}. Error: {e}")

                if len(all_deployments) >= total_records:
                    print(f"  Reached target of {total_records} records. Stopping fetch.")
                    break
            else:
                print(f"  Warning: 'content' key not found or not a list in response for page {page_number}. Skipping.")
        except requests.exceptions.RequestException as e:
            print(f"  Error fetching data for page {page_number}: {e}")
            print(f"  Response status code: {e.response.status_code if e.response else 'N/A'}")
            print(f"  Response text: {e.response.text if e.response else 'N/A'}")
            break

    return all_deployments[:total_records]

# --- Calculate DORA Metrics Function (Modified for Pydantic and improved MTTR) ---

def calculate_dora_metrics(deployments: List[DeploymentRecord]):
    """
    Calculates DORA metrics from the provided DeploymentRecord objects,
    excluding deployments with releaseType 'MAINTENANCE', and
    identifies top reasons for change failures.

    Args:
        deployments (list): A list of DeploymentRecord objects.

    Returns:
        dict: A dictionary containing the calculated DORA metrics and top failure reasons.
    """
    print("\nCalculating DORA metrics...")

    # Filter out deployments with releaseType 'MAINTENANCE'
    initial_total_deployments = len(deployments)
    filtered_deployments = [
        d for d in deployments if d.release_type != 'MAINTENANCE'
    ]

    print(f"Filtered out {initial_total_deployments - len(filtered_deployments)} 'MAINTENANCE' deployments.")
    print(f"Proceeding with {len(filtered_deployments)} relevant deployments.")

    total_successful_deployments = 0
    total_failed_deployments = 0
    lead_times = []
    deployment_dates = []

    # Track failures and recoveries by resource
    failed_deployments_by_resource = defaultdict(list)
    time_to_restore_values = []  # Mean Time To Restore (MTTR)

    all_error_logs = []

    # Process deployments (already sorted by created_on from the API)
    for deployment in filtered_deployments:
        status = deployment.status
        created_on = deployment.created_on  # Already a datetime object
        finished_on = deployment.finished_on  # Already an Optional[datetime]
        time_taken = deployment.time_taken_in_seconds

        # Extract resource key from changes_applied if available
        resource_key = None
        if deployment.changes_applied and len(deployment.changes_applied) > 0:
            resource_key = deployment.changes_applied[0].resource_key

        if status == 'SUCCEEDED':
            total_successful_deployments += 1

            # Track lead time (time taken to deploy)
            if time_taken is not None:
                lead_times.append(time_taken)

            # Track deployment date for frequency calculation
            if created_on:
                deployment_dates.append(created_on)

            # Calculate MTTR if this is a recovery from a previous failure
            if resource_key and failed_deployments_by_resource[resource_key]:
                # Get the most recent failure for this resource
                last_failed_deployment = failed_deployments_by_resource[resource_key][-1]

                # Ensure we have the necessary timestamps
                if (last_failed_deployment.finished_on or last_failed_deployment.created_on) and \
                   (finished_on or created_on):

                    try:
                        # Use the most accurate timestamps available
                        failure_time = last_failed_deployment.finished_on or last_failed_deployment.created_on
                        success_time = finished_on or created_on

                        # Calculate time to restore in seconds
                        time_to_restore = (success_time - failure_time).total_seconds()

                        # Only count positive values (future deployments can't fix past failures)
                        if time_to_restore > 0:
                            time_to_restore_values.append(time_to_restore)

                        # Clear the failure record since it's been resolved
                        failed_deployments_by_resource[resource_key] = []
                    except Exception as e:
                        print(f"Warning: Error calculating time to restore for resource {resource_key}: {e}")

        elif status == 'FAILED':
            total_failed_deployments += 1

            # Track the failure for future MTTR calculation
            if resource_key:
                failed_deployments_by_resource[resource_key].append(deployment)

            # Collect error logs for failure analysis
            if deployment.error_logs:
                for log_entry in deployment.error_logs:
                    if log_entry:
                        all_error_logs.append(log_entry.strip())

    # Total deployments after filtering 'MAINTENANCE'
    total_deployments = len(filtered_deployments)

    # Get the top 5 most common error messages
    top_failure_reasons = Counter(all_error_logs).most_common(5)

    # 1. Deployment Frequency (deployments per week)
    deployment_frequency = 0
    if deployment_dates:
        unique_days = set(d.date() for d in deployment_dates)
        if len(unique_days) > 1:
            min_date = min(deployment_dates)
            max_date = max(deployment_dates)
            total_time_span_days = (max_date - min_date).days
            if total_time_span_days > 0:
                # Calculate weekly average
                deployment_frequency = (total_deployments / total_time_span_days) * 7
            else:
                deployment_frequency = total_deployments
        else:
            deployment_frequency = total_deployments
            print("Note: Deployment frequency calculation might be less representative as all relevant deployments are on the same day.")

    # 2. Change Failure Rate (percentage of deployments that fail)
    change_failure_rate = 0
    if total_deployments > 0:
        change_failure_rate = (total_failed_deployments / total_deployments) * 100

    # 3. Mean Time to Restore Service (MTTR)
    # Using numpy for more robust statistical calculation
    mean_time_to_restore_service = 0
    median_time_to_restore_service = 0
    if time_to_restore_values:
        # Calculate both mean and median for a more complete picture
        mean_time_to_restore_service = np.mean(time_to_restore_values)
        median_time_to_restore_service = np.median(time_to_restore_values)

    # 4. Lead Time for Changes
    average_lead_time = 0
    median_lead_time = 0
    if lead_times:
        average_lead_time = np.mean(lead_times)
        median_lead_time = np.median(lead_times)

    metrics = {
        "total_deployments": total_deployments,
        "total_successful_deployments": total_successful_deployments,
        "total_failed_deployments": total_failed_deployments,
        "deployment_frequency_weekly_avg": deployment_frequency,
        "change_failure_rate_percent": change_failure_rate,
        "mean_time_to_restore_service_seconds": mean_time_to_restore_service,
        "median_time_to_restore_service_seconds": median_time_to_restore_service,
        "average_lead_time_seconds": average_lead_time,
        "median_lead_time_seconds": median_lead_time,
        "top_failure_reasons": top_failure_reasons,
    }

    return metrics

# --- Configuration ---
BASE_URL = "https://treebo-cp.console.facets.cloud/cc-ui/v1/clusters/62e8e0edce29ec00014423cb/deployments/search?pageNumber=1&pageSize=15&excludeStatus=STARTED,FAULT,IN_PROGRESS"
TOTAL_RECORDS_TO_FETCH = 500
PAGE_SIZE = 20

# --- Authentication Configuration ---
FACETS_USER = os.getenv("FACETS_USER")
FACETS_TOKEN = os.getenv("FACETS_TOKEN")

# --- Main Execution ---
if __name__ == "__main__":
    if not FACETS_USER or not FACETS_TOKEN:
        print("Error: FACETS_USER or FACETS_TOKEN environment variables are not set.")
        print("Please set them before running the script, e.g.:")
        print("  export FACETS_USER=\"your_username\"")
        print("  export FACETS_TOKEN=\"your_token\"")
    else:
        deployments = fetch_deployment_data(BASE_URL, TOTAL_RECORDS_TO_FETCH, PAGE_SIZE, FACETS_USER, FACETS_TOKEN)

        if deployments:
            print(f"\nTotal raw deployments fetched (and successfully parsed): {len(deployments)}")
            dora_metrics = calculate_dora_metrics(deployments)

            print("\n--- DORA Metrics ---")
            print(f"Total Relevant Deployments (excluding Maintenance): {dora_metrics['total_deployments']}")
            print(f"Total Successful Deployments: {dora_metrics['total_successful_deployments']}")
            print(f"Total Failed Deployments: {dora_metrics['total_failed_deployments']}")
            print(f"Deployment Frequency (Weekly Average): {dora_metrics['deployment_frequency_weekly_avg']:.2f}")
            print(f"Change Failure Rate: {dora_metrics['change_failure_rate_percent']:.2f}%")

            # Time to Restore Service (MTTR)
            print(f"Mean Time to Restore Service: {dora_metrics['mean_time_to_restore_service_seconds']:.2f} seconds")
            print(f"Median Time to Restore Service: {dora_metrics['median_time_to_restore_service_seconds']:.2f} seconds")

            # Lead Time for Changes
            print(f"Mean Lead Time for Changes: {dora_metrics['average_lead_time_seconds']:.2f} seconds")
            print(f"Median Lead Time for Changes: {dora_metrics['median_lead_time_seconds']:.2f} seconds")

            print("\n--- Top Reasons for Change Failures (from errorLogs) ---")
            if dora_metrics['top_failure_reasons']:
                for reason, count in dora_metrics['top_failure_reasons']:
                    print(f"- '{reason}' (Occurrences: {count})")
            else:
                print("No specific error logs found for failed deployments, or 'errorLogs' were empty/malformed.")
                print("Note: The accuracy of 'Top Reasons' depends on meaningful and consistent data in the 'errorLogs' field.")
        else:
            print("No deployment data fetched or all records were invalid. Cannot calculate DORA metrics.")
