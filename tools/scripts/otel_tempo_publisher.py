#!/usr/bin/env python3
"""
OpenTelemetry Tempo Publisher

This script demonstrates how to publish OpenTelemetry telemetry data to Tempo via the OpenTelemetry Collector.
It creates sample traces with spans and sends them to the configured OpenTelemetry Collector endpoint.

Usage:
  python otel_tempo_publisher.py [--service-name SERVICE_NAME] [--endpoint ENDPOINT]

Options:
  --service-name SERVICE_NAME  Name of the service generating telemetry [default: demo-service]
  --endpoint ENDPOINT          OTLP HTTP endpoint for OpenTelemetry Collector [default: opentelemetry-collector.monitoring.svc.cluster.local:4318/v1/traces]


pip install opentelemetry-api opentelemetry-sdk opentelemetry-exporter-otlp-proto-http

"""

import argparse
import logging
import random
import time
import uuid
from datetime import datetime

# OpenTelemetry imports
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.trace.status import Status, StatusCode
from opentelemetry.semconv.resource import ResourceAttributes

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Publish OpenTelemetry telemetry to Tempo"
    )
    parser.add_argument(
        "--service-name",
        default="demo-service",
        help="Name of the service generating telemetry",
    )
    parser.add_argument(
        "--endpoint",
        default="http://opentelemetry-collector.monitoring.svc.cluster.local:4318/v1/traces",
        help="OTLP HTTP endpoint for OpenTelemetry Collector",
    )
    return parser.parse_args()


def setup_tracer(service_name, endpoint):
    """Configure the OpenTelemetry tracer with OTLP exporter to the OpenTelemetry Collector."""
    # Create a resource with service information
    resource = Resource.create(
        {
            ResourceAttributes.SERVICE_NAME: service_name,
            ResourceAttributes.SERVICE_VERSION: "0.1.0",
            "environment": "production",
            "cluster": "treebo-tools-common",
        }
    )

    # Create a tracer provider with the resource
    tracer_provider = TracerProvider(resource=resource)

    # Create an OTLP HTTP exporter to send traces to the OpenTelemetry Collector
    otlp_exporter = OTLPSpanExporter(
        endpoint=endpoint,
    )

    # Add the exporter to the tracer provider
    tracer_provider.add_span_processor(BatchSpanProcessor(otlp_exporter))

    # Set the tracer provider as the global default
    trace.set_tracer_provider(tracer_provider)

    # Get a tracer from the provider
    tracer = trace.get_tracer(__name__)

    return tracer


def simulate_database_query(tracer):
    """Simulate a database query with a span."""
    with tracer.start_as_current_span("database.query") as span:
        # Add attributes to the span
        span.set_attribute("db.system", "postgresql")
        span.set_attribute("db.name", "users")
        span.set_attribute("db.statement", "SELECT * FROM users WHERE id = ?")
        span.set_attribute("db.user", "app_user")

        # Simulate query execution time
        query_time = random.uniform(0.05, 0.2)
        time.sleep(query_time)

        # Add events to the span
        span.add_event(
            "db.query.started",
            {"query_id": str(uuid.uuid4()), "timestamp": datetime.now().isoformat()},
        )

        # Simulate query result
        result_count = random.randint(0, 10)

        # Add more events
        span.add_event(
            "db.query.completed",
            {"rows_returned": result_count, "execution_time_ms": query_time * 1000},
        )

        # Set span status
        span.set_status(Status(StatusCode.OK))

        return result_count


def simulate_api_call(tracer):
    """Simulate an API call with a span."""
    with tracer.start_as_current_span("http.request") as span:
        # Add attributes to the span
        span.set_attribute("http.method", "GET")
        span.set_attribute("http.url", "https://api.example.com/users")
        span.set_attribute("http.status_code", 200)

        # Simulate API call time
        api_time = random.uniform(0.1, 0.5)
        time.sleep(api_time)

        # Add events to the span
        span.add_event("http.request.sent", {"timestamp": datetime.now().isoformat()})

        # Simulate some processing
        time.sleep(random.uniform(0.05, 0.1))

        # Add more events
        span.add_event(
            "http.response.received", {"response_size_bytes": random.randint(500, 2000)}
        )

        # Set span status
        span.set_status(Status(StatusCode.OK))


def simulate_request_processing(tracer):
    """Simulate processing a request with nested spans."""
    # Create a parent span for the entire request
    with tracer.start_as_current_span("request.process") as parent_span:
        # Add attributes to the parent span
        parent_span.set_attribute("request.id", str(uuid.uuid4()))
        parent_span.set_attribute("request.method", "POST")
        parent_span.set_attribute("request.path", "/api/v1/process")

        # Simulate some initial processing
        time.sleep(random.uniform(0.01, 0.05))

        # Call the API simulation as a child span
        simulate_api_call(tracer)

        # Simulate more processing
        time.sleep(random.uniform(0.01, 0.05))

        # Call the database query simulation as another child span
        result_count = simulate_database_query(tracer)

        # Add the result to the parent span
        parent_span.set_attribute("result.count", result_count)

        # Set the parent span status
        parent_span.set_status(Status(StatusCode.OK))


def main():
    """Main function to run the demo."""
    args = parse_args()

    logger.info(
        f"Setting up tracer for service '{args.service_name}' with endpoint '{args.endpoint}'"
    )
    tracer = setup_tracer(args.service_name, args.endpoint)

    try:
        # Generate a few traces
        for i in range(5):
            logger.info(f"Generating trace {i + 1}/5")
            simulate_request_processing(tracer)
            # Wait a bit between traces
            time.sleep(random.uniform(0.5, 1.5))

        logger.info("Successfully sent traces to Tempo")

        # Allow time for the batch processor to export spans
        time.sleep(5)

    except Exception as e:
        logger.error(f"Error generating traces: {e}")
        raise


if __name__ == "__main__":
    main()
