apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: flowise-secrets
  namespace: flowise
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: flowise-secrets
    creationPolicy: Owner
  data:
    - secretKey: FLOWISE_USERNAME
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/flowise
        property: username
    - secretKey: FLOWISE_PASSWORD
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/flowise
        property: password
