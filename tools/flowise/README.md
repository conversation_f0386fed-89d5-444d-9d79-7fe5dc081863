# Flowise

This tool deploys Flowise, a low-code tool for building customized LLM flows using LangChain.

## Overview

Flowise is an open-source UI visual tool to build your customized LLM flow using LangChain. It allows you to create AI applications with a drag-and-drop interface.

## Prerequisites

- Kubernetes cluster
- Ingress controller (nginx)
- External Secrets Operator (for credentials management)
- Persistent storage (EBS)

## Setup

1. Create the required secrets in AWS Parameter Store:
   ```bash
   aws ssm put-parameter --name "/treebo/flowise/username" --value "your-admin-username" --type "SecureString"
   aws ssm put-parameter --name "/treebo/flowise/password" --value "your-secure-password" --type "SecureString"
   ```

2. Deploy using ArgoCD or kubectl:
   ```bash
   kubectl apply -k tools/flowise/overlays/treebo-tools-common/
   ```

## Configuration

### Base Configuration
- **Namespace**: flowise
- **Image**: flowiseai/flowise:2.1.3
- **Port**: 3000
- **Storage**: 10Gi EBS volume

### Environment Variables
- `PORT`: 3000
- `FLOWISE_USERNAME`: Admin username (from Kubernetes secret)
- `FLOWISE_PASSWORD`: Admin password (from Kubernetes secret)
- `DATABASE_PATH`: /root/.flowise
- `LOG_LEVEL`: info

## Access

The application is accessible via:
- **URL**: https://flowise.ekscraving1775.treebo.com
- **Username**: Set via Kubernetes secret (populated by External Secret)
- **Password**: Set via Kubernetes secret (populated by External Secret)

## Features

- **Visual Flow Builder**: Drag-and-drop interface for building LLM flows
- **LangChain Integration**: Built on top of LangChain
- **Multiple LLM Support**: OpenAI, Anthropic, Hugging Face, and more
- **Vector Stores**: Support for various vector databases
- **Memory Management**: Conversation memory and context handling
- **API Integration**: REST API for programmatic access

## Usage

### Creating a Flow

1. Access the Flowise UI via the ingress URL
2. Log in with your credentials
3. Create a new chatflow
4. Drag and drop components to build your flow:
   - **LLM Models**: Choose your language model
   - **Chains**: Define the processing chain
   - **Memory**: Add conversation memory
   - **Vector Stores**: Connect to vector databases
   - **Tools**: Add external tools and APIs

### API Usage

Flowise provides REST APIs for:
- Creating and managing chatflows
- Sending messages to chatflows
- Managing vector stores
- Uploading documents

Example API call:
```bash
curl -X POST \
  https://flowise.ekscraving1775.treebo.com/api/v1/prediction/your-chatflow-id \
  -H 'Content-Type: application/json' \
  -d '{"question": "Hello, how are you?"}'
```

## Storage

- **Persistent Volume**: 10Gi EBS volume mounted at `/root/.flowise`
- **Data**: Stores chatflows, configurations, and uploaded files
- **Database**: SQLite database for metadata

## Monitoring

### Health Checks
- **Liveness Probe**: HTTP GET on port 3000
- **Readiness Probe**: HTTP GET on port 3000

### Logs
Check application logs:
```bash
kubectl logs -n flowise deployment/flowise
```

## Security

- **Authentication**: Username/password authentication
- **HTTPS**: TLS termination at ingress
- **Secrets**: Credentials stored in Kubernetes secrets (populated from AWS Parameter Store via External Secrets)
- **Network**: Internal cluster communication only

## Troubleshooting

### Common Issues

1. **Login failures**: Check external secrets are properly configured
2. **Storage issues**: Verify PVC is bound and accessible
3. **Performance**: Increase resource limits if needed

### Debug Commands

```bash
# Check pod status
kubectl get pods -n flowise

# Check logs
kubectl logs -n flowise deployment/flowise

# Check external secrets
kubectl get externalsecret -n flowise

# Check PVC
kubectl get pvc -n flowise
```

## Backup

To backup Flowise data:
```bash
kubectl exec -n flowise deployment/flowise -- tar czf - /root/.flowise > flowise-backup.tar.gz
```

To restore:
```bash
kubectl exec -n flowise deployment/flowise -- tar xzf - -C / < flowise-backup.tar.gz
```
