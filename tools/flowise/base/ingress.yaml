apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: flowise-ingress
  namespace: flowise
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: $(flowise_host)
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: flowise
            port:
              number: 3000
