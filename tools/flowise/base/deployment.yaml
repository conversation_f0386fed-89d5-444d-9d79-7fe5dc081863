apiVersion: apps/v1
kind: Deployment
metadata:
  name: flowise
  namespace: flowise
  labels:
    app: flowise
spec:
  replicas: 1
  selector:
    matchLabels:
      app: flowise
  template:
    metadata:
      labels:
        app: flowise
    spec:
      containers:
      - name: flowise
        image: flowiseai/flowise:3.0.0
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: PORT
          value: "3000"
        - name: FLOWISE_USERNAME
          valueFrom:
            secretKeyRef:
              name: flowise-secrets
              key: FLOWISE_USERNAME
        - name: FLOWISE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: flowise-secrets
              key: FLOWISE_PASSWORD
        - name: DATABASE_PATH
          value: "/root/.flowise"
        - name: APIKEY_PATH
          value: "/root/.flowise"
        - name: SECRETKEY_PATH
          value: "/root/.flowise"
        - name: BLOB_STORAGE_PATH
          value: "/root/.flowise/storage"
        - name: LOG_LEVEL
          value: "info"
        - name: DEBUG
          value: "false"
        volumeMounts:
        - name: flowise-data
          mountPath: /root/.flowise
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: flowise-data
        persistentVolumeClaim:
          claimName: flowise-pvc
