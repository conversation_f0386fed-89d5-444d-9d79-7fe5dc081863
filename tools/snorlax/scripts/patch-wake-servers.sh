#!/bin/bash

# Script to automatically patch snorlax wake server deployments with proper scheduling constraints
# This script should be run after creating sleepschedules

set -e

WORKLOAD_TYPE="${WORKLOAD_TYPE:-application-critical}"
NAMESPACE="${1:-nexus}"

echo "Patching snorlax wake server deployments in namespace: $NAMESPACE"
echo "Using workload type: $WORKLOAD_TYPE"

# Find all snorlax wake server deployments (they have app=snorlax label and start with "snorlax-")
DEPLOYMENTS=$(kubectl get deployments -n "$NAMESPACE" -l app=snorlax --no-headers -o custom-columns=":metadata.name" | grep "^snorlax-")

if [ -z "$DEPLOYMENTS" ]; then
    echo "No snorlax wake server deployments found in namespace $NAMESPACE"
    exit 0
fi

for deployment in $DEPLOYMENTS; do
    echo "Patching deployment: $deployment"
    
    # Check if deployment already has the required tolerations and nodeSelector
    HAS_TOLERATION=$(kubectl get deployment "$deployment" -n "$NAMESPACE" -o jsonpath='{.spec.template.spec.tolerations[?(@.key=="workload-type")].key}' 2>/dev/null || echo "")
    HAS_NODESELECTOR=$(kubectl get deployment "$deployment" -n "$NAMESPACE" -o jsonpath='{.spec.template.spec.nodeSelector.workload-type}' 2>/dev/null || echo "")
    
    if [ "$HAS_TOLERATION" = "workload-type" ] && [ "$HAS_NODESELECTOR" = "$WORKLOAD_TYPE" ]; then
        echo "Deployment $deployment already has proper scheduling constraints, skipping..."
        continue
    fi
    
    # Apply the patch
    kubectl patch deployment "$deployment" -n "$NAMESPACE" --type='merge' -p="{
        \"spec\": {
            \"template\": {
                \"spec\": {
                    \"tolerations\": [
                        {
                            \"key\": \"workload-type\",
                            \"operator\": \"Equal\",
                            \"value\": \"$WORKLOAD_TYPE\",
                            \"effect\": \"NoSchedule\"
                        }
                    ],
                    \"nodeSelector\": {
                        \"workload-type\": \"$WORKLOAD_TYPE\"
                    }
                }
            }
        }
    }"
    
    echo "Successfully patched deployment: $deployment"
done

echo "All snorlax wake server deployments have been patched!"
