# Default values from Snorlax Helm chart
# https://github.com/moonbeam-nyc/snorlax/tree/main/charts/snorlax

resources:
  limits:
    cpu: 200m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Node scheduling configuration for controller
tolerations:
  - key: workload-type
    operator: Equal
    value: "application-critical"
    effect: NoSchedule

nodeSelector:
  workload-type: application-critical

# Wake server configuration
wakeServer:
  tolerations:
    - key: workload-type
      operator: Equal
      value: "application-critical"
      effect: NoSchedule
  nodeSelector:
    workload-type: application-critical

# Add any custom values here
