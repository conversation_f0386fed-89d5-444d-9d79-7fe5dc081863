apiVersion: snorlax.moonbeam.nyc/v1beta1
kind: SleepSchedule
metadata:
  name: nexus-snorlax-scheduler
  namespace: nexus
spec:
  wakeTime: "8:00am"
  sleepTime: "10:00am"
  timezone: "Asia/Kolkata"
  deployments:
    - name: nexus-backend-service
    - name: nexus-celery-worker
    - name: nexus-frontend
    - name: nexus-mcp-service

  # (optional) the ingresses to update and point to the snorlax wake server,
  # which wakes your deployment when a request is received while it's
  # sleeping.
  # ingresses:
  # - name: your-app-ingress

  # (optional, defaults to all deployments) specify which deployments
  # must be ready to wake this ingress
  # requires:
  # - deployment:
  #     name: your-app-frontend
