apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

helmCharts:
  - name: snorlax
    repo: https://moonbeam-nyc.github.io/helm-charts
    version: 0.7.0
    releaseName: snorlax
    namespace: snorlax
    valuesFile: values.yaml
    includeCRDs: true

resources:
  - namespace.yaml
  - sleepschedule.yaml

# namespace: snorlax  # Don't set global namespace since sleepschedule needs to be in nexus namespace

components:
  - ../../../lib/common/components/application-critical

patches:
  # Patch wake server deployments created by snorlax to add proper scheduling constraints
  - target:
      kind: Deployment
      labelSelector: "app=snorlax"
    patch: |-
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: workload-type
            operator: Equal
            value: "application-critical"
            effect: NoSchedule
      - op: add
        path: /spec/template/spec/nodeSelector
        value:
          workload-type: application-critical
