# Process Exporter Configuration for treebo-production
# Provides detailed per-process metrics beyond what node-exporter offers

# Process matching configuration
config:
  # Define process groups to monitor
  process_names:
    # Monitor all Java processes (useful for Spring Boot apps)
    - name: "{{.Comm}}"
      cmdline:
        - 'java'
    
    # Monitor Python processes
    - name: "{{.Comm}}"
      cmdline:
        - 'python'
        - 'python3'
    
    # Monitor Node.js processes
    - name: "{{.Comm}}"
      cmdline:
        - 'node'
    
    # Monitor nginx processes
    - name: "{{.Comm}}"
      cmdline:
        - 'nginx'
    
    # Monitor specific application processes (production specific)
    - name: "treebo-prod-{{.Matches}}"
      cmdline:
        - 'gunicorn.*treebo.*prod'
        - 'uwsgi.*treebo.*prod'
        - 'gunicorn.*treebo'
        - 'uwsgi.*treebo'
    
    # Monitor database processes
    - name: "{{.Comm}}"
      cmdline:
        - 'postgres'
        - 'mysqld'
        - 'redis-server'
    
    # Monitor container runtime
    - name: "{{.Comm}}"
      cmdline:
        - 'containerd'
        - 'dockerd'
    
    # Monitor Kubernetes components
    - name: "{{.Comm}}"
      cmdline:
        - 'kubelet'
        - 'kube-proxy'

# Resource configuration - higher for production
resources:
  limits:
    cpu: 400m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

# Service monitor for Prometheus scraping
serviceMonitor:
  enabled: true
  interval: 15s  # More frequent scraping for production
  scrapeTimeout: 10s
  labels:
    app: process-exporter
    environment: production

# Security context
securityContext:
  runAsNonRoot: false  # Needs root to access /proc
  runAsUser: 0
  readOnlyRootFilesystem: true

# Host network and PID namespace access
hostNetwork: false
hostPID: true  # Required to see all processes

# Volume mounts for process information
extraVolumes:
  - name: proc
    hostPath:
      path: /proc
      type: Directory

extraVolumeMounts:
  - name: proc
    mountPath: /host/proc
    readOnly: true

# Environment variables
env:
  - name: PROC_FS_PATH
    value: "/host/proc"
  - name: ENVIRONMENT
    value: "production"

# Node selector to run on all nodes
nodeSelector: {}

# Tolerations to run on all nodes including masters
tolerations:
  - operator: Exists

# Update strategy - more conservative for production
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1

# Pod disruption budget - ensure availability
podDisruptionBudget:
  enabled: true
  minAvailable: 2  # Higher availability for production
