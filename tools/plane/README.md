# Plane.so

Plane is an open-source project management tool that helps teams plan, track, and collaborate on projects.

## Features

- **Project Management**: Create and manage projects with issues, cycles, and modules
- **Team Collaboration**: Real-time collaboration with team members
- **Issue Tracking**: Comprehensive issue tracking with custom fields
- **Roadmaps**: Visual roadmaps and project planning
- **Analytics**: Built-in analytics and reporting
- **Integrations**: GitHub, GitLab, and other tool integrations

## Architecture

This deployment uses the official Plane.so Helm chart and includes:

- **Frontend**: Next.js application serving the web interface
- **Backend**: Django REST API backend
- **Worker**: Celery worker for background tasks
- **Beat Worker**: Celery beat scheduler for periodic tasks
- **PostgreSQL Database**: For storing application data
- **Redis**: For caching and task queue
- **MinIO**: For file storage (can be configured to use S3)
- **Helm Chart**: Official Plane CE Helm chart from helm.plane.so

## Configuration

### Database Integration

Plane is configured to use PostgreSQL as the primary database:

- Database: `plane`
- User: `plane`
- Connection managed via External Secrets

### Storage

- **File Storage**: AWS S3 integration for file uploads
- **Database Storage**: 20Gi EBS volume for PostgreSQL data
- **Redis Storage**: 5Gi EBS volume for Redis data

### Security

- Service Account with IRSA for AWS integration
- External Secrets for sensitive configuration
- Non-root security context for all containers

## Deployment

### Prerequisites

1. External Secrets Operator installed
2. AWS Parameter Store configured with secrets
3. Nginx Ingress Controller
4. Cert-manager for TLS certificates
5. S3 bucket for file storage

### Install

```bash
# Deploy to treebo-tools-common cluster (requires --enable-helm flag)
kubectl apply --enable-helm -k tools/plane/overlays/treebo-tools-common/
```

### Access

- **Web Interface**: https://plane.treebo.tools
- **API**: https://plane-api.treebo.tools

## AWS Integration

### IAM Permissions

The service account permissions are automatically created by the S3 setup script:

- **S3 Access**: For file storage and uploads (bucket: `s3-plane-treebo-v1`)
- **SES Access**: For email notifications (defined in `tools/plane/policy.json`)

The setup script automatically merges the base S3 policy with additional permissions from `policy.json`.

To create the IAM role and S3 bucket, run:
```bash
# From clusters directory
npm run setup-s3-buckets-and-roles -- --cluster-name treebo-tools-common
```

### Required Secrets

Store these in AWS Parameter Store under `/treebo/production/eks/cluster-treebo-tools-common/plane-secrets`:

```json
{
  "databaseUrl": "*************************************************/plane",
  "redisUrl": "redis://:password@plane-redis-master:6379/0",
  "secretKey": "django-secret-key-for-plane",
  "awsAccessKeyId": "AKIA...",
  "awsSecretAccessKey": "secret-key",
  "postgresPassword": "secure-postgres-password",
  "userPassword": "secure-user-password"
}
```

### S3 Bucket

The S3 bucket `s3-plane-treebo-v1` is automatically created by the setup script with appropriate permissions for the service account.

## Components

### Frontend (plane-frontend)

- **Image**: `makeplane/plane-frontend:stable`
- **Port**: 3000
- **Resources**: 1 CPU, 2Gi memory

### Backend (plane-backend)

- **Image**: `makeplane/plane-backend:stable`
- **Port**: 8000
- **Resources**: 2 CPU, 4Gi memory
- **Health Checks**: `/api/` endpoint

### Worker (plane-worker)

- **Image**: `makeplane/plane-worker:stable`
- **Command**: `./bin/worker`
- **Resources**: 1 CPU, 2Gi memory

### Beat Worker (plane-beat-worker)

- **Image**: `makeplane/plane-worker:stable`
- **Command**: `./bin/beat`
- **Resources**: 0.5 CPU, 1Gi memory

## Monitoring

### Health Checks

- **Frontend**: HTTP health check on `/`
- **Backend**: HTTP health check on `/api/`
- **Workers**: Process-based monitoring

### Metrics

- Application metrics available through Django admin
- Database metrics via PostgreSQL exporter
- Redis metrics via Redis exporter

## Maintenance

### Backup

- Database backups handled by PostgreSQL configuration
- File storage backed up via S3 versioning
- Redis data can be recreated from database

### Updates

Update the image version in `overlays/treebo-tools-common/values.yaml` and redeploy:

```bash
kubectl apply --enable-helm -k tools/plane/overlays/treebo-tools-common/
```

### Database Migrations

Database migrations are handled automatically by the backend container on startup.

## Troubleshooting

### Common Issues

1. **Database Connection**: Check External Secrets and PostgreSQL pod status
2. **Redis Connection**: Verify Redis pod and connection string
3. **File Upload Issues**: Check S3 permissions and bucket configuration
4. **Worker Issues**: Check Celery worker logs and Redis connectivity

### Logs

```bash
# Frontend logs
kubectl logs -n plane deployment/plane-frontend

# Backend logs
kubectl logs -n plane deployment/plane-backend

# Worker logs
kubectl logs -n plane deployment/plane-worker
kubectl logs -n plane deployment/plane-beat-worker

# Database logs
kubectl logs -n plane deployment/plane-postgresql

# Redis logs
kubectl logs -n plane deployment/plane-redis-master
```

## Configuration

### Environment Variables

Key environment variables are configured via ConfigMap and Secrets:

- `DEBUG`: Set to "0" for production
- `DJANGO_SETTINGS_MODULE`: Django settings module
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `AWS_*`: AWS configuration for S3 storage
