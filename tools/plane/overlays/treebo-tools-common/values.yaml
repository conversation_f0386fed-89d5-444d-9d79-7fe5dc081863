ingress:
  enabled: true
  appHost: plane.ekscraving1775.treebo.com
  ingressClass: nginx

external_secrets:
  rabbitmq_existingSecret: plane-secrets
  pgdb_existingSecret: plane-secrets
  doc_store_existingSecret: plane-secrets
  app_env_existingSecret: plane-secrets
  live_env_existingSecret: plane-secrets

postgres:
  local_setup: false

redis:
  local_setup: false

rabbitmq:
  local_setup: true
  storageClass: ebs-sc

minio:
  local_setup: false

serviceAccount:
  create: true
  name: plane-sa
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::ACCOUNT_ID:role/role-eks-s3-plane-treebo-v1"
