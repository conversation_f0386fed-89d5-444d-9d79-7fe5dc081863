apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: plane

helmCharts:
  - name: plane-ce
    repo: https://helm.plane.so
    version: 1.1.2
    releaseName: plane
    namespace: plane
    valuesFile: values.yaml

resources:
  - namespace.yaml
  - external-secret.yaml

# Plane is an application workload - semi-critical for project management
components:
  - ../../../lib/common/components/application-semi-critical

# Add IAM role annotation to service account for S3 and SES access
patches:
  - target:
      kind: ServiceAccount
      name: plane-srv-account
    patch: |-
      - op: add
        path: /metadata/annotations/eks.amazonaws.com~1role-arn
        value: "arn:aws:iam::************:role/role-eks-s3-plane-treebo-v1"
