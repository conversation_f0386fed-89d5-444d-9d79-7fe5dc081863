apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: netdata-ingress
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
spec:
  ingressClassName: nginx
  rules:
    - host: "aab62199a8a5c433581409da7f70a631-800023927.ap-south-1.elb.amazonaws.com"
      http:
        paths:
          - path: /netdata/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: netdata
                port:
                  number: 19999
