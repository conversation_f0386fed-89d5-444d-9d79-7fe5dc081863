# OpenCost

OpenCost provides real-time cost monitoring and optimization for Kubernetes workloads. It integrates with your existing Prometheus setup to provide detailed cost breakdowns by namespace, deployment, service, and more.

## Overview

OpenCost gives you:

- **Real-time cost monitoring**: Track costs as they happen
- **Granular cost allocation**: Costs by namespace, deployment, pod, service
- **AWS integration**: Accurate cloud provider pricing
- **Resource efficiency insights**: Identify over/under-provisioned resources
- **Cost optimization recommendations**: Right-size your workloads

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Prometheus    │◄───│    OpenCost     │◄───│   AWS Billing   │
│   (existing)    │    │                 │    │      API        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  OpenCost UI    │
                       │ (Web Interface) │
                       └─────────────────┘
```

## Directory Structure

```
tools/opencost/
├── base/                           # Base OpenCost configuration
│   ├── kustomization.yaml         # Base kustomization
│   ├── namespace.yaml             # Monitoring namespace
│   └── values.yaml                # Base Helm values
├── overlays/
│   └── treebo-tools-common/       # Tools cluster configuration
│       ├── kustomization.yaml    # Overlay kustomization
│       ├── values.yaml           # Environment-specific values
│       └── external-secret.yaml  # AWS credentials and auth
└── README.md                      # This file
```

## Configuration

### Base Configuration

The base configuration (`base/values.yaml`) provides:
- Prometheus integration with existing kube-prometheus-stack
- Basic resource allocation
- Security contexts and pod security
- Service monitor for metrics export
- Persistent storage for cost data

### Environment-Specific Configuration

The treebo-tools-common overlay (`overlays/treebo-tools-common/values.yaml`) adds:
- Enhanced resource allocation for tools cluster
- Ingress configuration with basic auth
- TLS termination with cert-manager
- AWS credentials via external secrets
- Environment-specific labels and annotations

## Prerequisites

1. **Prometheus**: OpenCost requires Prometheus to be running
2. **External Secrets**: For AWS credentials management
3. **Cert-manager**: For TLS certificate management
4. **Nginx Ingress**: For web UI access

## AWS Integration

OpenCost integrates with AWS for accurate pricing data. You need to:

1. Create an IAM user with billing read permissions
2. Store credentials in your secret management system
3. Configure the external secret to provide AWS credentials

### Required AWS Permissions

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ce:GetDimensionValues",
                "ce:GetReservationCoverage",
                "ce:GetReservationPurchaseRecommendation",
                "ce:GetReservationUtilization",
                "ce:GetCostAndUsage",
                "ce:GetUsageReport",
                "ce:DescribeCostCategoryDefinition",
                "ce:GetRightsizingRecommendation",
                "ce:GetSavingsUtilization",
                "ce:GetSavingsPlansUtilization",
                "ce:ListCostCategoryDefinitions",
                "ce:GetCostCategories"
            ],
            "Resource": "*"
        }
    ]
}
```

## Access

Once deployed, OpenCost will be available at:
- **URL**: https://opencost.ekscraving1775.treebo.com
- **Authentication**: Basic auth (configured via external secret)

## Monitoring

OpenCost exports metrics to Prometheus via ServiceMonitor:
- Cost metrics by namespace, deployment, pod
- Resource utilization metrics
- Efficiency recommendations

## Usage Examples

### View Costs by Namespace
Navigate to the OpenCost UI and filter by namespace to see costs for specific applications.

### API Access
OpenCost provides a REST API for programmatic access:

```bash
# Get allocation data
curl "https://opencost.ekscraving1775.treebo.com/allocation?window=7d&aggregate=namespace"

# Get asset data
curl "https://opencost.ekscraving1775.treebo.com/assets?window=7d&aggregate=type"
```

## Troubleshooting

### Common Issues

1. **No cost data**: Verify AWS credentials and permissions
2. **Prometheus connection**: Check service name and port configuration
3. **UI not accessible**: Verify ingress configuration and DNS

### Logs

```bash
# Check OpenCost logs
kubectl logs -n monitoring deployment/opencost

# Check external secret status
kubectl get externalsecret -n monitoring opencost-aws-credentials
```
