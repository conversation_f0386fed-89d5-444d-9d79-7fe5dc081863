apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: opencost-aws-credentials
  namespace: monitoring
  labels:
    app.kubernetes.io/name: opencost
    environment: tools
    cluster: treebo-tools-common
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: opencost-aws-credentials
    creationPolicy: Owner
  data:
    - secretKey: AWS_ACCESS_KEY_ID
      remoteRef:
        key: secret/opencost/aws
        property: access_key_id
    - secretKey: AWS_SECRET_ACCESS_KEY
      remoteRef:
        key: secret/opencost/aws
        property: secret_access_key
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: opencost-basic-auth
  namespace: monitoring
  labels:
    app.kubernetes.io/name: opencost
    environment: tools
    cluster: treebo-tools-common
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: opencost-basic-auth
    creationPolicy: Owner
  data:
    - secretKey: auth
      remoteRef:
        key: secret/opencost/auth
        property: htpasswd
