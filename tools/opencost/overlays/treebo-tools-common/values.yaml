# OpenCost configuration for treebo-tools-common cluster
# Extends base configuration with environment-specific settings

# OpenCost configuration
opencost:
  # Prometheus configuration - connect to existing Prometheus
  prometheus:
    internal:
      enabled: true
      serviceName: prometheus-kube-prometheus-prometheus
      namespaceName: monitoring
      port: 9090
    external:
      enabled: false

  # AWS Cloud Provider configuration for cost data
  cloudProviderApiKey: ""  # Set via external secret
  
  # UI configuration
  ui:
    enabled: true
    
  # Export metrics to Prometheus for monitoring
  metrics:
    serviceMonitor:
      enabled: true
      additionalLabels:
        app.kubernetes.io/name: opencost
        environment: tools
        cluster: treebo-tools-common

# Enhanced resource allocation for tools cluster
resources:
  requests:
    cpu: 200m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi

# Environment-specific labels
commonLabels:
  environment: tools
  cluster: treebo-tools-common
  team: devops

# Ingress configuration for tools cluster
ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: opencost-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "OpenCost Authentication Required"
  hosts:
    - host: opencost.ekscraving1775.treebo.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: opencost-tls
      hosts:
        - opencost.ekscraving1775.treebo.com

# Persistence configuration for cost data
persistence:
  enabled: true
  storageClass: ebs-sc
  size: 20Gi
  accessMode: ReadWriteOnce
