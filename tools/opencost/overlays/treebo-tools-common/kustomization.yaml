apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: monitoring

# resources:
#   - external-secret.yaml

helmCharts:
  - name: opencost
    repo: https://opencost.github.io/opencost-helm-chart
    version: 2.1.5
    releaseName: opencost
    namespace: monitoring
    valuesFile: values.yaml
    includeCRDs: true

components:
  - ../../../lib/common/components/application-sub-critical
# # Patch VPA to target OpenCost deployment
# patches:
#   - target:
#       kind: VerticalPodAutoscaler
#       name: monitoring-tool-vpa
#     patch: |-
#       - op: replace
#         path: /metadata/name
#         value: opencost-vpa
#       - op: replace
#         path: /spec/targetRef/name
#         value: opencost
#       - op: add
#         path: /metadata/labels/app.kubernetes.io~1name
#         value: opencost
