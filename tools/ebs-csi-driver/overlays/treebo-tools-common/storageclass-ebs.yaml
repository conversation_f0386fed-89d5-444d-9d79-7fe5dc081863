apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: ebs-sc
  annotations:
    argocd.argoproj.io/sync-wave: "0"
provisioner: ebs.csi.aws.com
parameters:
  type: gp3
  tagSpecification_1: "Tags=[{Key=Name,Value=$(pvc.name)},{Key=kubernetes.io/cluster/treebo-tools-common,Value=owned},{Key=Cluster,Value=treebo-tools-common}]"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
