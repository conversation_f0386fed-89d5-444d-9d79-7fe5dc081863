#!/bin/bash

# CRDS has some limitations and fail when installed via helm
# The recommendation is to install them manully on serverside
# https://github.com/prometheus-community/helm-charts/issues/2217
# https://github.com/prometheus-operator/kube-prometheus?tab=readme-ov-file#quickstart

# Get directory listing from GitHub API and get files download url in the directory
FILES=$(curl -s https://api.github.com/repos/prometheus-operator/kube-prometheus/contents/manifests/setup?ref=v0.14.0 | jq -r '.[] | select(.type == "file" and (.name | endswith(".yaml"))) | .download_url')

# Apply each YAML file
for url in $FILES; do
  echo "Applying $url"
  kubectl apply --server-side -f "$url"
done

kubectl wait --for condition=Established --all CustomResourceDefinition --namespace=monitoring