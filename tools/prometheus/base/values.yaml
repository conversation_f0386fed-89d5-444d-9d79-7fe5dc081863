grafana:
  enabled: false

prometheusOperator:
  createCustomResource: false # CRDs are installed by install-crds.sh
  crds:
    enabled: false
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          preference:
            matchExpressions:
              - key: workload-type
                operator: In
                values:
                  - application-critical
  tolerations:
    - key: workload-type
      operator: Equal
      value: "application-critical"
      effect: NoSchedule

prometheus:
  prometheusSpec:
    ##########
    # enables prometheus to scrape metrics from pods from other namespaces
    # ref: https://kubernetes.github.io/ingress-nginx/user-guide/monitoring/#verify-ingress-nginx-controller-is-installed
    #
    podMonitorSelectorNilUsesHelmValues: false
    serviceMonitorSelectorNilUsesHelmValues: false
    #########
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: ebs-sc
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 5Gi # we are using mimir so this can be less and rentention should expire within a day
    remoteWrite:
      - url: http://mimir-gateway.monitoring.svc.cluster.local/api/v1/push
    affinity:
      nodeAffinity:
        preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
                - key: workload-type
                  operator: In
                  values:
                    - application-critical
    tolerations:
      - key: workload-type
        operator: Equal
        value: "application-critical"
        effect: NoSchedule

alertmanager:
  alertmanagerSpec:
    affinity:
      nodeAffinity:
        preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
                - key: workload-type
                  operator: In
                  values:
                    - application-critical
    tolerations:
      - key: workload-type
        operator: Equal
        value: "application-critical"
        effect: NoSchedule
