helmCharts:
  - name: kube-prometheus-stack
    repo: https://prometheus-community.github.io/helm-charts
    version: 71.1.0
    releaseName: prometheus
    namespace: monitoring
    valuesFile: values.yaml

# handled specially in values.yaml because this stack introduces custom types
components:
  #### NOTE: #######
  # More ciriticality is added to values as this stack introduces custom Kinds of CRDs
  #  was debugged by outputting kustomize build and inspecting the output
  - ../../lib/common/components/application-critical
##################
