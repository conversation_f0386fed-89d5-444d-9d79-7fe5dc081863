apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pyroscope-ingress
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  ingressClassName: nginx
  rules:
    - host: pyroscope.ekscraving1775.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: pyroscope
                port:
                  number: 4040
