helmCharts:
  - name: opentelemetry-demo
    repo: https://open-telemetry.github.io/opentelemetry-helm-charts
    version: 0.30.2
    releaseName: opentelemetry-demo
    namespace: opentelemetry-demo
    valuesFile: values.yaml

resources:
  - namespace.yaml

components:
  - ../../lib/common/components/application-sub-critical

patches:
  - target:
      kind: Deployment
      name: opentelemetry-demo-frontendproxy
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/env/9/value
        value: "grafana.monitoring.svc.cluster.local"
