# Basic values for OpenTelemetry Demo
# Sends traces to Tempo via OTLP HTTP endpoint
default:
  envOverrides:
    - name: OTEL_COLLECTOR_NAME
      value: opentelemetry-collector.monitoring.svc.cluster.local

opentelemetry-collector:
  enabled: false
  config:
    exporters:
      otlp:
        endpoint: tempo.monitoring.svc.cluster.local:4317
        tls:
          insecure: true
      otlphttp/prometheus:
        endpoint: http://opentelemetry-demo-prometheus-server:9090/api/v1/otlp
        tls:
          insecure: true

jaeger:
  enabled: false

prometheus:
  enabled: false

grafana:
  enabled: false

components:
  frontendProxy:
    # env:
    #   - name: GRAFANA_SERVICE_HOST
    #     value: grafana.monitoring.svc.cluster.local
    ingress:
      enabled: true
      annotations: {}
      ingressClassName: nginx
      hosts:
        - host: otel-demo.ekscraving1775.treebo.com
          paths:
            - path: /
              pathType: Prefix
              port: 8080
