# KEDA

## Installation

KEDA installation is split into two phases to avoid CRD annotation bloat issues:

### Phase 1: Install CRDs

```bash
# Run the CRD installation script
./tools/keda/overlays/treebo-tools-common/install-crds.sh
```

This script:
- Checks for existing KEDA CRDs with large annotations
- Removes problematic CRDs if found
- Installs clean CRDs directly from the KEDA repository
- Verifies the installation

### Phase 2: Install KEDA Components

```bash
# Apply the KEDA components using Kustomize
kubectl apply -k tools/keda/overlays/treebo-tools-common/
```

## Troubleshooting

### CRD Annotation Size Error

If you encounter the error:
```
The CustomResourceDefinition "scaledjobs.keda.sh" is invalid: metadata.annotations: Too long: may not be more than 262144 bytes
```

This is caused by He<PERSON>/Kustomize adding large annotations to CRDs. The solution is:

1. Run the CRD installation script to clean up and reinstall CRDs
2. Use `includeCRDs: false` in the Helm chart configuration
3. Install CRDs separately using the official KEDA CRD manifests

### Metrics Server Authentication Error

If you see this error in KEDA metrics server logs:
```
unable to load configmap based request-header-client-ca-file: configmaps "extension-apiserver-authentication" is forbidden: User "system:serviceaccount:keda:keda-metrics-server" cannot get resource "configmaps" in API group "" in the namespace "kube-system"
```

This is a known issue ([kedacore/charts#189](https://github.com/kedacore/charts/issues/189)) where the KEDA metrics server needs access to the authentication ConfigMap in `kube-system`. The fix is included in our configuration:

1. **RBAC Patch**: `rbac-patch.yaml` grants the necessary permissions
2. **Namespace Patch**: Ensures ClusterRoleBinding references the correct namespace
3. **Automatic Application**: Both patches are applied automatically via Kustomize

### Manual CRD Cleanup

If needed, you can manually remove KEDA CRDs:

```bash
kubectl delete crd scaledjobs.keda.sh scaledobjects.keda.sh triggerauthentications.keda.sh clustertriggerauthentications.keda.sh
```

## Configuration

The KEDA configuration includes:
- High availability with 2 replicas for all components
- Pod disruption budgets
- Certificate management via cert-manager
- Rolling update strategy
- AWS region configuration - Kubernetes Event-driven Autoscaling

This directory contains the KEDA (Kubernetes Event-driven Autoscaling) configuration for the Treebo infrastructure. KEDA allows applications to scale based on external events and metrics beyond traditional CPU/memory metrics.

## Overview

KEDA is a Kubernetes-based Event Driven Autoscaler that provides:

- **Event-driven scaling**: Scale based on external events (queues, streams, metrics)
- **Scale to zero**: Reduce costs by scaling deployments to zero when idle
- **Multiple scalers**: Support for 50+ event sources and metrics
- **Custom metrics**: Expose custom metrics to Kubernetes HPA
- **Job scaling**: Create jobs based on external events

## Architecture

KEDA consists of three main components:

1. **KEDA Operator**: Manages ScaledObject and ScaledJob custom resources
2. **Metrics API Server**: Exposes external metrics to Kubernetes HPA
3. **Admission Webhooks**: Validates KEDA resources

## Directory Structure

```
tools/keda/
├── base/                           # Base KEDA configuration
│   ├── kustomization.yaml         # Base kustomization
│   ├── namespace.yaml             # KEDA namespace
│   └── values.yaml                # Base Helm values
├── overlays/
│   └── treebo-tools-common/       # Tools cluster configuration
│       ├── kustomization.yaml    # Overlay kustomization
│       └── values.yaml           # Environment-specific values
└── examples/                      # Example configurations
    ├── scaledobject-cpu-memory.yaml  # CPU/Memory scaling examples
    ├── scaledobject-aws.yaml         # AWS services scaling examples
    └── scaledjob.yaml                # Job scaling examples
```

## Installation

### Using Kustomize

Include KEDA in your cluster manifest:

```yaml
resources:
  - ../../../tools/keda/overlays/treebo-tools-common
```

### Manual Installation

```bash
# Apply base configuration
kubectl apply -k tools/keda/base

# Or apply environment-specific overlay
kubectl apply -k tools/keda/overlays/treebo-tools-common
```

## Configuration

### Base Configuration

The base configuration (`base/values.yaml`) includes:
- High availability setup (2 replicas for each component)
- Security hardening (non-root containers, read-only filesystem)
- Resource limits and requests
- Prometheus monitoring integration
- Pod disruption budgets

### Environment-Specific Configuration

The treebo-tools-common overlay (`overlays/treebo-tools-common/values.yaml`) adds:
- Cluster-specific labels and annotations
- AWS region configuration
- Enhanced monitoring labels
- Optimized resource allocation for tools cluster

## Usage Examples

### 1. CPU/Memory Based Scaling

```yaml
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: api-scaler
spec:
  scaleTargetRef:
    name: api-deployment
  minReplicaCount: 2
  maxReplicaCount: 10
  triggers:
  - type: cpu
    metricType: Utilization
    metadata:
      value: "70"
  - type: memory
    metricType: Utilization
    metadata:
      value: "80"
```

### 2. Redis Queue Based Scaling

```yaml
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: worker-scaler
spec:
  scaleTargetRef:
    name: worker-deployment
  minReplicaCount: 0
  maxReplicaCount: 30
  triggers:
  - type: redis
    metadata:
      address: redis-master.redis.svc.cluster.local:6379
      listName: task_queue
      listLength: '5'
```

### 3. AWS SQS Based Scaling

```yaml
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: sqs-consumer-scaler
spec:
  scaleTargetRef:
    name: sqs-consumer-deployment
  minReplicaCount: 0
  maxReplicaCount: 50
  triggers:
  - type: aws-sqs-queue
    metadata:
      queueURL: https://sqs.ap-south-1.amazonaws.com/123456789012/my-queue
      queueLength: '5'
      awsRegion: ap-south-1
    authenticationRef:
      name: keda-aws-credentials
```

### 4. Prometheus Metrics Based Scaling

```yaml
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: prometheus-scaler
spec:
  scaleTargetRef:
    name: api-deployment
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
      metricName: http_requests_per_second
      threshold: '100'
      query: sum(rate(http_requests_total[2m]))
```

## Supported Scalers

KEDA supports 50+ scalers including:

### Message Queues
- Redis Lists/Streams
- AWS SQS
- RabbitMQ
- Apache Kafka
- Google Pub/Sub

### Databases
- PostgreSQL
- MySQL
- MongoDB
- InfluxDB

### Cloud Services
- AWS CloudWatch
- AWS Kinesis
- Azure Service Bus
- GCP Pub/Sub

### Monitoring Systems
- Prometheus
- Datadog
- New Relic

### Others
- HTTP endpoints
- Cron schedules
- External metrics

## AWS Integration

For AWS services, configure authentication using:

1. **EKS Pod Identity** (Recommended):
```yaml
apiVersion: keda.sh/v1alpha1
kind: TriggerAuthentication
metadata:
  name: keda-aws-credentials
spec:
  podIdentity:
    provider: aws-eks
```

2. **IAM Roles for Service Accounts**:
```yaml
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT:role/keda-role
```

## Monitoring

KEDA provides comprehensive monitoring through:

### Prometheus Metrics
- Operator metrics: `http://keda-operator.keda.svc.cluster.local:8080/metrics`
- Metrics API Server: `http://keda-metrics-apiserver.keda.svc.cluster.local:9022/metrics`
- Admission Webhooks: `http://keda-admission-webhooks.keda.svc.cluster.local:8080/metrics`

### Key Metrics
- `keda_scaler_active`: Number of active scalers
- `keda_scaler_errors_total`: Total scaler errors
- `keda_scaled_object_paused`: Paused ScaledObjects
- `keda_webhook_scaled_object_validation_total`: Webhook validations

### ServiceMonitor
ServiceMonitor resources are automatically created for Prometheus scraping.

## Troubleshooting

### Check KEDA Status
```bash
# Check KEDA pods
kubectl get pods -n keda

# Check KEDA logs
kubectl logs -n keda deployment/keda-operator
kubectl logs -n keda deployment/keda-metrics-apiserver

# Check ScaledObject status
kubectl get scaledobject
kubectl describe scaledobject <name>
```

### Common Issues

1. **Authentication Failures**: Verify AWS credentials and permissions
2. **Metric Collection**: Check external service connectivity
3. **Scaling Issues**: Verify deployment selector and HPA creation
4. **Webhook Errors**: Check admission webhook certificates

### Debug Commands
```bash
# Check HPA created by KEDA
kubectl get hpa

# Check external metrics
kubectl get --raw "/apis/external.metrics.k8s.io/v1beta1"

# Check KEDA CRDs
kubectl get crd | grep keda
```

## Security Considerations

- KEDA runs with minimal required permissions
- All containers run as non-root users
- Read-only root filesystems enabled
- Network policies can be applied to restrict traffic
- TLS enabled for webhook communications

## Best Practices

1. **Start Small**: Begin with simple CPU/memory scaling before complex event sources
2. **Monitor Costs**: Use scale-to-zero carefully to avoid cold start issues
3. **Set Limits**: Always configure maxReplicaCount to prevent runaway scaling
4. **Test Thoroughly**: Validate scaling behavior in staging environments
5. **Use Authentication**: Secure external service connections with proper auth
