---
# Additional ClusterRole for KEDA metrics server to access extension-apiserver-authentication ConfigMap
# This fixes the known issue: https://github.com/kedacore/charts/issues/189
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: keda-metrics-server-auth-reader
  labels:
    app.kubernetes.io/name: keda
    app.kubernetes.io/component: metrics-server
    app.kubernetes.io/part-of: keda
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  resourceNames: ["extension-apiserver-authentication"]
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding to grant the KEDA metrics server access to the authentication ConfigMap
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: keda-metrics-server-auth-reader
  labels:
    app.kubernetes.io/name: keda
    app.kubernetes.io/component: metrics-server
    app.kubernetes.io/part-of: keda
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: keda-metrics-server-auth-reader
subjects:
- kind: ServiceAccount
  name: keda-metrics-server
  namespace: keda
