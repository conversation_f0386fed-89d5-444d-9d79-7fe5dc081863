clusterName: cluster-treebo-tools-common

operator:
  replicaCount: 2

metricsServer:
  replicaCount: 2

webhooks:
  enabled: true
  replicaCount: 2

# prometheus:
#   metricServer:
#     enabled: true
#     port: 9022
#     portName: metrics
#     path: /metrics
#     serviceMonitor:
#       enabled: true
#       interval: 30s
#       scrapeTimeout: 10s
#       labels:
#         app: keda-metrics-server
#         environment: tools
#         cluster: treebo-tools-common
#   operator:
#     enabled: true
#     port: 8080
#     portName: metrics
#     path: /metrics
#     serviceMonitor:
#       enabled: true
#       interval: 30s
#       scrapeTimeout: 10s
#       labels:
#         app: keda-operator
#         environment: tools
#         cluster: treebo-tools-common
#   webhooks:
#     enabled: true
#     port: 8080
#     portName: metrics
#     path: /metrics
#     serviceMonitor:
#       enabled: true
#       interval: 30s
#       scrapeTimeout: 10s
#       labels:
#         app: keda-admission-webhooks
#         environment: tools
#         cluster: treebo-tools-common

podDisruptionBudget:
  operator:
    enabled: true
    minAvailable: 1
  metricServer:
    enabled: true
    minAvailable: 1
  webhooks:
    enabled: true
    minAvailable: 1

additionalLabels:
  environment: tools
  cluster: treebo-tools-common
  team: devops

# Certificate management
certificates:
  autoGenerated: true
  certManager:
    enabled: true

upgradeStrategy:
  operator:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  metricsApiServer:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  webhooks:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

env:
  - name: AWS_REGION
    value: "ap-south-1"
  - name: AWS_DEFAULT_REGION
    value: "ap-south-1"
# # Cluster-specific settings
# extraArgs:
#   keda:
#     - --zap-log-level=info
#     - --zap-encoder=console
#   metricsAdapter:
#     - --logtostderr=true
#     - --v=0

rbac:
  controlPlaneServiceAccountsNamespace: keda
