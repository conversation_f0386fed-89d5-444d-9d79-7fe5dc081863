#!/bin/bash

# KEDA CRD Installation Script
# This script handles the installation of KEDA CRDs separately to avoid annotation bloat

set -euo pipefail

KEDA_VERSION="2.17.2"
NAMESPACE="keda"

echo "Installing KEDA CRDs version ${KEDA_VERSION}..."

# Function to clean up existing CRDs if they have annotation issues
cleanup_existing_crds() {
    echo "Checking for existing KEDA CRDs..."

    local crds=(
        "scaledjobs.keda.sh"
        "scaledobjects.keda.sh"
        "triggerauthentications.keda.sh"
        "clustertriggerauthentications.keda.sh"
    )

    for crd in "${crds[@]}"; do
        if kubectl get crd "$crd" >/dev/null 2>&1; then
            echo "Found existing CRD: $crd"

            # Check annotation size
            local annotation_size
            annotation_size=$(kubectl get crd "$crd" -o jsonpath='{.metadata.annotations}' | wc -c)

            if [ "$annotation_size" -gt 200000 ]; then
                echo "CRD $crd has large annotations (${annotation_size} bytes), removing..."
                kubectl delete crd "$crd" --ignore-not-found=true
            else
                echo "CRD $crd annotations are acceptable (${annotation_size} bytes)"
            fi
        fi
    done
}

# Function to install CRDs directly from KEDA repository
install_crds() {
    echo "Installing KEDA CRDs from official repository..."

    # Download and apply CRDs directly
    kubectl apply --server-side -f "https://github.com/kedacore/keda/releases/download/v${KEDA_VERSION}/keda-${KEDA_VERSION}-crds.yaml"

    echo "KEDA CRDs installed successfully"
}

# Function to verify CRD installation
verify_crds() {
    echo "Verifying KEDA CRDs..."

    local crds=(
        "scaledjobs.keda.sh"
        "scaledobjects.keda.sh"
        "triggerauthentications.keda.sh"
        "clustertriggerauthentications.keda.sh"
    )

    for crd in "${crds[@]}"; do
        if kubectl get crd "$crd" >/dev/null 2>&1; then
            echo "✓ CRD $crd is installed"

            # Check annotation size
            local annotation_size
            annotation_size=$(kubectl get crd "$crd" -o jsonpath='{.metadata.annotations}' | wc -c)
            echo "  Annotation size: ${annotation_size} bytes"
        else
            echo "✗ CRD $crd is missing"
            return 1
        fi
    done

    echo "All KEDA CRDs are properly installed"
}

# Main execution
main() {
    echo "Starting KEDA CRD installation process..."

    # Create namespace if it doesn't exist
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

    # Clean up problematic CRDs
    cleanup_existing_crds

    # Install CRDs
    install_crds

    # Verify installation
    verify_crds

    echo "KEDA CRD installation completed successfully!"
    echo ""
}

# Run main function
main "$@"
