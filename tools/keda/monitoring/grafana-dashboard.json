{"dashboard": {"id": null, "title": "KEDA - Kubernetes Event-driven Autoscaling", "tags": ["keda", "autoscaling", "kubernetes"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "KEDA Operator Status", "type": "stat", "targets": [{"expr": "up{job=\"keda-operator\"}", "legendFormat": "Operator Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "Active ScaledObjects", "type": "stat", "targets": [{"expr": "sum(keda_scaler_active)", "legendFormat": "Active Scalers"}]}, {"id": 3, "title": "ScaledObject Errors", "type": "graph", "targets": [{"expr": "rate(keda_scaler_errors_total[5m])", "legendFormat": "Error Rate"}]}, {"id": 4, "title": "Webhook Validations", "type": "graph", "targets": [{"expr": "rate(keda_webhook_scaled_object_validation_total[5m])", "legendFormat": "Validation Rate"}]}, {"id": 5, "title": "KEDA Component Resource Usage", "type": "graph", "targets": [{"expr": "rate(container_cpu_usage_seconds_total{namespace=\"keda\"}[5m])", "legendFormat": "CPU Usage - {{pod}}"}, {"expr": "container_memory_working_set_bytes{namespace=\"keda\"}", "legendFormat": "Memory Usage - {{pod}}"}]}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}