apiVersion: apps/v1
kind: Deployment
metadata:
  name: typesense-dashboard
  labels:
    app: typesense-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: typesense-dashboard
  template:
    metadata:
      labels:
        app: typesense-dashboard
    spec:
      containers:
        - name: typesense-dashboard
          image: ghcr.io/bfritscher/typesense-dashboard:latest
          ports:
            - containerPort: 80
