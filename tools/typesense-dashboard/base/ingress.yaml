apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: typesense-dashboard-ingress
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/enable-access-log: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: typesense-dashboard
                port:
                  number: 80
