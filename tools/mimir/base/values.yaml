# this is a tweaked copy of small.yaml from the helm chart

alertmanager:
  persistentVolume:
    storageClass: ebs-sc
    enabled: true
  replicas: 2
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi
  statefulSet:
    enabled: true

compactor:
  persistentVolume:
    storageClass: ebs-sc
    size: 5Gi
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi

distributor:
  replicas: 2
  resources:
    limits:
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 250Mi

ingester:
  persistentVolume:
    size: 5Gi
    storageClass: ebs-sc # IMP: its just storageClass here and for store_gateway
  replicas: 3
  resources:
    limits:
      memory: 4Gi
    requests:
      cpu: 250m
      memory: 250Mi
  topologySpreadConstraints: {}

  # **NOTE**: Disabling affinity and zoneAwareReplication to keep resources minimal
  # affinity:
  #   podAntiAffinity:
  #     requiredDuringSchedulingIgnoredDuringExecution:
  #       - labelSelector:
  #           matchExpressions:
  #             - key: target # support for enterprise.legacyLabels
  #               operator: In
  #               values:
  #                 - ingester
  #         topologyKey: 'kubernetes.io/hostname'

  #       - labelSelector:
  #           matchExpressions:
  #             - key: app.kubernetes.io/component
  #               operator: In
  #               values:
  #                 - ingester
  #         topologyKey: 'kubernetes.io/hostname'

  zoneAwareReplication:
    enabled: false
    topologyKey: "kubernetes.io/hostname"

admin-cache:
  enabled: true
  replicas: 3

chunks-cache:
  enabled: true
  replicas: 3
  volumeClaimTemplates:
    - metadata:
        name: mimir-chunks-cache-pvc
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 5Gi
        storageClassName: ebs-sc
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi

index-cache:
  enabled: true
  replicas: 3
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi
  volumeClaimTemplates:
    - metadata:
        name: mimir-index-cache-pvc
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 5Gi
        storageClassName: ebs-sc

metadata-cache:
  enabled: true
  replicas: 3
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi
  volumeClaimTemplates:
    - metadata:
        name: mimir-metadata-cache-pvc
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 5Gi
        storageClassName: ebs-sc

results-cache:
  enabled: true
  replicas: 3
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi
  volumeClaimTemplates:
    - metadata:
        name: mimir-results-cache-pvc
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 5Gi
        storageClassName: ebs-sc

minio:
  enabled: false

overrides_exporter:
  replicas: 1
  resources:
    limits:
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 128Mi

querier:
  replicas: 1
  resources:
    limits:
      memory: 4Gi
    requests:
      cpu: 250m
      memory: 250Mi

query_frontend:
  replicas: 1
  resources:
    limits:
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 250Mi

ruler:
  replicas: 1
  resources:
    limits:
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 250Mi

store_gateway:
  persistentVolume:
    size: 5Gi
    storageClass: ebs-sc # IMP: its just storageClass here and for ingester
  replicas: 3
  resources:
    limits:
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 250Mi
  topologySpreadConstraints: {}
  # **NOTE**: Disabling affinity and zoneAwareReplication to keep resources minimal
  # affinity:
  #   podAntiAffinity:
  #     requiredDuringSchedulingIgnoredDuringExecution:
  #       - labelSelector:
  #           matchExpressions:
  #             - key: target # support for enterprise.legacyLabels
  #               operator: In
  #               values:
  #                 - store-gateway
  #         topologyKey: 'kubernetes.io/hostname'

  #       - labelSelector:
  #           matchExpressions:
  #             - key: app.kubernetes.io/component
  #               operator: In
  #               values:
  #                 - store-gateway
  #         topologyKey: 'kubernetes.io/hostname'
  zoneAwareReplication:
    enabled: false
    topologyKey: "kubernetes.io/hostname"

nginx:
  enabled: false # nginx is deprecated in favor of gateway

gateway:
  enabled: true
  enabledNonEnterprise: true
  replicas: 1
  ingress:
    enabled: true
    ingressClassName: nginx
    hosts:
      - host: mimir.ekscraving1775.treebo.com
        paths:
          - path: /
            pathType: Prefix
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      nginx.ingress.kubernetes.io/enable-access-log: "true"
      nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
    tls: null
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 250Mi

mimir:
  structuredConfig:
    common:
      storage:
        backend: s3
        s3:
          endpoint: s3.ap-south-1.amazonaws.com
          region: ap-south-1
          bucket_name: s3-mimir-treebo-v1

    blocks_storage:
      storage_prefix: blocks
      s3:
        endpoint: s3.ap-south-1.amazonaws.com
        region: ap-south-1
        bucket_name: s3-mimir-treebo-v1

    alertmanager_storage:
      storage_prefix: alertmanager
      s3:
        endpoint: s3.ap-south-1.amazonaws.com
        region: ap-south-1
        bucket_name: s3-mimir-treebo-v1

    ruler_storage:
      storage_prefix: ruler
      s3:
        endpoint: s3.ap-south-1.amazonaws.com
        region: ap-south-1
        bucket_name: s3-mimir-treebo-v1

runtimeConfig:
  overrides:
    "otel-collector-staging":
      max_global_series_per_user: 750000 # 5x the default limit
    "otel-collector-tools-common":
      max_global_series_per_user: 750000 # 5x the default limit
    "otel-collector-production":
      max_global_series_per_user: 1500000 # 10x the default limit

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: $(mimir_role_arn)
