namespace: monitoring

helmCharts:
  - name: mimir-distributed
    repo: https://grafana.github.io/helm-charts
    version: 5.7.0
    releaseName: mimir
    namespace: monitoring
    valuesFile: values.yaml
    includeCRDs: true

patches:
  - patch: |-
      apiVersion: batch/v1
      kind: Job
      metadata:
        name: mimir-smoke-test
        namespace: monitoring
      $patch: delete

components:
  - ../../lib/common/components/application-critical
