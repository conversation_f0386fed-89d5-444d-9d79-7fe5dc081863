#!/bin/bash

# Script to install Karp<PERSON> and its CRDs
# Usage: ./install.sh <environment-name>

set -e

if [ -z "$1" ]; then
  echo "Error: No environment name provided."
  echo "Usage: $0 <environment-name>"
  exit 1
fi

ENV_NAME="$1"
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

echo "Installing Karpenter and its CRDs for environment: $ENV_NAME"

# Build Karpenter resources and store in a variable to check if empty
KARPENTER_RESOURCES=$(kustomize build --enable-helm --load-restrictor=LoadRestrictionsNone $SCRIPT_DIR/overlays/$ENV_NAME)
if [ -z "$KARPENTER_RESOURCES" ]; then
  echo "Warning: No Karpenter resources found to apply"
else
  echo "Applying Karpenter resources..."
  MAX_RETRIES=5
  RETRY_COUNT=0

  # The while loop is necessary because:
  # 1. CRDs might not be immediately available after installation
  # 2. There may be race conditions with webhook configurations
  # 3. Some resources depend on others being fully established
  # 4. Network or API server issues might cause temporary failures
  while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if echo "$KARPENTER_RESOURCES" | kubectl apply -f -; then
      echo "✅ Successfully applied Karpenter resources"
      break
    else
      RETRY_COUNT=$((RETRY_COUNT+1))
      if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
        echo "❌ Failed to apply Karpenter resources after $MAX_RETRIES attempts"
        exit 1
      fi
      echo "Retrying to apply Karpenter resources ($RETRY_COUNT/$MAX_RETRIES)..."
      sleep 5
    fi
  done
fi

echo "Karpenter installation completed for $ENV_NAME"
