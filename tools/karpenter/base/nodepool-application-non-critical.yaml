apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-non-critical-nodepool
  namespace: karpenter
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot"] # Spot only
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3a.small"
            - "t3a.medium"
            - "t3a.large"
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: topology.kubernetes.io/zone
          operator: In
          values: ["ap-south-1a", "ap-south-1b", "ap-south-1c"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: default-nodeclass
      # Taint to ensure only non-critical workloads run here
      # This prevents critical workloads from "spilling over" to cheap spot nodes
      taints:
        - key: workload-type
          value: application-non-critical
          effect: NoSchedule
      expireAfter: "168h" # 7 days - reduce disruption frequency for non-critical workloads
    metadata:
      labels:
        workload-type: application-non-critical
        pod-capacity-type: SPOT
  # Limited capacity for non-critical workloads
  limits:
    cpu: "8"
    memory: "16Gi"
  disruption:
    consolidationPolicy: "WhenEmptyOrUnderutilized"
    consolidateAfter: "15m" # Less aggressive consolidation to reduce pod churn
  weight: 25 # Lowest weight for non-critical workloads
