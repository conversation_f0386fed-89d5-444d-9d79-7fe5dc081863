apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-critical-nodepool
  namespace: karpenter
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["reserved", "on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "m5a.large"
            - "t3a.medium"
            - "t3a.large"
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: topology.kubernetes.io/zone
          operator: In
          values: ["ap-south-1a", "ap-south-1b", "ap-south-1c"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: default-nodeclass
      taints:
        - key: workload-type
          value: application-critical
          effect: NoSchedule
      expireAfter: "336h" # 14 days - replace nodes bi-weekly for critical workloads
    metadata:
      labels:
        workload-type: application-critical
        pod-capacity-type: ON_DEMAND
  limits:
    cpu: "40"
    memory: "40Gi"
  disruption:
    consolidationPolicy: "WhenEmptyOrUnderutilized"
    consolidateAfter: "60m" # Longer time for critical workloads to ensure stability
  weight: 100 # Highest weight for critical workloads
