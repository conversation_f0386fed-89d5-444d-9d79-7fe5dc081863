apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: default-nodeclass
  namespace: karpenter
spec:
  amiFamily: AL2023
  # Role will be patched per environment
  role: $(PLACEHOLDER_NODE_ROLE)
  metadataOptions:
    # very very important as ALB will fail
    # https://github.com/kubernetes-sigs/aws-load-balancer-controller/issues/1561#issuecomment-2303924757
    # https://github.com/kubernetes-sigs/karpenter/issues/1769
    httpEndpoint: enabled
    httpPutResponseHopLimit: 3
    httpTokens: required
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: $(PLACEHOLDER_CLUSTER_NAME)
        treebo_env: $(PLACEHOLDER_ENVIRONMENT)
  securityGroupSelectorTerms:
    - name: $(PLACEHOLDER_NODE_SECURITY_GROUP_NAME)
    - tags:
        karpenter.sh/discovery: $(PLACEHOLDER_CLUSTER_NAME)
        treebo_env: $(PLACEHOLDER_ENVIRONMENT)
  amiSelectorTerms:
    # Use dynamic AMI selection instead of hard-coded AMI
    - tags:
        karpenter.sh/discovery: $(PLACEHOLDER_CLUSTER_NAME)
        # kubernetes.io/arch: amd64
  tags:
    karpenter.sh/discovery: $(PLACEHOLDER_CLUSTER_NAME)
    held-by: $(k8s-PLACEHOLDER_CLUSTER_NAME)
    environment: $(PLACEHOLDER_ENVIRONMENT)
    treebo_env: $(PLACEHOLDER_ENVIRONMENT)
    team: devops
    Name: $(instance-PLACEHOLDER_CLUSTER_NAME)
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 20Gi
        volumeType: gp3
        deleteOnTermination: true
