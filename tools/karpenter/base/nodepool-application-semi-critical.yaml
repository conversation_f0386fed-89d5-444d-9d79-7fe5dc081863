apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-semi-critical-nodepool
  namespace: karpenter
spec:
  template:
    spec:
      requirements:
        # Order matters - <PERSON><PERSON><PERSON> tries capacity types in this order
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["reserved", "on-demand", "spot"] # Prefers on-demand/reserved, falls back to spot
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "m5a.large"
            - "t3a.medium"
            - "t3a.large"
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: topology.kubernetes.io/zone
          operator: In
          values: ["ap-south-1a", "ap-south-1b", "ap-south-1c"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: default-nodeclass
      taints:
        - key: workload-type
          value: application-semi-critical
          effect: NoSchedule
      expireAfter: "168h" # 7 days - replace nodes weekly for semi-critical workloads
    metadata:
      labels:
        workload-type: application-semi-critical
        pod-capacity-type: MIXED
  limits:
    cpu: "20"
    memory: "32Gi"
  disruption:
    consolidationPolicy: "WhenEmptyOrUnderutilized"
    consolidateAfter: "30m" # Moderate time for semi-critical workloads
  weight: 75 # High-medium weight for semi-critical workloads
