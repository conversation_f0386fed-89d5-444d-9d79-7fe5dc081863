apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-sub-critical-nodepool
  namespace: karpenter
spec:
  template:
    spec:
      requirements:
        # Order matters - <PERSON><PERSON><PERSON> tries capacity types in this order
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "reserved", "on-demand"] # Prefers spot, falls back to on-demand/reserved
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3a.medium"
            - "t3a.large"
            - "m5a.large"
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: topology.kubernetes.io/zone
          operator: In
          values: ["ap-south-1a", "ap-south-1b", "ap-south-1c"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: default-nodeclass
      taints:
        - key: workload-type
          value: application-sub-critical
          effect: NoSchedule
      expireAfter: "72h" # 3 days - replace nodes more frequently for sub-critical workloads
    metadata:
      labels:
        workload-type: application-sub-critical
        pod-capacity-type: MIXED
  limits:
    cpu: "32"
    memory: "64Gi"
  disruption:
    consolidationPolicy: "WhenEmptyOrUnderutilized"
    consolidateAfter: "15m" # Moderate time for sub-critical workloads
  weight: 50 # Medium weight for sub-critical workloads
