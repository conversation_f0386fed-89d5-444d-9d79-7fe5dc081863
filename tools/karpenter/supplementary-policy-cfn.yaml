AWSTemplateFormatVersion: "2010-09-09"
Description: "Supplementary IAM policy for Karpenter to allow spot instance request tagging and KMS operations"

Parameters:
  ClusterName:
    Type: String
    Description: "EKS cluster name"
Resources:
  KarpenterSupplementaryPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: !Sub "KarpenterSupplementaryPolicy-v1-${ClusterName}"
      Description: "Supplementary policy for Karpenter to allow spot instance request tagging and KMS operations"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: "AllowSpotInstanceRequestTagging"
            Effect: "Allow"
            Resource:
              - !Sub "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:spot-instances-request/*"
            Action:
              - "ec2:CreateTags"
            Condition:
              StringEquals:
                !Sub "aws:RequestTag/kubernetes.io/cluster/${ClusterName}": "owned"
                "ec2:CreateAction":
                  - "RunInstances"
                  - "CreateFleet"
              StringLike:
                "aws:RequestTag/karpenter.sh/nodepool": "*"
          - Sid: "AllowUnconditionalSpotInstanceRequestTagging"
            Effect: "Allow"
            Resource:
              - !Sub "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:spot-instances-request/*"
            Action:
              - "ec2:CreateTags"
          - Sid: "AllowAccessForEBSThroughKMS1"
            Effect: "Allow"
            Resource: "*"
            Action:
              - "kms:CreateGrant"
              - "kms:ListGrants"
              - "kms:RevokeGrant"
              - "kms:Encrypt"
              - "kms:Decrypt"
              - "kms:ReEncrypt*"
              - "kms:GenerateDataKey*"
              - "kms:DescribeKey"
            Condition:
              StringEquals:
                "kms:ViaService": !Sub "ec2.${AWS::Region}.amazonaws.com"
                "kms:CallerAccount": !Sub "${AWS::AccountId}"

Outputs:
  SupplementaryPolicyArn:
    Description: "ARN of the created supplementary policy"
    Value: !Ref KarpenterSupplementaryPolicy
