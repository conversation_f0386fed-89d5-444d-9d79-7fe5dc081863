apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: default-nodeclass
  namespace: karpenter
spec:
  role: KarpenterNodeRole-cluster-treebo-staging
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: cluster-treebo-staging
        treebo_env: staging
  securityGroupSelectorTerms:
    - tags:
        kubernetes.io/cluster/cluster-treebo-staging: owned
  amiSelectorTerms:
    - tags:
        karpenter.sh/discovery: cluster-treebo
        # kubernetes.io/arch: amd64
  tags:
    karpenter.sh/discovery: cluster-treebo-staging
    held-by: k8s-cluster-treebo-staging
    environment: staging
    treebo_env: staging
    team: devops
    Name: instance-cluster-treebo-staging
