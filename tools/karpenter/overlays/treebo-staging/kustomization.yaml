apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

helmCharts:
  # Install Karpenter CRDs first
  - name: karpenter-crd
    repo: "oci://public.ecr.aws/karpenter"
    version: 1.5.0
    releaseName: karpenter-crd
    namespace: karpenter
    includeCRDs: true
  # Install Karpenter controller
  - name: karpenter
    repo: "oci://public.ecr.aws/karpenter"
    version: 1.5.0
    releaseName: karpenter
    namespace: karpenter
    valuesFile: values.yaml

patches:
  - path: nodeclass-patch.yaml
    target:
      kind: EC2NodeClass
      name: default-nodeclass
      namespace: karpenter
  - path: nodepool-limits-patch.yaml
