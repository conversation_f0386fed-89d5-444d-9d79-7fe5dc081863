settings:
  clusterName: cluster-treebo-staging
  interruptionQueue: cluster-treebo-staging
  aws:
    defaultInstanceProfile: KarpenterNodeRole-cluster-treebo-staging

serviceAccount:
  name: karpenter-sa
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/KarpenterServiceAccountRole-treebo-staging

resources:
  requests:
    cpu: 100m
    memory: 128Mi
  limits:
    cpu: 500m
    memory: 512Mi
