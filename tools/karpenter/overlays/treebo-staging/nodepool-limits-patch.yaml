# NodePool resource limit patches for treebo-staging (Staging)
# Lower limits for cost optimization

# 1. Critical NodePool - Mission Critical Staging Workloads
#    Target: application-critical-nodepool
#    Capacity: Reserved/On-demand instances
#    Use Case: Critical staging tests, production-like services
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "30"
    memory: "30Gi"

---
# 2. Semi-Critical NodePool - Important Staging Workloads
#    Target: application-semi-critical-nodepool  
#    Capacity: Mixed (reserved/on-demand/spot)
#    Use Case: Important staging services, integration tests
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-semi-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "25"
    memory: "25Gi"

---
# 3. Sub-Critical NodePool - Standard Staging Workloads
#    Target: application-sub-critical-nodepool
#    Capacity: Spot-preferred instances
#    Use Case: Standard staging workloads, feature testing
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-sub-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "20"
    memory: "20Gi"

---
# 4. Non-Critical NodePool - Development/Testing Workloads
#    Target: application-non-critical-nodepool
#    Capacity: Spot instances only
#    Use Case: Development, testing, experimental workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-non-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "15"
    memory: "15Gi"
