# NodePool resource limit patches for treebo-tools-common (Production)
# Higher limits for production workloads

# 1. Critical NodePool - Highest Priority Workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-critical-nodepool
  namespace: karpenter
spec:
  template:
    spec:
      expireAfter: "168h" # 7 days - replace nodes weekly for critical workloads
  limits:
    cpu: "50"
    memory: "50Gi"

---
# 2. Semi-Critical NodePool - Important Workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-semi-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "40"
    memory: "40Gi"

---
# 3. Sub-Critical NodePool - Standard Workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-sub-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "40"
    memory: "40Gi"

---
# 4. Non-Critical NodePool - Development/Testing Workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: application-non-critical-nodepool
  namespace: karpenter
spec:
  limits:
    cpu: "20"
    memory: "20Gi"
