settings:
  clusterName: cluster-treebo-tools-common
  interruptionQueue: cluster-treebo-tools-common
  aws:
    defaultInstanceProfile: KarpenterNodeRole-cluster-treebo-tools-common

serviceAccount:
  name: karpenter-sa
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/KarpenterServiceAccountRole-treebo-tools-common

serviceMonitor:
  enabled: true

resources:
  requests:
    cpu: 100m
    memory: 128Mi
  limits:
    cpu: 500m
    memory: 512Mi
