apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: default-nodeclass
  namespace: karpenter
spec:
  role: KarpenterNodeRole-cluster-treebo-tools-common
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: cluster-treebo-tools-common
        treebo_env: production
  securityGroupSelectorTerms:
    - tags:
        kubernetes.io/cluster/cluster-treebo-tools-common: owned
  amiSelectorTerms:
    - tags:
        karpenter.sh/discovery: cluster-treebo
        # kubernetes.io/arch: amd64
  tags:
    karpenter.sh/discovery: cluster-treebo-tools-common
    held-by: k8s-cluster-treebo-tools-common
    environment: production
    treebo_env: production
    team: devops
    Name: instance-cluster-treebo-tools-common
