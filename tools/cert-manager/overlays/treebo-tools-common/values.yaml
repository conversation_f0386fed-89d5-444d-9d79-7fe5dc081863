global:
  leaderElection:
    namespace: cert-manager

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

config:
  metricsTLSConfig:
    dynamic:
      secretNamespace: "cert-manager"
      secretName: "cert-manager-metrics-ca"
      dnsNames:
        - cert-manager-metrics

podDisruptionBudget:
  enabled: true
  minAvailable: 1

webhook:
  hostNetwork: true
  securePort: 10260
  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 64Mi
  config:
    metricsTLSConfig:
      dynamic:
        secretNamespace: "cert-manager"
        secretName: "cert-manager-metrics-ca"
        dnsNames:
          - cert-manager-metrics

cainjector:
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi
  config:
    metricsTLSConfig:
      dynamic:
        secretNamespace: "cert-manager"
        secretName: "cert-manager-metrics-ca"
        dnsNames:
          - cert-manager-metrics

installCRDs: true

# Prometheus monitoring
prometheus:
  enabled: true
  servicemonitor:
    enabled: true
    prometheusInstance: default
    targetPort: 9402
    path: /metrics
    interval: 60s
    scrapeTimeout: 30s
