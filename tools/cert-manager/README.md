# cert-manager

cert-manager is a Kubernetes add-on to automate the management and issuance of TLS certificates from various issuing sources.

## Overview

cert-manager will ensure certificates are valid and up to date periodically, and attempt to renew certificates at an appropriate time before expiry.

## Features

- **Automatic Certificate Management**: Automatically provisions and renews TLS certificates
- **Multiple Issuers**: Supports Let's Encrypt, HashiCorp Vault, Venafi, self-signed, and CA issuers
- **Kubernetes Native**: Uses Custom Resource Definitions (CRDs) for configuration
- **High Availability**: Configured with multiple replicas for production use
- **Monitoring**: Prometheus metrics and monitoring support

## Components

- **Controller**: Main cert-manager controller that manages certificates
- **Webhook**: Admission webhook for validating and mutating cert-manager resources
- **CA Injector**: Injects CA bundles into webhooks and API services
- **ACME Solver**: Handles ACME challenges for Let's Encrypt certificates

## Installation

### Prerequisites

- Kubernetes cluster
- Helm 3.x (handled by <PERSON><PERSON><PERSON>)

### Deploy cert-manager

```bash
# Deploy to tools cluster
kubectl apply -k tools/cert-manager/overlays/treebo-tools-common
```

### Verify Installation

```bash
# Check cert-manager pods
kubectl get pods -n cert-manager

# Check CRDs
kubectl get crd | grep cert-manager

# Check cert-manager logs
kubectl logs -n cert-manager -l app.kubernetes.io/name=cert-manager
```

## Usage

### Create a Self-Signed ClusterIssuer

```yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: selfsigned-issuer
spec:
  selfSigned: {}
```

### Create a Certificate

```yaml
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: example-cert
  namespace: default
spec:
  secretName: example-cert-tls
  issuerRef:
    name: selfsigned-issuer
    kind: ClusterIssuer
  commonName: example.com
  dnsNames:
    - example.com
    - www.example.com
```

### Let's Encrypt Issuer (for public certificates)

```yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

## Configuration

### Resource Limits

The deployment is configured with appropriate resource limits:
- **Controller**: 500m CPU, 512Mi memory
- **Webhook**: 500m CPU, 256Mi memory  
- **CA Injector**: 500m CPU, 512Mi memory

### High Availability

- 2 replicas for each component
- Pod disruption budgets enabled
- Anti-affinity rules for better distribution

### Security

- Runs as non-root user (1000)
- Security contexts configured
- RBAC with minimal required permissions

## Monitoring

cert-manager exposes Prometheus metrics on port 9402:

```yaml
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
  endpoints:
  - port: tcp-prometheus-servicemonitor
    interval: 60s
    path: /metrics
```

## Troubleshooting

### Check Certificate Status

```bash
# List certificates
kubectl get certificates -A

# Describe certificate
kubectl describe certificate <cert-name> -n <namespace>

# Check certificate events
kubectl get events -n <namespace> --field-selector involvedObject.name=<cert-name>
```

### Check Issuer Status

```bash
# List issuers
kubectl get issuers,clusterissuers -A

# Describe issuer
kubectl describe clusterissuer <issuer-name>
```

### Common Issues

1. **Certificate not ready**: Check issuer configuration and ACME challenges
2. **Webhook failures**: Verify webhook pod is running and accessible
3. **RBAC issues**: Check service account permissions

### Logs

```bash
# Controller logs
kubectl logs -n cert-manager -l app.kubernetes.io/component=controller

# Webhook logs  
kubectl logs -n cert-manager -l app.kubernetes.io/component=webhook

# CA Injector logs
kubectl logs -n cert-manager -l app.kubernetes.io/component=cainjector
```

## Integration with Other Tools

cert-manager is used by:
- **Woodpecker Toleration Webhook**: For TLS certificates
- **Ingress Controllers**: For automatic TLS termination
- **Service Meshes**: For mTLS certificates
- **Custom Applications**: For application-specific certificates

## Upgrade

To upgrade cert-manager:

1. Update the version in `base/kustomization.yaml`
2. Review breaking changes in release notes
3. Apply the updated configuration
4. Verify all certificates are still valid

## Links

- [cert-manager Documentation](https://cert-manager.io/docs/)
- [Helm Chart Repository](https://charts.jetstack.io)
- [GitHub Repository](https://github.com/cert-manager/cert-manager)
