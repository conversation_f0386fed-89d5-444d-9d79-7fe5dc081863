monitoringPasscodeSecretName: "sonarqube-secrets"
monitoringPasscodeSecretKey: "monitoringPassword"

community:
  enabled: true

postgresql:
  enabled: false

setAdminPassword:
  passwordSecretName: "sonarqube-secrets"

jdbcOverwrite:
  enabled: true
  jdbcUrl: "********************************************************************"
  jdbcUsername: "sonar"
  jdbcSecretName: "sonarqube-secrets"
  jdbcSecretPasswordKey: "postgresPassword"

  # JVM options for tools cluster
  # jvmOpts: "-Xmx4096m -Xms2048m"

  # # SonarQube properties
  # sonarProperties: |
  #   sonar.forceAuthentication=true
  #   sonar.security.realm=sonar
  #   sonar.web.host=0.0.0.0
  #   sonar.web.port=9000
  #   sonar.web.context=/

plugins:
  install:
    - "https://github.com/mc1arke/sonarqube-community-branch-plugin/releases/download/1.19.0/sonarqube-community-branch-plugin-1.19.0.jar"

resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 1000m
    memory: 2Gi

persistence:
  enabled: true
  storageClass: ebs-sc
  size: 5Gi
  accessMode: ReadWriteOnce
# initContainers:
#   - name: install-plugins
#     image: curlimages/curl:8.5.0
#     command:
#       - sh
#       - -c
#       - |
#         mkdir -p /opt/sonarqube/extensions/plugins
#         curl -L -o /opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin.jar \
#           https://github.com/mc1arke/sonarqube-community-branch-plugin/releases/download/1.19.0/sonarqube-community-branch-plugin-1.19.0.jar
#     volumeMounts:
#       - name: sonarqube-extensions
#         mountPath: /opt/sonarqube/extensions
