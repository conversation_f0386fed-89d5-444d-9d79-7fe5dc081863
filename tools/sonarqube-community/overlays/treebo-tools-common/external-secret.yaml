apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: sonarqube-secrets
  namespace: sonarqube
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: sonarqube-secrets
    creationPolicy: Owner
  data:
    - secretKey: password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/sonarqube
        property: newPassword
    - secretKey: currentPassword
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/sonarqube
        property: oldPassword
    - secretKey: monitoringPassword
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/sonarqube
        property: monitoringPassword
    - secretKey: postgresPassword
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/sonarqube
        property: postgresPassword
