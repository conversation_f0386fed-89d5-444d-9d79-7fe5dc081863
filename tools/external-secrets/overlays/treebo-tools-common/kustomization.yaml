resources:
  - ../../base

namespace: external-secrets

patches:
  - target:
      kind: ClusterSecretStore
      name: aws-secrets-store
    patch: |-
      - op: replace
        path: /spec/provider/aws/region
        value: "ap-south-1"
  - target:
      kind: ClusterSecretStore
      name: aws-parameters-store
    patch: |-
      - op: replace
        path: /spec/provider/aws/region
        value: "ap-south-1"
  - target:
      kind: ServiceAccount
      name: external-secrets-sa
    patch: |-
      - op: replace
        path: /metadata/annotations/eks.amazonaws.com~1role-arn
        value: "arn:aws:iam::************:role/role-external-secrets-treebo-tools-common"
