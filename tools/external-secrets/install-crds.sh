
#!/bin/bash

# Install External Secrets CRDs separately
# This is useful when you want to install CRDs before the main Helm chart
# or if you're experiencing issues with CRD installation via Helm

# Set the version of External Secrets to use
ESO_VERSION="v0.17.0"

# Get directory listing from GitHub API and get CRD files download url
echo "Fetching External Secrets CRDs from GitHub..."
FILES=$(curl -s https://api.github.com/repos/external-secrets/external-secrets/contents/config/crds/bases?ref=${ESO_VERSION} | jq -r '.[] | select(.type == "file" and (.name | endswith(".yaml"))) | .download_url')

# Apply each YAML file
for url in $FILES; do
  echo "Applying $url"
  kubectl apply --server-side -f "$url"
done

# Wait for CRDs to be established
echo "Waiting for External Secrets CRDs to be established..."
kubectl wait --for condition=Established --all CustomResourceDefinition --timeout=60s

echo "External Secrets CRDs installation complete!"
