apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: uptime-kuma

helmCharts:
  - name: uptime-kuma
    repo: https://dirsigler.github.io/uptime-kuma-helm
    version: 2.21.0
    releaseName: uptime-kuma
    namespace: uptime-kuma
    valuesFile: values.yaml

resources:
  - ingress.yaml
  - external-secret.yaml

# Uptime Kuma is an application workload - semi-critical for monitoring
components:
  - ../../../lib/common/components/application-semi-critical
