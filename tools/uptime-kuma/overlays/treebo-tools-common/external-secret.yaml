apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: uptime-kuma-external-secret
  namespace: uptime-kuma
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: uptime-kuma-secrets
    creationPolicy: Owner
  data:
    - secretKey: database-url
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/uptime-kuma-secrets
        property: databaseUrl
    - secretKey: postgres-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/uptime-kuma-secrets
        property: postgresPassword
    - secretKey: user-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/uptime-kuma-secrets
        property: userPassword
