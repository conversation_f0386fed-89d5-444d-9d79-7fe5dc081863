# Uptime Kuma Configuration for treebo-tools-common
# Self-hosted monitoring tool for uptime monitoring

# Uptime Kuma image configuration
image:
  repository: louislam/uptime-kuma
  tag: 1.23.13
  pullPolicy: IfNotPresent

# Replica count
replicaCount: 1

# Resources
resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

# PostgreSQL database configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "" # Will be set via external secret
    username: uptime_kuma
    password: "" # Will be set via external secret
    database: uptime_kuma
  primary:
    persistence:
      enabled: true
      storageClass: ebs-sc
      size: 10Gi
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

# Database connection via external secret
env:
  - name: UPTIME_KUMA_PORT
    value: "3001"
  - name: UPTIME_KUMA_HOST
    value: "0.0.0.0"
  - name: DATABASE_URL
    valueFrom:
      secretKeyRef:
        name: uptime-kuma-secrets
        key: database-url

# Service configuration
service:
  type: ClusterIP
  port: 3001

# Ingress configuration
ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: uptime.treebo.tools
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - uptime.treebo.tools
      secretName: uptime-kuma-tls

# Persistence for data
persistence:
  enabled: true
  storageClass: ebs-sc
  size: 5Gi
  accessMode: ReadWriteOnce

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 1000

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 1000

# Health checks
livenessProbe:
  httpGet:
    path: /
    port: 3001
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10

readinessProbe:
  httpGet:
    path: /
    port: 3001
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5

# Service Account
serviceAccount:
  create: true
  name: uptime-kuma-sa
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::ACCOUNT_ID:role/treebo-tools-common-uptime-kuma-role"

# Node affinity and tolerations for tools-common
nodeSelector:
  karpenter.sh/nodepool: tools-common

tolerations:
  - key: "tools-common"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1
