# Uptime Kuma

Uptime Kuma is a self-hosted monitoring tool that provides uptime monitoring for websites, APIs, and services.

## Features

- **Website Monitoring**: Monitor HTTP/HTTPS websites and APIs
- **Service Monitoring**: Monitor TCP, UDP, DNS, and other services
- **Notifications**: Multiple notification channels (email, Slack, Discord, etc.)
- **Status Pages**: Create public status pages for your services
- **Multi-language Support**: Available in multiple languages
- **Docker Support**: Easy deployment with Docker

## Architecture

This deployment uses the official Uptime Kuma Helm chart and includes:

- **Uptime Kuma Application**: Main monitoring application (louislam/uptime-kuma:1.23.13)
- **PostgreSQL Database**: For storing monitoring data and configuration
- **Persistent Storage**: For data persistence across restarts
- **Ingress**: HTTPS access via nginx ingress controller
- **Helm Chart**: Official Uptime Kuma Helm chart from dirsigler.github.io

## Configuration

### Database Integration

Uptime Kuma is configured to use PostgreSQL as the backend database:

- Database: `uptime_kuma`
- User: `uptime_kuma`
- Connection managed via External Secrets

### Storage

- **Application Data**: 5Gi EBS volume for Uptime Kuma data
- **Database Storage**: 10Gi EBS volume for PostgreSQL data

### Security

- Service Account with IRSA for AWS integration
- External Secrets for sensitive configuration
- Non-root security context

## Deployment

### Prerequisites

1. External Secrets Operator installed
2. AWS Parameter Store configured with secrets
3. Nginx Ingress Controller
4. Cert-manager for TLS certificates

### Install

```bash
# Deploy to treebo-tools-common cluster (requires --enable-helm flag)
kubectl apply --enable-helm -k tools/uptime-kuma/overlays/treebo-tools-common/
```

### Access

- **Web Interface**: https://uptime.treebo.tools
- **Default Port**: 3001

## AWS Integration

### IAM Permissions

The service account has permissions for:

- **S3 Access**: For backup storage
- **SES Access**: For email notifications

### Required Secrets

Store these in AWS Parameter Store under `/treebo/production/eks/cluster-treebo-tools-common/uptime-kuma-secrets`:

```json
{
  "databaseUrl": "*************************************************************/uptime_kuma",
  "postgresPassword": "secure-postgres-password",
  "userPassword": "secure-user-password"
}
```

## Monitoring

Uptime Kuma provides its own monitoring capabilities:

- **Health Checks**: Built-in liveness and readiness probes
- **Metrics**: Application-level monitoring metrics
- **Alerting**: Configurable alerts for service outages

## Maintenance

### Backup

- Database backups handled by PostgreSQL configuration
- Application data persisted in EBS volumes
- Optional S3 backup integration via IAM role

### Updates

Update the image tag in `overlays/treebo-tools-common/values.yaml` and redeploy:

```bash
kubectl apply --enable-helm -k tools/uptime-kuma/overlays/treebo-tools-common/
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Check External Secrets and PostgreSQL pod status
2. **Storage Issues**: Verify EBS CSI driver and storage class
3. **Ingress Issues**: Check nginx ingress controller and cert-manager

### Logs

```bash
# Application logs
kubectl logs -n uptime-kuma deployment/uptime-kuma

# Database logs
kubectl logs -n uptime-kuma deployment/uptime-kuma-postgresql
```
