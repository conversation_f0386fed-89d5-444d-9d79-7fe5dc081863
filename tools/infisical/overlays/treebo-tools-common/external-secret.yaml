apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: infisical-secrets
  namespace: infisical
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: infisical-secrets
    creationPolicy: Owner
  data:
    - secretKey: AUTH_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/infisical-secrets
        property: authSecret
    - secretKey: ENCRYPTION_KEY
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/infisical-secrets
        property: encryptionKey
    - secretKey: REDIS_URL
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/infisical-secrets
        property: redisUrl
    - secretKey: DB_CONNECTION_URI
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/infisical-secrets
        property: dbConnectionUri
