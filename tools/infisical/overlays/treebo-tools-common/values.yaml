backend:
  enabled: true
  replicaCount: 1
  image:
    repository: infisical/infisical
    tag: "v0.130.1-postgres"
    pullPolicy: IfNotPresent

  env:
    SITE_URL: "https://infisical.ekscraving1775.treebo.com"

  kubeSecretRef: "infisical-secrets"

  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 200m
      memory: 256Mi

# Backend environment variables (will be populated via external secret)
backendEnvironmentVariables:
  SITE_URL: "https://infisical.ekscraving1775.treebo.com"
  INVITE_ONLY_SIGNUP: false
  REDIS_URL: "redis://redis-master.redis.svc.cluster.local:6379"

ingress:
  enabled: false

redis:
  enabled: false

mongodb:
  enabled: false
