apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: infisical-ingress
  namespace: infisical
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  ingressClassName: nginx
  rules:
    - host: infisical.ekscraving1775.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: infisical-backend
                port:
                  number: 8080
