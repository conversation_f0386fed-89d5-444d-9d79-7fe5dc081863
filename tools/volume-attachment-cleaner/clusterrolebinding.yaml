apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: volume-attachment-cleaner
  labels:
    app: volume-attachment-cleaner
    app.kubernetes.io/name: volume-attachment-cleaner
    app.kubernetes.io/component: cleaner
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: volume-attachment-cleaner
subjects:
- kind: ServiceAccount
  name: volume-attachment-cleaner
  namespace: kube-system
