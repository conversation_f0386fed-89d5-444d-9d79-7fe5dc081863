apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: volume-attachment-cleaner
  labels:
    app: volume-attachment-cleaner
    app.kubernetes.io/name: volume-attachment-cleaner
    app.kubernetes.io/component: cleaner
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "delete"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["storage.k8s.io"]
    resources: ["volumeattachments"]
    verbs: ["get", "list", "watch", "patch", "update", "delete"]
