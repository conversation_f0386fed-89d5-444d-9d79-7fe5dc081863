apiVersion: apps/v1
kind: Deployment
metadata:
  name: volume-attachment-cleaner
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: volume-attachment-cleaner
  template:
    metadata:
      labels:
        app: volume-attachment-cleaner
    spec:
      serviceAccountName: volume-attachment-cleaner
      containers:
        - name: cleaner
          image: bitnami/kubectl:latest
          command:
            - /bin/bash
            - -c
            - |
              while true; do
                echo "Checking for stuck volume attachments..."
                # Find pods with deletionTimestamp set for more than 10 minutes (any phase)
                STUCK_PODS=$(kubectl get pods --all-namespaces -o json | jq -r '.items[] | select(.metadata.deletionTimestamp != null) | select((now - (.metadata.deletionTimestamp | fromdateiso8601)) > 600) | "\(.metadata.namespace)/\(.metadata.name)/\(.status.phase // "Unknown")"')
                echo "Found stuck pods: $STUCK_PODS"

                for POD_INFO in $STUCK_PODS; do
                  POD_NAMESPACE=$(echo $POD_INFO | cut -d'/' -f1)
                  POD=$(echo $POD_INFO | cut -d'/' -f2)
                  POD_PHASE=$(echo $POD_INFO | cut -d'/' -f3)
                  echo "Processing stuck pod: $POD in namespace $POD_NAMESPACE (phase: $POD_PHASE)"
                  # Find PVCs used by this pod
                  PVCS=$(kubectl get pod $POD -n $POD_NAMESPACE -o json | jq -r '.spec.volumes[]? | select(.persistentVolumeClaim) | .persistentVolumeClaim.claimName')
                  POD_DELETED=false

                  if [ -z "$PVCS" ]; then
                    echo "No PVCs found for pod $POD, force deleting pod directly"
                    kubectl delete pod $POD -n $POD_NAMESPACE --force --grace-period=0
                    POD_DELETED=true
                  else
                    for PVC in $PVCS; do
                      echo "Found PVC: $PVC"
                      # Get the PV name for this PVC
                      PV=$(kubectl get pvc $PVC -n $POD_NAMESPACE -o jsonpath='{.spec.volumeName}')
                      if [ ! -z "$PV" ]; then
                        echo "PVC $PVC is bound to PV: $PV"
                        # Find volume attachments for this PV
                        VAS=$(kubectl get volumeattachment -o json | jq -r '.items[] | select(.spec.source.persistentVolumeName == "'$PV'") | .metadata.name')
                        if [ ! -z "$VAS" ]; then
                          if [ "$POD_DELETED" = false ]; then
                            echo "Force deleting stuck pod: $POD in namespace $POD_NAMESPACE"
                            kubectl delete pod $POD -n $POD_NAMESPACE --force --grace-period=0
                            POD_DELETED=true

                            # Wait a moment for the pod deletion to process
                            sleep 5
                          fi

                          for VA in $VAS; do
                            echo "Deleting volume attachment: $VA (for PV: $PV)"
                            kubectl delete volumeattachment $VA
                          done
                        fi
                      fi
                    done

                    # If we found PVCs but no volume attachments, still delete the pod
                    if [ "$POD_DELETED" = false ]; then
                      echo "Found PVCs but no volume attachments for pod $POD, force deleting pod"
                      kubectl delete pod $POD -n $POD_NAMESPACE --force --grace-period=0
                    fi
                  fi
                done
                echo "Sleeping for 5 minutes..."
                sleep 300
              done
