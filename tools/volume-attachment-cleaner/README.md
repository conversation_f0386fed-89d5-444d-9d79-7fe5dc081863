# Volume Attachment Cleaner

A utility service that automatically cleans up stuck volume attachments in Kubernetes clusters. This service helps resolve issues where pods get stuck in terminating state due to volume attachment problems.

## Problem Statement

In Kubernetes clusters, pods can sometimes get stuck in a terminating state when:
- EBS volumes fail to detach properly
- Node failures leave volume attachments in inconsistent state
- AWS API rate limiting causes volume operations to timeout
- Network issues prevent proper volume cleanup

This results in:
- Pods stuck with `deletionTimestamp` set but never actually terminating
- Volume attachments remaining on nodes that no longer exist
- New pods unable to start because volumes are "already attached"
- Manual intervention required to clean up stuck resources

## Solution

This service automatically:
1. **Detects stuck pods** - Finds pods with `deletionTimestamp` set for >10 minutes (any phase)
2. **Force deletes pods** - Removes stuck pods to trigger volume detachment
3. **Cleans volume attachments** - Removes orphaned volume attachments from AWS
4. **Monitors continuously** - Runs every 5 minutes to catch new issues

## How It Works

### Detection Logic
```bash
# Find pods with deletionTimestamp set for more than 10 minutes (any phase)
kubectl get pods --all-namespaces -o json | jq -r '
  .items[] | 
  select(.metadata.deletionTimestamp != null) | 
  select((now - (.metadata.deletionTimestamp | fromdateiso8601)) > 600) | 
  "\(.metadata.namespace)/\(.metadata.name)/\(.status.phase // "Unknown")"
'
```

### Pod Phases Handled
- **Pending**: Pod stuck trying to schedule/start
- **Running**: Pod stuck in termination process  
- **Failed**: Pod failed but can't be cleaned up
- **Unknown**: Communication issues with pod
- **Succeeded**: Completed pods that won't terminate

### Cleanup Process
1. **Force delete stuck pods**: `kubectl delete pod --force --grace-period=0`
2. **Find associated PVCs**: Extract PVC names from pod spec
3. **Get PV details**: Find underlying EBS volume IDs
4. **Clean volume attachments**: Remove AWS volume attachments by PVC ID matching

## Configuration

### Environment Variables
- **Cleanup interval**: 5 minutes (hardcoded)
- **Stuck threshold**: 10 minutes (600 seconds)
- **Grace period**: 0 seconds for force deletion

### Permissions Required
```yaml
# Cluster-wide permissions needed
- pods: get, list, delete
- persistentvolumeclaims: get, list  
- persistentvolumes: get, list
- volumeattachments: get, list, delete
```

### AWS Permissions
The service account needs IAM permissions for:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow", 
      "Action": [
        "ec2:DescribeVolumes",
        "ec2:DetachVolume"
      ],
      "Resource": "*"
    }
  ]
}
```

## Deployment

### Prerequisites
1. **Metrics Server**: Required for pod resource monitoring
2. **IRSA**: IAM role for service account with EC2 permissions
3. **Cluster Admin**: Service account needs cluster-wide access

### Install
```bash
# Deploy to cluster
kubectl apply -f tools/volume-attachment-cleaner/
```

### Verify
```bash
# Check service status
kubectl get deployment volume-attachment-cleaner -n kube-system

# View logs
kubectl logs -n kube-system deployment/volume-attachment-cleaner -f

# Check for stuck pods manually
kubectl get pods --all-namespaces | grep Terminating
```

## Monitoring

### Logs
The service provides detailed logging:
```
Checking for stuck volume attachments...
Found stuck pods: namespace/pod-name/Running
Processing stuck pod: pod-name in namespace namespace (phase: Running)
Found PVCs: pvc-name
Found PV: pv-name with volume ID: vol-1234567890abcdef0
Cleaning up volume attachment for volume vol-1234567890abcdef0
Successfully cleaned volume attachment
Pod deleted successfully
```

### Metrics
Monitor these indicators:
- Pod termination times
- Volume attachment cleanup frequency  
- Failed cleanup attempts
- AWS API errors

## Safety Features

### Conservative Approach
- **10-minute threshold**: Only acts on pods stuck for >10 minutes
- **Force delete only**: Doesn't modify volume attachments directly
- **PVC-based matching**: Uses PVC IDs to find correct volume attachments
- **Error handling**: Continues processing other pods if one fails

### Rollback
If issues occur:
1. **Scale down**: `kubectl scale deployment volume-attachment-cleaner --replicas=0`
2. **Manual cleanup**: Use AWS console to fix volume attachments
3. **Restart pods**: Delete and recreate affected workloads

## Troubleshooting

### Common Issues

1. **No stuck pods detected**
   - Check if pods actually have `deletionTimestamp` set
   - Verify 10-minute threshold has passed
   - Review pod phases - all phases are now handled

2. **Volume attachments not cleaned**
   - Check AWS IAM permissions for EC2 operations
   - Verify volume IDs match between K8s and AWS
   - Check AWS API rate limiting

3. **Service account permissions**
   - Ensure cluster-wide RBAC permissions
   - Verify IRSA annotation on service account
   - Check AWS role trust policy

### Useful Commands
```bash
# Check stuck pods manually
kubectl get pods --all-namespaces -o json | jq '.items[] | select(.metadata.deletionTimestamp != null)'

# View volume attachments
kubectl get volumeattachments

# Check PVC to PV mapping
kubectl get pvc -A -o wide

# AWS volume status
aws ec2 describe-volumes --volume-ids vol-1234567890abcdef0
```

## Improvements Made

### Version 2.0 Changes
- ✅ **Removed phase filtering**: Now handles all pod phases (Pending, Running, Failed, Unknown, Succeeded)
- ✅ **Enhanced logging**: Shows pod phase in log output for better debugging
- ✅ **Broader detection**: Catches more stuck pod scenarios
- ✅ **Better monitoring**: More comprehensive pod state visibility

### Why Remove Phase Filtering?
The original logic only looked for `Running` pods, but pods can get stuck in any phase:
- **Pending + deletionTimestamp**: Stuck trying to delete during scheduling
- **Failed + deletionTimestamp**: Failed pods that can't be cleaned up
- **Unknown + deletionTimestamp**: Communication issues preventing cleanup

By removing the phase filter, we catch all problematic pods regardless of their current state.
