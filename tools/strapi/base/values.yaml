# Strapi Helm Chart configuration
image:
  registry: docker.io
  repository: bitnami/strapi
  tag: 4.25.12-debian-12-r0
  pullPolicy: IfNotPresent

# Strapi configuration
strapi:
  # Admin user configuration
  adminUser: admin
  adminPassword: ""  # Will be set via external secret
  adminEmail: <EMAIL>

  # Application configuration
  host: "0.0.0.0"
  port: 1337

  # Environment
  env: production

  # Skip first run wizard
  skipFirstRunWizard: true

# Service configuration
service:
  type: ClusterIP
  ports:
    http: 1337

# Ingress configuration
ingress:
  enabled: false  # Will be enabled in overlays

# Resources
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 512Mi

# Persistence
persistence:
  enabled: true
  storageClass: ebs-sc
  size: 10Gi
  accessModes:
    - ReadWriteOnce

# PostgreSQL configuration (as dependency)
postgresql:
  enabled: true
  auth:
    postgresPassword: ""  # Will be set via external secret
    username: strapi
    password: ""  # Will be set via external secret
    database: strapi
  primary:
    persistence:
      enabled: true
      storageClass: ebs-sc
      size: 20Gi
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 250m
        memory: 256Mi

# Security context
podSecurityContext:
  enabled: true
  fsGroup: 1001

containerSecurityContext:
  enabled: true
  runAsUser: 1001
  runAsNonRoot: true

# Service account
serviceAccount:
  create: true
  name: strapi

# Autoscaling
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Health checks
livenessProbe:
  enabled: true
  httpGet:
    path: /_health
    port: http
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  enabled: true
  httpGet:
    path: /_health
    port: http
  initialDelaySeconds: 30
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
