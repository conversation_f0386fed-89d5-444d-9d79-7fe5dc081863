apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: strapi-ingress
  namespace: strapi
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  rules:
  - host: strapi.ekscraving1775.treebo.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: strapi
            port:
              number: 1337
