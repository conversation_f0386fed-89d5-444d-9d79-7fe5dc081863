resources:
  - ../../base
  - ingress.yaml
  - external-secret.yaml

# Strapi is an application workload - prefers cost-effective spot nodes
components:
  - ../../../lib/common/components/application-non-critical

patches:
  - target:
      kind: Deployment
      name: strapi
    patch: |-
      - op: add
        path: /spec/template/spec/containers/0/env
        value:
          - name: NODE_ENV
            value: "production"
          - name: STRAPI_LOG_LEVEL
            value: "info"
