apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: strapi-secrets
  namespace: strapi
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-store
    kind: ClusterSecretStore
  target:
    name: strapi-secrets
    creationPolicy: Owner
  data:
    - secretKey: STRAPI_ADMIN_PASSWORD
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: adminPassword
    - secretKey: DATABASE_PASSWORD
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: databasePassword
    - secretKey: DATABASE_POSTGRES_PASSWORD
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: postgresPassword
    - secretKey: JWT_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: jwtSecret
    - secretKey: API_TOKEN_SALT
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: apiTokenSalt
    - secretKey: ADMIN_JWT_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: adminJwtSecret
    - secretKey: TRANSFER_TOKEN_SALT
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/strapi
        property: transferTokenSalt
