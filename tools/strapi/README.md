# Strapi

This tool deploys <PERSON>rapi, an open-source headless CMS built with Node.js.

## Overview

Strapi is a leading open-source headless CMS that gives developers the freedom to choose their favorite tools and frameworks while allowing editors to manage their content with ease.

## Prerequisites

- Kubernetes cluster
- Ingress controller (nginx)
- External Secrets Operator (for credentials management)
- Persistent storage (EBS)

## Setup

1. Create the required secrets in AWS Parameter Store:
   ```bash
   # Admin credentials
   aws ssm put-parameter --name "/treebo/strapi/admin-password" --value "your-secure-admin-password" --type "SecureString"
   
   # Database credentials
   aws ssm put-parameter --name "/treebo/strapi/database-password" --value "your-db-password" --type "SecureString"
   aws ssm put-parameter --name "/treebo/strapi/postgres-password" --value "your-postgres-password" --type "SecureString"
   
   # JWT and API secrets
   aws ssm put-parameter --name "/treebo/strapi/jwt-secret" --value "$(openssl rand -base64 32)" --type "SecureString"
   aws ssm put-parameter --name "/treebo/strapi/api-token-salt" --value "$(openssl rand -base64 32)" --type "SecureString"
   aws ssm put-parameter --name "/treebo/strapi/admin-jwt-secret" --value "$(openssl rand -base64 32)" --type "SecureString"
   aws ssm put-parameter --name "/treebo/strapi/transfer-token-salt" --value "$(openssl rand -base64 32)" --type "SecureString"
   ```

2. Deploy using ArgoCD or kubectl:
   ```bash
   kubectl apply -k tools/strapi/overlays/treebo-tools-common/
   ```

## Configuration

### Base Configuration
- **Namespace**: strapi
- **Helm Chart**: strapi from charts.bitnami.com/bitnami
- **Version**: 2.1.8
- **Image**: bitnami/strapi:4.25.12-debian-12-r0

### Database
- **PostgreSQL**: Deployed as dependency
- **Storage**: 20Gi EBS volume
- **Database Name**: strapi
- **Username**: strapi

### Storage
- **Application Data**: 10Gi EBS volume
- **Database Data**: 20Gi EBS volume

## Access

The application is accessible via:
- **URL**: https://strapi.ekscraving1775.treebo.com
- **Admin Panel**: https://strapi.ekscraving1775.treebo.com/admin
- **API**: https://strapi.ekscraving1775.treebo.com/api

### Default Credentials
- **Admin Email**: <EMAIL>
- **Admin Password**: Set via external secret

## Features

- **Content Management**: Rich content editor with media library
- **API Generation**: Automatic REST and GraphQL API generation
- **User Management**: Built-in user authentication and permissions
- **Plugin System**: Extensible with plugins
- **Multi-database**: Support for PostgreSQL, MySQL, SQLite
- **Internationalization**: Multi-language content support
- **Role-based Access**: Granular permissions system

## API Usage

### REST API

```bash
# Get all articles
curl https://strapi.ekscraving1775.treebo.com/api/articles

# Get specific article
curl https://strapi.ekscraving1775.treebo.com/api/articles/1

# Create article (requires authentication)
curl -X POST https://strapi.ekscraving1775.treebo.com/api/articles \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -d '{"data": {"title": "My Article", "content": "Article content"}}'
```

### GraphQL API

```bash
# GraphQL endpoint
curl -X POST https://strapi.ekscraving1775.treebo.com/graphql \
  -H 'Content-Type: application/json' \
  -d '{"query": "{ articles { data { id attributes { title content } } } }"}'
```

## Content Types

### Creating Content Types

1. Access the admin panel
2. Go to Content-Types Builder
3. Create new collection or single types
4. Define fields (text, rich text, media, relations, etc.)
5. Save and restart if needed

### Example Content Type

```json
{
  "kind": "collectionType",
  "collectionName": "articles",
  "info": {
    "singularName": "article",
    "pluralName": "articles",
    "displayName": "Article"
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "content": {
      "type": "richtext"
    },
    "publishedAt": {
      "type": "datetime"
    }
  }
}
```

## Authentication

### API Tokens

1. Go to Settings > API Tokens
2. Create new token with appropriate permissions
3. Use token in API requests:
   ```bash
   curl -H 'Authorization: Bearer YOUR_API_TOKEN' \
     https://strapi.ekscraving1775.treebo.com/api/articles
   ```

### JWT Authentication

```bash
# Login to get JWT token
curl -X POST https://strapi.ekscraving1775.treebo.com/api/auth/local \
  -H 'Content-Type: application/json' \
  -d '{"identifier": "<EMAIL>", "password": "password"}'

# Use JWT token
curl -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  https://strapi.ekscraving1775.treebo.com/api/users/me
```

## Monitoring

### Health Checks
- **Liveness Probe**: HTTP GET on `/_health`
- **Readiness Probe**: HTTP GET on `/_health`

### Logs

```bash
# Application logs
kubectl logs -n strapi deployment/strapi

# Database logs
kubectl logs -n strapi deployment/strapi-postgresql
```

### Metrics

Strapi doesn't expose Prometheus metrics by default, but you can:
1. Use custom middleware to expose metrics
2. Monitor via ingress controller metrics
3. Use APM tools like New Relic or DataDog

## Backup and Restore

### Database Backup

```bash
# Backup PostgreSQL database
kubectl exec -n strapi deployment/strapi-postgresql -- pg_dump -U strapi strapi > strapi-backup.sql
```

### Media Files Backup

```bash
# Backup uploaded files
kubectl exec -n strapi deployment/strapi -- tar czf - /opt/bitnami/strapi/public/uploads > strapi-media-backup.tar.gz
```

### Restore

```bash
# Restore database
kubectl exec -i -n strapi deployment/strapi-postgresql -- psql -U strapi strapi < strapi-backup.sql

# Restore media files
kubectl exec -i -n strapi deployment/strapi -- tar xzf - -C /opt/bitnami/strapi/public/uploads < strapi-media-backup.tar.gz
```

## Security

- **HTTPS**: TLS termination at ingress
- **Secrets**: Credentials stored in AWS Parameter Store
- **Database**: PostgreSQL with authentication
- **RBAC**: Role-based access control
- **CORS**: Configurable CORS settings
- **Rate Limiting**: Built-in rate limiting

## Troubleshooting

### Common Issues

1. **Database connection**: Check PostgreSQL pod status and credentials
2. **File uploads**: Verify persistent volume is mounted correctly
3. **Performance**: Increase resource limits if needed
4. **Plugin issues**: Check plugin compatibility with Strapi version

### Debug Commands

```bash
# Check pod status
kubectl get pods -n strapi

# Check logs
kubectl logs -n strapi deployment/strapi
kubectl logs -n strapi deployment/strapi-postgresql

# Check external secrets
kubectl get externalsecret -n strapi

# Check PVCs
kubectl get pvc -n strapi

# Test database connection
kubectl exec -n strapi deployment/strapi-postgresql -- psql -U strapi -d strapi -c "SELECT version();"
```
