apiVersion: clickhouse.altinity.com/v1
kind: ClickHouseInstallation
metadata:
  name: clickhouse-cluster-installation
  namespace: clickhouse
spec:
  configuration:
    users:
      admin/password: "admin123"
      admin/networks/ip: "::/0"
      admin/profile: "default"
      admin/quota: "default"
      admin/access_management: 1
    # profiles:
    #   default/max_memory_usage: 10000000000
    #   default/use_uncompressed_cache: 0
    #   default/load_balancing: random
    # quotas:
    #   default/interval/duration: 3600
    #   default/interval/queries: 0
    #   default/interval/errors: 0
    #   default/interval/result_rows: 0
    #   default/interval/read_rows: 0
    #   default/interval/execution_time: 0
    # settings:
    #   compression/case/method: zstd
    #   disable_internal_dns_cache: 1
    #   # ClickHouse Keeper configuration
    #   keeper_server/tcp_port: 9181
    #   keeper_server/server_id: 1
    #   keeper_server/log_storage_path: /var/lib/clickhouse/coordination/log
    #   keeper_server/snapshot_storage_path: /var/lib/clickhouse/coordination/snapshots
    # files:
    #   docker_related_config.xml: |
    #     <clickhouse>
    #         <logger>
    #             <console>1</console>
    #         </logger>
    #         <keeper_server>
    #             <tcp_port>9181</tcp_port>
    #             <server_id>1</server_id>
    #             <log_storage_path>/var/lib/clickhouse/coordination/log</log_storage_path>
    #             <snapshot_storage_path>/var/lib/clickhouse/coordination/snapshots</snapshot_storage_path>
    #             <coordination_settings>
    #                 <operation_timeout_ms>10000</operation_timeout_ms>
    #                 <session_timeout_ms>30000</session_timeout_ms>
    #                 <raft_logs_level>warning</raft_logs_level>
    #             </coordination_settings>
    #             <raft_configuration>
    #                 <server>
    #                     <id>1</id>
    #                     <hostname>treebo-clickhouse-cluster-0-0</hostname>
    #                     <port>9234</port>
    #                 </server>
    #                 <server>
    #                     <id>2</id>
    #                     <hostname>treebo-clickhouse-cluster-0-1</hostname>
    #                     <port>9234</port>
    #                 </server>
    #                 <server>
    #                     <id>3</id>
    #                     <hostname>treebo-clickhouse-cluster-1-0</hostname>
    #                     <port>9234</port>
    #                 </server>
    #             </raft_configuration>
    #         </keeper_server>
    #     </clickhouse>
    clusters:
      - name: "ch-cluster"
        layout:
          shardsCount: 2
          replicasCount: 2
  defaults:
    templates:
      podTemplate: clickhouse-pod-template
      dataVolumeClaimTemplate: data-volume-template
      logVolumeClaimTemplate: log-volume-template
  templates:
    podTemplates:
      - name: clickhouse-pod-template
        spec:
          containers:
            - name: clickhouse
              image: clickhouse/clickhouse-server:**********-alpine
              resources:
                requests:
                  memory: "2Gi"
                  cpu: "1000m"
                limits:
                  memory: "4Gi"
                  cpu: "2000m"
              ports:
                - name: http
                  containerPort: 8123
                - name: client
                  containerPort: 9000
                - name: interserver
                  containerPort: 9009
    volumeClaimTemplates:
      - name: data-volume-template
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 2Gi
          storageClassName: ebs-sc
      - name: log-volume-template
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 1Gi
          storageClassName: ebs-sc
