- op: add
  path: /spec/templates/podTemplates/0/spec/affinity
  value:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          preference:
            matchExpressions:
              - key: workload-type
                operator: In
                values:
                  - application-critical
- op: add
  path: /spec/templates/podTemplates/0/spec/tolerations
  value:
    - key: workload-type
      operator: Equal
      value: "application-critical"
      effect: NoSchedule
