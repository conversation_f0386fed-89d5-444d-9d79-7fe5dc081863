apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

helmCharts:
  - name: altinity-clickhouse-operator
    repo: https://docs.altinity.com/clickhouse-operator/
    version: 0.25.0
    releaseName: clickhouse
    namespace: kube-system
    valuesFile: values.yaml
    includeCRDs: true

resources:
  - namespace.yaml
  - clickhouse-cluster.yaml
  # - clickhouse-patches.yaml

patches:
  - target:
      kind: ClickHouseInstallation
      name: clickhouse-cluster-installation
    path: clickhouse-patches.yaml

# This applies to the operator itself
components:
  - ../../../lib/common/components/application-critical
