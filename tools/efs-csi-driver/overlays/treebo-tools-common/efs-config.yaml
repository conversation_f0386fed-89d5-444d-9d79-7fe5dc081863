# EFS Configuration for treebo-tools-common cluster
# Maps user-friendly aliases to actual EFS file system IDs
apiVersion: v1
kind: ConfigMap
metadata:
  name: efs-config
  namespace: kube-system
  labels:
    app.kubernetes.io/name: efs-csi-driver
    app.kubernetes.io/component: config
    treebo.com/cluster: treebo-tools-common
data:
  # User-friendly EFS aliases mapped to actual file system IDs
  # These are updated automatically by the setup script
  shared: "fs-03c81605c6978fadf"
  high-performance: fs-0496037e455dc779e

  # Default alias for applications that don't specify one
  default-alias: "shared"
