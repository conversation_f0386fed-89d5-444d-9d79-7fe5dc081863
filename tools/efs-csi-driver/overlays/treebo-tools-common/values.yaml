replicaCount: 2

controller:
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 128Mi
  serviceAccount:
    create: true
    name: efs-csi-controller-sa
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/role-efs-csi-driver-controller-treebo-tools-common

node:
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 128Mi
  serviceAccount:
    create: true
    name: efs-csi-node-sa
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/role-efs-csi-driver-node-treebo-tools-common

storageClasses:
  - name: efs-sc
    annotations:
      storageclass.kubernetes.io/is-default-class: "false"
      # treebo.com/storage-type: "efs"
      # treebo.com/access-mode: "ReadWriteMany"
    mountOptions:
      - _netdev
      - stunnel=false
    parameters:
      provisioningMode: efs-ap
      fileSystemId: fs-03c81605c6978fadf
      directoryPerms: "0755"
      uid: "1000"
      gid: "1000"
      basePath: "/treebo-services"
    reclaimPolicy: Delete
    volumeBindingMode: Immediate
    # allowVolumeExpansion: true # not supported by chart
  - name: efs-performance-sc
    annotations:
      storageclass.kubernetes.io/is-default-class: "false"
      # treebo.com/storage-type: "efs-performance"
      # treebo.com/access-mode: "ReadWriteMany"
    mountOptions:
      # - tls
      - _netdev
      - fsc # Enable local caching for better performance
      - stunnel=false
    parameters:
      provisioningMode: efs-ap
      fileSystemId: fs-0496037e455dc779e
      directoryPerms: "0755"
      uid: "1000"
      gid: "1000"
      basePath: "/treebo-services-performance"
    reclaimPolicy: Delete
    volumeBindingMode: Immediate
    # allowVolumeExpansion: true # not supported by chart
