replicaCount: 2

controller:
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 128Mi
  serviceAccount:
    create: true
    name: efs-csi-controller-sa
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/role-efs-csi-driver-controller-treebo-staging

node:
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 128Mi
  serviceAccount:
    create: true
    name: efs-csi-node-sa
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/role-efs-csi-driver-node-treebo-staging

storageClasses:
  - name: efs-sc
    annotations:
      storageclass.kubernetes.io/is-default-class: "false"
      # treebo.com/storage-type: "efs"
      # treebo.com/access-mode: "ReadWriteMany"
    mountOptions:
      # - tls
      - _netdev
      - stunnel=false
    parameters:
      provisioningMode: efs-ap
      fileSystemId: fs-06c87262bd457623c
      directoryPerms: "0755"
      gidRangeStart: "1000"
      gidRangeEnd: "2000"
      basePath: "/treebo-services"
    reclaimPolicy: Delete
    volumeBindingMode: Immediate
    # AllowVolumeExpansion: true # not supported by chart
  - name: efs-performance-sc
    annotations:
      storageclass.kubernetes.io/is-default-class: "false"
      # treebo.com/storage-type: "efs-performance"
      # treebo.com/access-mode: "ReadWriteMany"
    mountOptions:
      # - tls
      - _netdev
      - fsc # Enable local caching for better performance
      - stunnel=false
    parameters:
      provisioningMode: efs-ap
      fileSystemId: fs-0fb649dd083067399
      directoryPerms: "0755"
      gidRangeStart: "1000"
      gidRangeEnd: "2000"
      basePath: "/treebo-services-performance"
    reclaimPolicy: Delete
    volumeBindingMode: Immediate
    # AllowVolumeExpansion: true # not supported by chart
