# EFS Configuration for treebo-staging cluster
# Maps user-friendly aliases to actual EFS file system IDs
apiVersion: v1
kind: ConfigMap
metadata:
  name: efs-config
  namespace: kube-system
  labels:
    app.kubernetes.io/name: efs-csi-driver
    app.kubernetes.io/component: config
    treebo.com/cluster: treebo-staging
  annotations:
    argocd.argoproj.io/sync-wave: "0"
data:
  # User-friendly EFS aliases mapped to actual file system IDs
  # These are updated automatically by the setup-efs-file-systems.ts script
  shared: "fs-06c87262bd457623c"
  high-performance: "fs-0fb649dd083067399"

  # Default alias used when no specific alias is provided
  default: "shared"
