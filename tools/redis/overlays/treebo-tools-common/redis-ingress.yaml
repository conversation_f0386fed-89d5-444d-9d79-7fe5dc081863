apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: redis-ingress
  namespace: redis
  annotations:
    nginx.ingress.kubernetes.io/tcp-services-configmap: "redis/redis-tcp"
    nginx.ingress.kubernetes.io/backend-protocol: "TCP"
spec:
  ingressClassName: nginx
  rules:
    - host: redis.ekscraving1775.treebo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: redis-master
                port:
                  number: 6379
