auth:
  existingSecret: "redis-secrets"
  existingSecretPasswordKey: "redis-password"

architecture: standalone

master:
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 512Mi

  persistence:
    storageClass: ebs-sc
    size: 2Gi

replica:
  enabled: false

metrics:
  enabled: true
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
