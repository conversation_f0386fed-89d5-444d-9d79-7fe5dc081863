apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: redis-external-secret
  namespace: redis
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: redis-secrets
    creationPolicy: Owner
  data:
    - secretKey: redis-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/redis-secrets
        property: redisPassword
