datasources:
  datasources.yaml:
    apiVersion: 1
    datasources:
      # NOTE: We have mimir, so we don't need prometheus
      # - name: Prometheus
      #   type: prometheus
      #   url: http://prometheus-kube-prometheus-prometheus.monitoring.svc.cluster.local:9090
      #   access: proxy
      - name: Loki
        type: loki
        url: http://loki-gateway.monitoring.svc.cluster.local
        access: proxy
        isDefault: true
        jsonData:
          maxLines: 1000
      - name: Tempo
        type: tempo
        url: http://tempo-query-frontend.monitoring.svc.cluster.local:3200
        access: proxy
      - name: Prometheus
        type: prometheus
        url: http://mimir-gateway.monitoring.svc.cluster.local/prometheus
        access: proxy
      - name: Pyroscope
        type: grafana-pyroscope-datasource
        url: http://pyroscope.monitoring.svc.cluster.local:4040
        access: proxy

admin:
  existingSecret: "grafana-secrets"
  userKey: admin-username
  passwordKey: admin-password

sidecar:
  dashboards:
    enabled: true
    label: grafana_dashboard
    folder: /var/lib/grafana/dashboards/default

grafana.ini:
  auth:
    login_cookie_name: grafana_session_treebo
