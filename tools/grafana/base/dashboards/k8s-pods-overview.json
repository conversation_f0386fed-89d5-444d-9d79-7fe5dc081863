{"uid": "k8s-pods-overview", "title": "Kubernetes Pods Overview", "tags": ["kubernetes", "pods"], "templating": {"list": [{"name": "pod", "type": "query", "datasource": "Prometheus", "query": "label_values(pod)", "includeAll": true, "allValue": ".*"}]}, "panels": [{"title": "Pod CPU Usage vs Requests/Limits", "type": "timeseries", "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{pod=~'$pod'}[5m])) by (pod)", "legendFormat": "CPU Usage"}, {"expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\",pod=~'$pod'}) by (pod)", "legendFormat": "CPU Requests"}, {"expr": "sum(kube_pod_container_resource_limits{resource=\"cpu\",pod=~'$pod'}) by (pod)", "legendFormat": "CPU Limits"}]}, {"title": "Pod Memory Usage vs Requests/Limits", "type": "timeseries", "gridPos": {"x": 12, "y": 0, "w": 12, "h": 8}, "targets": [{"expr": "sum(container_memory_usage_bytes{pod=~'$pod'}) by (pod)", "legendFormat": "Memory Usage"}, {"expr": "sum(kube_pod_container_resource_requests{resource=\"memory\",pod=~'$pod'}) by (pod)", "legendFormat": "Memory Requests"}, {"expr": "sum(kube_pod_container_resource_limits{resource=\"memory\",pod=~'$pod'}) by (pod)", "legendFormat": "Memory Limits"}]}, {"title": "Pod Network (In/Out)", "type": "timeseries", "gridPos": {"x": 0, "y": 8, "w": 12, "h": 8}, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{pod=~'$pod'}[5m])) by (pod)", "legendFormat": "Receive"}, {"expr": "sum(rate(container_network_transmit_bytes_total{pod=~'$pod'}[5m])) by (pod)", "legendFormat": "Transmit"}]}, {"title": "Pod Disk Usage", "type": "timeseries", "gridPos": {"x": 12, "y": 8, "w": 12, "h": 8}, "targets": [{"expr": "sum(container_fs_usage_bytes{pod=~'$pod'}) by (pod)", "legendFormat": "Disk Usage"}]}]}