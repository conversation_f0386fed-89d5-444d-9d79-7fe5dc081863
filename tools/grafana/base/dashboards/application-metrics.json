{"uid": "application-metrics", "title": "Application Metrics Dashboard", "tags": ["application", "metrics"], "templating": {"list": [{"name": "app", "type": "query", "datasource": "Prometheus", "query": "label_values(application)"}, {"name": "path", "type": "query", "datasource": "Prometheus", "query": "label_values(path)", "includeAll": true, "allValue": ".*"}]}, "panels": [{"title": "Latency (avg, p50, p75, p90, p95, p99, max) by API Path", "type": "timeseries", "gridPos": {"x": 0, "y": 0, "w": 12, "h": 10}, "targets": [{"expr": "sum(rate(http_request_duration_seconds_sum{application=~'$app',path=~'$path'}[5m])) by (path) / sum(rate(http_request_duration_seconds_count{application=~'$app',path=~'$path'}[5m])) by (path)", "legendFormat": "avg $path"}, {"expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{application=~'$app',path=~'$path'}[5m])) by (le, path))", "legendFormat": "p50 $path"}, {"expr": "histogram_quantile(0.75, sum(rate(http_request_duration_seconds_bucket{application=~'$app',path=~'$path'}[5m])) by (le, path))", "legendFormat": "p75 $path"}, {"expr": "histogram_quantile(0.90, sum(rate(http_request_duration_seconds_bucket{application=~'$app',path=~'$path'}[5m])) by (le, path))", "legendFormat": "p90 $path"}, {"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{application=~'$app',path=~'$path'}[5m])) by (le, path))", "legendFormat": "p95 $path"}, {"expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{application=~'$app',path=~'$path'}[5m])) by (le, path))", "legendFormat": "p99 $path"}, {"expr": "max(rate(http_request_duration_seconds_sum{application=~'$app',path=~'$path'}[5m]) / rate(http_request_duration_seconds_count{application=~'$app',path=~'$path'}[5m])) by (path)", "legendFormat": "max $path"}]}, {"title": "Throughput (Overall and by API Path)", "type": "timeseries", "gridPos": {"x": 12, "y": 0, "w": 12, "h": 10}, "targets": [{"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path'}[5m]))", "legendFormat": "Overall"}, {"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path'}[5m])) by (path)", "legendFormat": "Throughput $path"}]}, {"title": "HTTP Status Codes (Overall and by API Path)", "type": "timeseries", "gridPos": {"x": 0, "y": 10, "w": 12, "h": 10}, "targets": [{"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'2..'}[5m]))", "legendFormat": "2xx Overall"}, {"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'4..'}[5m]))", "legendFormat": "4xx Overall"}, {"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'5..'}[5m]))", "legendFormat": "5xx Overall"}, {"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'2..'}[5m])) by (path)", "legendFormat": "2xx $path"}, {"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'4..'}[5m])) by (path)", "legendFormat": "4xx $path"}, {"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'5..'}[5m])) by (path)", "legendFormat": "5xx $path"}]}, {"title": "Application CPU Usage", "type": "timeseries", "gridPos": {"x": 0, "y": 20, "w": 12, "h": 8}, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{application=~'$app'}[5m])) by (application)", "legendFormat": "CPU Usage"}]}, {"title": "Application Memory Usage", "type": "timeseries", "gridPos": {"x": 12, "y": 20, "w": 12, "h": 8}, "targets": [{"expr": "sum(container_memory_usage_bytes{application=~'$app'}) by (application)", "legendFormat": "Memory Usage"}]}, {"title": "Errors (5xx Error Rate)", "type": "stat", "gridPos": {"x": 12, "y": 10, "w": 12, "h": 10}, "datasource": "Prometheus", "targets": [{"expr": "sum(rate(http_requests_total{application=~'$app',path=~'$path',status=~'5..'}[5m])) / sum(rate(http_requests_total{application=~'$app',path=~'$path'}[5m]))", "legendFormat": "5xx Error Rate"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2}}}, {"title": "Recent Spans (Tempo)", "type": "table", "gridPos": {"x": 0, "y": 28, "w": 24, "h": 8}, "datasource": "Tempo", "targets": [{"expr": "{service=~'$app'}", "queryType": "traces", "refId": "A"}], "columns": [{"text": "Trace ID", "value": "traceID"}, {"text": "Span Name", "value": "name"}, {"text": "Duration", "value": "duration"}, {"text": "Start Time", "value": "startTime"}]}]}