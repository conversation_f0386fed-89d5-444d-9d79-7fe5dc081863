{"uid": "k8s-cluster-overview", "title": "Kubernetes Cluster Overview", "tags": ["kubernetes", "cluster"], "templating": {"list": [{"name": "cluster", "type": "query", "datasource": "Prometheus", "query": "label_values(cluster)", "includeAll": true, "allValue": ".*"}]}, "panels": [{"title": "Cluster CPU Requested vs Limits", "type": "timeseries", "gridPos": {"x": 0, "y": 0, "w": 12, "h": 12}, "targets": [{"expr": "sum(kube_resourcequota{resource=\"requests.cpu\",cluster=~'$cluster'})", "legendFormat": "CPU Requests"}, {"expr": "sum(kube_resourcequota{resource=\"limits.cpu\",cluster=~'$cluster'})", "legendFormat": "CPU Limits"}]}, {"title": "Cluster Memory Requested vs Limits", "type": "timeseries", "gridPos": {"x": 12, "y": 0, "w": 12, "h": 12}, "targets": [{"expr": "sum(kube_resourcequota{resource=\"requests.memory\",cluster=~'$cluster'})", "legendFormat": "Memory Requests"}, {"expr": "sum(kube_resourcequota{resource=\"limits.memory\",cluster=~'$cluster'})", "legendFormat": "Memory Limits"}]}, {"title": "Cluster Network (In/Out)", "type": "timeseries", "gridPos": {"x": 0, "y": 12, "w": 12, "h": 12}, "targets": [{"expr": "sum(rate(node_network_receive_bytes_total[5m]))", "legendFormat": "Receive"}, {"expr": "sum(rate(node_network_transmit_bytes_total[5m]))", "legendFormat": "Transmit"}]}, {"title": "Cluster Disk Usage", "type": "timeseries", "gridPos": {"x": 12, "y": 12, "w": 12, "h": 12}, "targets": [{"expr": "sum(node_filesystem_size_bytes - node_filesystem_free_bytes)", "legendFormat": "Used"}, {"expr": "sum(node_filesystem_size_bytes)", "legendFormat": "Total"}]}, {"title": "HTTP Status Codes (ALB/NGINX)", "type": "timeseries", "targets": [{"expr": "sum(rate(alb_http_response_status_group_count{status_group=\"2xx\"}[5m]))", "legendFormat": "ALB 2xx"}, {"expr": "sum(rate(alb_http_response_status_group_count{status_group=\"4xx\"}[5m]))", "legendFormat": "ALB 4xx"}, {"expr": "sum(rate(alb_http_response_status_group_count{status_group=\"5xx\"}[5m]))", "legendFormat": "ALB 5xx"}, {"expr": "sum(rate(nginx_http_requests_total{status=~\"2..\"}[5m]))", "legendFormat": "NGINX 2xx"}, {"expr": "sum(rate(nginx_http_requests_total{status=~\"4..\"}[5m]))", "legendFormat": "NGINX 4xx"}, {"expr": "sum(rate(nginx_http_requests_total{status=~\"5..\"}[5m]))", "legendFormat": "NGINX 5xx"}]}]}