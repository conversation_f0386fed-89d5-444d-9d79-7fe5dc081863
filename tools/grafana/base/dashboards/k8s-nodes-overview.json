{"uid": "k8s-nodes-overview", "title": "Kubernetes Nodes Overview", "tags": ["kubernetes", "nodes"], "templating": {"list": [{"name": "node", "type": "query", "datasource": "Prometheus", "query": "label_values(node)", "includeAll": true, "allValue": ".*"}]}, "panels": [{"title": "Node CPU Usage (p95, p99, min, max)", "type": "timeseries", "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}, "targets": [{"expr": "quantile_over_time(0.95, sum(rate(node_cpu_seconds_total{mode!='idle',instance=~'$node'}[5m])) by (instance)[30m:5m])", "legendFormat": "p95"}, {"expr": "quantile_over_time(0.99, sum(rate(node_cpu_seconds_total{mode!='idle',instance=~'$node'}[5m])) by (instance)[30m:5m])", "legendFormat": "p99"}, {"expr": "min_over_time(sum(rate(node_cpu_seconds_total{mode!='idle',instance=~'$node'}[5m])) by (instance)[30m:5m])", "legendFormat": "min"}, {"expr": "max_over_time(sum(rate(node_cpu_seconds_total{mode!='idle',instance=~'$node'}[5m])) by (instance)[30m:5m])", "legendFormat": "max"}]}, {"title": "Node Memory Usage (p95, p99, min, max)", "type": "timeseries", "gridPos": {"x": 12, "y": 0, "w": 12, "h": 8}, "targets": [{"expr": "quantile_over_time(0.95, node_memory_MemTotal_bytes{instance=~'$node'} - node_memory_MemAvailable_bytes{instance=~'$node'}[30m:5m])", "legendFormat": "p95"}, {"expr": "quantile_over_time(0.99, node_memory_MemTotal_bytes{instance=~'$node'} - node_memory_MemAvailable_bytes{instance=~'$node'}[30m:5m])", "legendFormat": "p99"}, {"expr": "min_over_time(node_memory_MemTotal_bytes{instance=~'$node'} - node_memory_MemAvailable_bytes{instance=~'$node'}[30m:5m])", "legendFormat": "min"}, {"expr": "max_over_time(node_memory_MemTotal_bytes{instance=~'$node'} - node_memory_MemAvailable_bytes{instance=~'$node'}[30m:5m])", "legendFormat": "max"}]}, {"title": "Node Network (In/Out)", "type": "timeseries", "gridPos": {"x": 0, "y": 8, "w": 12, "h": 8}, "targets": [{"expr": "sum(rate(node_network_receive_bytes_total{instance=~'$node'}[5m]))", "legendFormat": "Receive"}, {"expr": "sum(rate(node_network_transmit_bytes_total{instance=~'$node'}[5m]))", "legendFormat": "Transmit"}]}, {"title": "Node Disk (Read/Write)", "type": "timeseries", "gridPos": {"x": 12, "y": 8, "w": 12, "h": 8}, "targets": [{"expr": "sum(rate(node_disk_read_bytes_total{instance=~'$node'}[5m]))", "legendFormat": "Read"}, {"expr": "sum(rate(node_disk_written_bytes_total{instance=~'$node'}[5m]))", "legendFormat": "Write"}]}]}