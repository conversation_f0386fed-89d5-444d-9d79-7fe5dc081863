{"uid": "logs-loki-overview", "title": "Loki Logs Overview", "tags": ["logs", "loki", "observability"], "style": "dark", "time": {"from": "now-1h", "to": "now"}, "templating": {"list": [{"name": "app", "type": "query", "datasource": "<PERSON>", "query": "label_values(app)"}, {"name": "level", "type": "custom", "options": [{"value": "", "text": "All"}, {"value": "info", "text": "info"}, {"value": "warn", "text": "warn"}, {"value": "error", "text": "error"}, {"value": "debug", "text": "debug"}]}]}, "panels": [{"title": "Log Volume (Last Hour)", "type": "stat", "gridPos": {"x": 0, "y": 0, "w": 6, "h": 4}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({app=~'$app'}[1h])", "legendFormat": "Logs"}]}, {"title": "Error Count (Last Hour)", "type": "stat", "gridPos": {"x": 6, "y": 0, "w": 6, "h": 4}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({app=~'$app', level='error'}[1h])", "legendFormat": "Errors"}]}, {"title": "Warning Count (Last Hour)", "type": "stat", "gridPos": {"x": 12, "y": 0, "w": 6, "h": 4}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({app=~'$app', level='warn'}[1h])", "legendFormat": "Warnings"}]}, {"title": "Top 5 Log Sources", "type": "table", "gridPos": {"x": 18, "y": 0, "w": 6, "h": 4}, "datasource": "<PERSON>", "targets": [{"expr": "topk(5, count_over_time({app=~'$app'}[1h]) by (host))", "legendFormat": "Host $host"}], "columns": [{"text": "Host", "value": "host"}, {"text": "Log Count", "value": "value"}]}, {"title": "Log Level Distribution", "type": "piechart", "gridPos": {"x": 0, "y": 4, "w": 8, "h": 6}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({app=~'$app'}[1h]) by (level)", "legendFormat": "$level"}]}, {"title": "Log Volume Over Time", "type": "timeseries", "gridPos": {"x": 8, "y": 4, "w": 16, "h": 6}, "datasource": "<PERSON>", "targets": [{"expr": "sum by (app) (count_over_time({app=~'$app'}[5m]))", "legendFormat": "$app"}]}, {"title": "Recent Logs", "type": "logs", "gridPos": {"x": 0, "y": 10, "w": 24, "h": 10}, "datasource": "<PERSON>", "targets": [{"expr": "{app=~'$app', level=~'$level'} |~ \".*\" | line_format \"{{.time}} {{.level}} {{.msg}}\"", "legendFormat": "Logs"}]}]}