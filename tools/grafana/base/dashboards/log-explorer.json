{"uid": "log-explorer", "title": "Log Explorer", "tags": ["logs", "explorer", "loki", "debugging"], "style": "dark", "time": {"from": "now-1h", "to": "now"}, "templating": {"list": [{"name": "app", "type": "query", "datasource": "<PERSON>", "query": "label_values(app)"}, {"name": "level", "type": "custom", "options": [{"value": "", "text": "All"}, {"value": "info", "text": "info"}, {"value": "warn", "text": "warn"}, {"value": "error", "text": "error"}, {"value": "debug", "text": "debug"}]}, {"name": "host", "type": "query", "datasource": "<PERSON>", "query": "label_values(host)"}]}, "panels": [{"title": "Log Query Builder", "type": "text", "gridPos": {"x": 0, "y": 0, "w": 24, "h": 2}, "options": {"content": "<b>Tip:</b> Use filters above to narrow down logs. You can also use regex or full-text search in the log panel below.", "mode": "html"}}, {"title": "Log Volume by Level (Last Hour)", "type": "barGauge", "gridPos": {"x": 0, "y": 2, "w": 8, "h": 5}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({app=~'$app', host=~'$host'}[1h]) by (level)", "legendFormat": "$level"}]}, {"title": "Log Volume by Host (Last Hour)", "type": "barGauge", "gridPos": {"x": 8, "y": 2, "w": 8, "h": 5}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({app=~'$app', level=~'$level'}[1h]) by (host)", "legendFormat": "$host"}]}, {"title": "Log Volume by App (Last Hour)", "type": "barGauge", "gridPos": {"x": 16, "y": 2, "w": 8, "h": 5}, "datasource": "<PERSON>", "targets": [{"expr": "count_over_time({level=~'$level', host=~'$host'}[1h]) by (app)", "legendFormat": "$app"}]}, {"title": "Recent Logs Explorer", "type": "logs", "gridPos": {"x": 0, "y": 7, "w": 24, "h": 13}, "datasource": "<PERSON>", "targets": [{"expr": "{app=~'$app', level=~'$level', host=~'$host'} |~ \".*\" | line_format \"{{.time}} {{.level}} {{.host}} {{.msg}}\"", "legendFormat": "Logs"}]}]}