{"uid": "tempo-spans-window", "title": "Tempo Spans (Selected Window)", "tags": ["tempo", "tracing", "spans"], "templating": {"list": [{"name": "service", "type": "query", "datasource": "Tempo", "query": "label_values(service)"}]}, "time": {"from": "now-5m", "to": "now"}, "panels": [{"title": "Recent Spans (Last 5m)", "type": "table", "gridPos": {"x": 0, "y": 0, "w": 24, "h": 16}, "datasource": "Tempo", "targets": [{"expr": "{service=~'$service'}", "queryType": "traces", "refId": "A"}], "columns": [{"text": "Trace ID", "value": "traceID"}, {"text": "Span Name", "value": "name"}, {"text": "Duration", "value": "duration"}, {"text": "Start Time", "value": "startTime"}]}]}