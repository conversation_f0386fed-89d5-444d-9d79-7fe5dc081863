# Placeholder for grafana HelmRelease or manifests

helmCharts:
  - name: grafana
    repo: https://grafana.github.io/helm-charts
    version: 9.0.0
    releaseName: grafana
    namespace: monitoring
    valuesFile: values.yaml
resources:
  - namespace.yaml
  - ingress.yaml

configMapGenerator:
  - name: grafana-dashboards
    namespace: monitoring
    files:
      - dashboards/application-metrics.json
      - dashboards/k8s-cluster-overview.json
      - dashboards/k8s-nodes-overview.json
      - dashboards/k8s-pods-overview.json
      - dashboards/tempo-spans-window.json
      - dashboards/logs-loki-overview.json
      - dashboards/log-explorer.json
      # - dashboards/logs-dashboard.json
      # - dashboards/node-dashboard.json
      # - dashboards/k8s-service-overview.json
      # - dashboards/k8s-storage-overview.json
      # - dashboards/k8s-workload-overview.json
    options:
      labels:
        grafana_dashboard: "1"

components:
  - ../../lib/common/components/application-non-critical
