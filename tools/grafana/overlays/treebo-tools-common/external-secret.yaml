apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: grafana-external-secret
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: grafana-secrets
    creationPolicy: Owner
  data:
    - secretKey: admin-username
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/grafana-secrets
        property: adminUsername
    - secretKey: admin-password
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/grafana-secrets
        property: adminPassword

