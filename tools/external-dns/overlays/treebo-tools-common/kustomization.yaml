namespace: external-dns

helmCharts:
  - name: external-dns
    version: 1.16.1
    repo: https://kubernetes-sigs.github.io/external-dns
    valuesFile: values.yaml
    releaseName: external-dns

resources:
  - namespace.yaml

# External DNS manages Route53 records - critical for service discovery
components:
  - ../../../lib/common/components/system-critical

patches:
  - target:
      kind: Deployment
      name: external-dns
    patch: |-
      - op: add
        path: /spec/template/spec/containers/0/env
        value:
          - name: AWS_DEFAULT_REGION
            value: ap-south-1
