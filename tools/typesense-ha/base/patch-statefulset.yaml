- op: replace
  path: /spec/volumeClaimTemplates/0/spec/storageClassName
  value: ebs-sc
- op: replace
  path: /spec/volumeClaimTemplates/0/spec/resources/requests/storage
  value: 1Gi
- op: replace
  path: /spec/template/spec/containers/0/image
  value: typesense/typesense:28.0-amd64
- op: replace
  path: /spec/serviceName
  value: typesense-peering

# Remove password from args and add in env. 
# Removing 3 both times is intentional as index changes after first remove
- op: remove
  path: /spec/template/spec/containers/0/command/3
- op: remove
  path: /spec/template/spec/containers/0/command/3
- op: add
  path: /spec/template/spec/containers/0/env
  value:
    - name: TYPESENSE_API_KEY
      valueFrom:
        secretKeyRef:
          name: typesense-secrets
          key: TYPESENSE_API_KEY
    - name: TYPESENSE_ENABLE_CORS
      value: "false"

# Redefine resource limits
- op: replace
  path: /spec/template/spec/containers/0/resources
  value:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      cpu: 500m
      memory: 1Gi

# https://github.com/typesense/typesense/issues/465#issuecomment-1689460898
- op: add
  path: /spec/template/spec/containers/0/command/-
  value: --reset-peers-on-error
