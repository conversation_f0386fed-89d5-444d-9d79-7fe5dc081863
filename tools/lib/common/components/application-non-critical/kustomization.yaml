apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

patches:
  - target:
      kind: Deployment
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - application-non-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: workload-type
            operator: Equal
            value: "application-non-critical"
            effect: NoSchedule
  - target:
      kind: StatefulSet
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - application-non-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: workload-type
            operator: Equal
            value: "application-non-critical"
            effect: NoSchedule
  - target:
      kind: DaemonSet
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - application-non-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: workload-type
            operator: Equal
            value: "application-non-critical"
            effect: NoSchedule

  - target:
      kind: ReplicaSet
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - application-non-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: workload-type
            operator: Equal
            value: "application-non-critical"
            effect: NoSchedule
  - target:
      kind: Job
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - application-non-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: workload-type
            operator: Equal
            value: "application-non-critical"
            effect: NoSchedule
