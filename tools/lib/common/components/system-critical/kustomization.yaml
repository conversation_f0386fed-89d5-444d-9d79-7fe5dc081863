apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

# Critical infrastructure components (ArgoCD, NGINX, External DNS, etc.)
# must run on reliable on-demand system nodes with CriticalAddonsOnly tolerations
patches:
  - target:
      kind: Deployment
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - system-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: CriticalAddonsOnly
            operator: Equal
            value: "true"
            effect: NoSchedule
  - target:
      kind: StatefulSet
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - system-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: CriticalAddonsOnly
            operator: Equal
            value: "true"
            effect: NoSchedule
  - target:
      kind: DaemonSet
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - system-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: CriticalAddonsOnly
            operator: Equal
            value: "true"
            effect: NoSchedule
  - target:
      kind: ReplicaSet
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - system-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: CriticalAddonsOnly
            operator: Equal
            value: "true"
            effect: NoSchedule
  - target:
      kind: Job
    patch: |-
      - op: add
        path: /spec/template/spec/affinity
        value:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: workload-type
                      operator: In
                      values:
                        - system-critical
      - op: add
        path: /spec/template/spec/tolerations
        value:
          - key: CriticalAddonsOnly
            operator: Equal
            value: "true"
            effect: NoSchedule
