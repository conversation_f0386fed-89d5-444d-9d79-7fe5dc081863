{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:DescribeVolumes", "ec2:DescribeSnapshots", "ec2:CreateTags", "ec2:DescribeInstances", "ec2:DescribeInstanceAttribute", "ec2:DescribeInstanceStatus", "ec2:DescribeInstanceTypes", "ec2:DescribeRegions", "ec2:DescribeAvailabilityZones"], "Resource": "*"}, {"Effect": "Allow", "Action": ["ec2:CreateSnapshot", "ec2:DeleteSnapshot", "ec2:DescribeSnapshots", "ec2:DescribeSnapshotAttribute", "ec2:ModifySnapshotAttribute", "ec2:ResetSnapshotAttribute"], "Resource": "*"}, {"Effect": "Allow", "Action": ["s3:GetObject", "s3:DeleteObject", "s3:PutObject", "s3:AbortMultipartUpload", "s3:ListMultipartUploadParts"], "Resource": ["arn:aws:s3:::s3-velero-treebo-v1/*"]}, {"Effect": "Allow", "Action": ["s3:ListBucket", "s3:GetBucketVersioning", "s3:PutBucketVersioning", "s3:GetBucketLocation", "s3:ListBucketVersions", "s3:ListBucketMultipartUploads"], "Resource": ["arn:aws:s3:::s3-velero-treebo-v1"]}, {"Effect": "Allow", "Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey", "kms:GenerateDataKeyWithoutPlaintext", "kms:ReEncryptFrom", "kms:ReEncryptTo"], "Resource": "*", "Condition": {"StringEquals": {"kms:ViaService": ["s3.ap-south-1.amazonaws.com", "ec2.ap-south-1.amazonaws.com"]}}}]}