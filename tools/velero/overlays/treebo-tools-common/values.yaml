initContainers:
  - name: velero-plugin-for-aws
    image: velero/velero-plugin-for-aws:v1.12.1
    imagePullPolicy: IfNotPresent
    volumeMounts:
      - mountPath: /target
        name: plugins

deployNodeAgent: true

nodeAgent:
  priorityClassName: system-cluster-critical
  resources:
    requests:
      cpu: 250m
      memory: 250Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  tolerations:
    # Tolerate all taints regardless of key or value
    - operator: Exists
      effect: NoExecute
    - operator: Exists
      effect: NoSchedule

resources:
  requests:
    cpu: 500m
    memory: 128Mi
  limits:
    cpu: 1000m
    memory: 512Mi

schedules: {}

# upgradeCRDs: true

serviceAccount:
  server:
    create: true
    name: velero-sa
    annotations:
      eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/role-eks-s3-velero-treebo-v1"

# Credentials configuration - using IAM roles for service accounts
credentials:
  useSecret: false

# Configuration for backup storage location
configuration:
  backupStorageLocation:
    - name: default
      provider: aws
      bucket: s3-velero-treebo-v1
      prefix: treebo-tools-common
      config:
        region: ap-south-1
        s3ForcePathStyle: false
        serverSideEncryption: AES256
        insecureSkipTLSVerify: false

  volumeSnapshotLocation:
    - name: default
      provider: aws
      config:
        region: ap-south-1

metrics:
  enabled: true
  scrapeInterval: 30s
  scrapeTimeout: 10s
  service:
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "8085"
      prometheus.io/path: "/metrics"
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8085"
    prometheus.io/path: "/metrics"

tolerations:
  - operator: Equal
    effect: NoSchedule
    key: workload-type
    value: "application-critical"
