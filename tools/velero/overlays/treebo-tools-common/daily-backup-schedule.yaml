# Daily backups - Once per day, 30 days retention
# Complete backup including infrastructure
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-backup
  namespace: velero
  labels:
    backup-type: daily
spec:
  schedule: "0 2 * * *" # Daily at 2 AM
  template:
    ttl: 720h # 30 days retention
    includedNamespaces:
      - vaultwarden
      - postgresql
      - mariadb
      - infisical
      - unleash
      - metabase
      - flowise
      - strapi
      - drone
      - monitoring
      - karpenter
      - external-secrets
    excludedResources:
      - events
      - events.events.k8s.io
    storageLocation: default
    volumeSnapshotLocations:
      - default
    defaultVolumesToFsBackup: true
    metadata:
      labels:
        backup-type: daily
        schedule: daily-backup
