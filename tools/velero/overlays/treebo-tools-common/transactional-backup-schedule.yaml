# Transactional data backup - Every 15 minutes for critical data stores
# Focus on PVs and data consistency for postgres/mariadb/vaultwarden
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: transactional-backup
  namespace: velero
  labels:
    backup-type: transactional
spec:
  schedule: "*/15 * * * *" # Every 15 minutes
  template:
    ttl: 6h # 6 hours retention (short-term for transactional recovery)
    includedNamespaces:
      - vaultwarden
      - postgresql
      - mariadb
    includedResources:
      - persistentvolumes
      - persistentvolumeclaims
      - secrets
      - configmaps
      - statefulsets
      - deployments
    excludedResources:
      - events
      - events.events.k8s.io
      - pods
      - replicasets
      - services
      - ingresses
    storageLocation: default
    volumeSnapshotLocations:
      - default
    defaultVolumesToFsBackup: true
    metadata:
      labels:
        backup-type: transactional
        schedule: transactional-backup
