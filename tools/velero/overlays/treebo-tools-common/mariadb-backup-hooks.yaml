# MariaDB backup hook configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: mariadb-backup-hooks
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-hooks
data:
  pre-hook.sh: |
    #!/bin/bash
    # MariaDB pre-backup hook
    # This script ensures database consistency before backup

    echo "Starting MariaDB pre-backup hook..."

    # Flush tables and acquire read lock
    mysql -u root -p"${MARIADB_ROOT_PASSWORD}" -e "FLUSH TABLES WITH READ LOCK;" || true

    # Flush binary logs
    mysql -u root -p"${MARIADB_ROOT_PASSWORD}" -e "FLUSH LOGS;" || true

    echo "MariaDB pre-backup hook completed"

  post-hook.sh: |
    #!/bin/bash
    # MariaDB post-backup hook
    # This script releases locks after backup

    echo "Starting MariaDB post-backup hook..."

    # Release read lock
    mysql -u root -p"${MARIADB_ROOT_PASSWORD}" -e "UNLOCK TABLES;" || true

    echo "MariaDB post-backup hook completed"
