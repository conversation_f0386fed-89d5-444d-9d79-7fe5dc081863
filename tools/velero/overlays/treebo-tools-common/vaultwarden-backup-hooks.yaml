# Vaultwarden backup hook configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: vaultwarden-backup-hooks
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-hooks
data:
  pre-hook.sh: |
    #!/bin/bash
    # Vaultwarden pre-backup hook
    # This script ensures data consistency before backup

    echo "Starting Vaultwarden pre-backup hook..."

    # Signal Vaultwarden to flush any pending writes
    # Vaultwarden uses SQLite, so we'll sync the filesystem
    sync

    # Create a consistent point for backup
    echo "Vaultwarden pre-backup hook completed"

  post-hook.sh: |
    #!/bin/bash
    # Vaultwarden post-backup hook
    # This script runs after backup completion

    echo "Starting Vaultwarden post-backup hook..."

    # No specific cleanup needed for Vaultwarden
    echo "Vaultwarden post-backup hook completed"
