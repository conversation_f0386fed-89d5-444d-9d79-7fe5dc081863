# PostgreSQL backup hook configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-backup-hooks
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-hooks
data:
  pre-hook.sh: |
    #!/bin/bash
    # PostgreSQL pre-backup hook
    # This script ensures database consistency before backup

    echo "Starting PostgreSQL pre-backup hook..."

    # Flush any pending writes
    psql -U postgres -c "CHECKPOINT;"

    # Create a backup label (if supported)
    psql -U postgres -c "SELECT pg_start_backup('velero-backup', false, false);" || true

    echo "PostgreSQL pre-backup hook completed"

  post-hook.sh: |
    #!/bin/bash
    # PostgreSQL post-backup hook
    # This script cleans up after backup

    echo "Starting PostgreSQL post-backup hook..."

    # Stop backup mode (if started)
    psql -U postgres -c "SELECT pg_stop_backup();" || true

    echo "PostgreSQL post-backup hook completed"
