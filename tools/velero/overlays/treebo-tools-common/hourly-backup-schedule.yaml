# Hourly backups - Every hour, 24h retention
# Focus on all applications for regular recovery points
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: hourly-backup
  namespace: velero
  labels:
    backup-type: hourly
spec:
  schedule: "0 * * * *" # Every hour
  template:
    ttl: 24h # 24 hours retention
    includedNamespaces:
      - vaultwarden
      - postgresql
      - mariadb
      - infisical
      - unleash
      - metabase
      - flowise
      - strapi
      - drone
    excludedResources:
      - events
      - events.events.k8s.io
    storageLocation: default
    volumeSnapshotLocations:
      - default
    defaultVolumesToFsBackup: true
    metadata:
      labels:
        backup-type: hourly
        schedule: hourly-backup
