# Backup hook annotations for database pods
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-annotations
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-annotations
data:
  postgresql-annotations.yaml: |
    # Apply these annotations to PostgreSQL pods for backup hooks
    metadata:
      annotations:
        pre.hook.backup.velero.io/container: postgresql
        pre.hook.backup.velero.io/command: >-
          ["/bin/bash", "-c", "psql -U postgres -c \"CHECKPOINT;\" &&
          psql -U postgres -c \"SELECT pg_start_backup('velero-backup', false, false);\" || true"]
        post.hook.backup.velero.io/container: postgresql
        post.hook.backup.velero.io/command: >-
          ["/bin/bash", "-c", "psql -U postgres -c \"SELECT pg_stop_backup();\" || true"]
  mariadb-annotations.yaml: |
    # Apply these annotations to MariaDB pods for backup hooks
    metadata:
      annotations:
        pre.hook.backup.velero.io/container: mariadb
        pre.hook.backup.velero.io/command: >-
          ["/bin/bash", "-c", "mysql -u root -p\"${MARIADB_ROOT_PASSWORD}\"
          -e \"FLUSH TABLES WITH READ LOCK; FLUSH LOGS;\" || true"]
        post.hook.backup.velero.io/container: mariadb
        post.hook.backup.velero.io/command: >-
          ["/bin/bash", "-c", "mysql -u root -p\"${MARIADB_ROOT_PASSWORD}\"
          -e \"UNLOCK TABLES;\" || true"]
  vaultwarden-annotations.yaml: |
    # Apply these annotations to Vaultwarden pods for backup hooks
    metadata:
      annotations:
        pre.hook.backup.velero.io/container: vaultwarden
        pre.hook.backup.velero.io/command: '["/bin/bash", "-c", "sync"]'
        post.hook.backup.velero.io/container: vaultwarden
        post.hook.backup.velero.io/command: '["/bin/bash", "-c", "echo \"Backup completed\""]'
