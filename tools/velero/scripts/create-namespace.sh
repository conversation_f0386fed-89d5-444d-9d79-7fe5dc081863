#!/bin/bash

# Velero Namespace Creation Script
# This script creates the Velero namespace separately

set -euo pipefail

# Configuration
NAMESPACE="velero"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create namespace function

# Verify prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi

    # Check if we can connect to the cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check if namespace.yaml exists
    if [[ ! -f "tools/velero/base/namespace.yaml" ]]; then
        log_error "Namespace configuration file not found: tools/velero/base/namespace.yaml"
        exit 1
    fi

    log_info "Prerequisites check passed"
}

# Show usage
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -f, --force    Force creation even if namespace exists"
    echo ""
    echo "This script creates the Velero namespace separately from the main installation."
    echo "This is useful when you need to create the namespace before applying other resources."
}

# Main function
main() {
    local force_create=false

    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -f|--force)
                force_create=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    log_info "Starting Velero namespace creation..."

    check_prerequisites
    create_namespace

    log_info "Namespace creation completed!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Run the main Velero installation: ./tools/velero/install.sh"
    log_info "2. Or apply Velero configuration: kubectl apply -k tools/velero/overlays/treebo-tools-common/"
}

# Run main function
main "$@"
