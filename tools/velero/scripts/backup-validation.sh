#!/bin/bash

# Velero Backup Validation Script
# This script validates backup completion and integrity

set -euo pipefail

# Configuration
NAMESPACE="velero"
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Send notification to Slack (if configured)
send_slack_notification() {
    local message="$1"
    local color="$2"
    
    if [[ -n "${SLACK_WEBHOOK_URL}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"${color}\",\"text\":\"${message}\"}]}" \
            "${SLACK_WEBHOOK_URL}" || log_warn "Failed to send Slack notification"
    fi
}

# Check backup status
check_backup_status() {
    local backup_name="$1"
    
    log_info "Checking status of backup: ${backup_name}"
    
    # Get backup status
    local status=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
    
    case "${status}" in
        "Completed")
            log_info "✅ Backup ${backup_name} completed successfully"
            
            # Get backup details
            local start_time=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.startTimestamp}')
            local completion_time=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.completionTimestamp}')
            local total_items=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.totalItems}')
            local backup_size=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.progress.totalBytes}')
            
            log_info "  Start time: ${start_time}"
            log_info "  Completion time: ${completion_time}"
            log_info "  Total items: ${total_items}"
            log_info "  Backup size: ${backup_size} bytes"
            
            send_slack_notification "✅ Backup ${backup_name} completed successfully. Items: ${total_items}, Size: ${backup_size} bytes" "good"
            return 0
            ;;
        "Failed")
            log_error "❌ Backup ${backup_name} failed"
            
            # Get failure reason
            local failure_reason=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.failureReason}')
            log_error "  Failure reason: ${failure_reason}"
            
            send_slack_notification "❌ Backup ${backup_name} failed: ${failure_reason}" "danger"
            return 1
            ;;
        "InProgress")
            log_info "🔄 Backup ${backup_name} is in progress"
            return 2
            ;;
        "NotFound")
            log_error "❌ Backup ${backup_name} not found"
            return 1
            ;;
        *)
            log_warn "⚠️ Backup ${backup_name} has unknown status: ${status}"
            return 2
            ;;
    esac
}

# Validate recent backups
validate_recent_backups() {
    local hours="${1:-24}"
    
    log_info "Validating backups from the last ${hours} hours..."
    
    # Get backups from the last N hours
    local cutoff_time=$(date -u -d "${hours} hours ago" '+%Y-%m-%dT%H:%M:%SZ')
    
    # Get all backups and filter by time
    local recent_backups=$(kubectl get backups -n "${NAMESPACE}" -o json | \
        jq -r ".items[] | select(.status.startTimestamp > \"${cutoff_time}\") | .metadata.name")
    
    if [[ -z "${recent_backups}" ]]; then
        log_warn "No backups found in the last ${hours} hours"
        send_slack_notification "⚠️ No backups found in the last ${hours} hours" "warning"
        return 1
    fi
    
    local total_backups=0
    local successful_backups=0
    local failed_backups=0
    local in_progress_backups=0
    
    while IFS= read -r backup_name; do
        [[ -n "${backup_name}" ]] || continue
        
        ((total_backups++))
        
        check_backup_status "${backup_name}"
        local result=$?
        
        case $result in
            0) ((successful_backups++)) ;;
            1) ((failed_backups++)) ;;
            2) ((in_progress_backups++)) ;;
        esac
        
        echo ""
    done <<< "${recent_backups}"
    
    # Summary
    log_info "Backup validation summary (last ${hours} hours):"
    log_info "  Total backups: ${total_backups}"
    log_info "  Successful: ${successful_backups}"
    log_info "  Failed: ${failed_backups}"
    log_info "  In progress: ${in_progress_backups}"
    
    # Send summary notification
    local summary_message="Backup validation summary (${hours}h): ${successful_backups}/${total_backups} successful"
    if [[ ${failed_backups} -gt 0 ]]; then
        summary_message="${summary_message}, ${failed_backups} failed"
        send_slack_notification "⚠️ ${summary_message}" "warning"
    else
        send_slack_notification "✅ ${summary_message}" "good"
    fi
    
    return $([[ ${failed_backups} -eq 0 ]] && echo 0 || echo 1)
}

# Check backup schedules
check_backup_schedules() {
    log_info "Checking backup schedules..."
    
    local schedules=$(kubectl get schedules -n "${NAMESPACE}" -o json | jq -r '.items[].metadata.name')
    
    if [[ -z "${schedules}" ]]; then
        log_error "No backup schedules found"
        return 1
    fi
    
    while IFS= read -r schedule_name; do
        [[ -n "${schedule_name}" ]] || continue
        
        local last_backup=$(kubectl get schedule "${schedule_name}" -n "${NAMESPACE}" -o jsonpath='{.status.lastBackup}')
        local phase=$(kubectl get schedule "${schedule_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}')
        
        log_info "Schedule: ${schedule_name}"
        log_info "  Last backup: ${last_backup:-"None"}"
        log_info "  Phase: ${phase:-"Unknown"}"
        echo ""
    done <<< "${schedules}"
}

# Main function
main() {
    local hours="${1:-24}"
    
    log_info "Starting backup validation..."
    
    # Check if Velero is running
    if ! kubectl get deployment velero -n "${NAMESPACE}" &> /dev/null; then
        log_error "Velero deployment not found"
        exit 1
    fi
    
    # Check if Velero pods are running
    if ! kubectl get pods -n "${NAMESPACE}" -l app.kubernetes.io/name=velero | grep -q "Running"; then
        log_error "Velero pods are not running"
        kubectl get pods -n "${NAMESPACE}"
        exit 1
    fi
    
    check_backup_schedules
    validate_recent_backups "${hours}"
    
    log_info "Backup validation completed"
}

# Show usage
usage() {
    echo "Usage: $0 [hours]"
    echo "  hours: Number of hours to look back for backup validation (default: 24)"
    echo ""
    echo "Environment variables:"
    echo "  SLACK_WEBHOOK_URL: Slack webhook URL for notifications (optional)"
    echo ""
    echo "Examples:"
    echo "  $0        # Validate backups from last 24 hours"
    echo "  $0 12     # Validate backups from last 12 hours"
}

# Parse arguments
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    usage
    exit 0
fi

# Run main function
main "${1:-24}"
