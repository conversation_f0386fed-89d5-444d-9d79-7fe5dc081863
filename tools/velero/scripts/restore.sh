#!/bin/bash

# Velero Restore Script
# This script helps with restoring backups for specific applications

set -euo pipefail

# Configuration
NAMESPACE="velero"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# List available backups
list_backups() {
    local app_filter="${1:-}"
    
    log_info "Available backups:"
    
    if [[ -n "${app_filter}" ]]; then
        log_info "Filtering for application: ${app_filter}"
        kubectl get backups -n "${NAMESPACE}" -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,CREATED:.metadata.creationTimestamp,TTL:.spec.ttl" | \
            grep -E "(NAME|${app_filter})" || log_warn "No backups found for ${app_filter}"
    else
        kubectl get backups -n "${NAMESPACE}" -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,CREATED:.metadata.creationTimestamp,TTL:.spec.ttl"
    fi
}

# Get backup details
get_backup_details() {
    local backup_name="$1"
    
    log_info "Backup details for: ${backup_name}"
    
    if ! kubectl get backup "${backup_name}" -n "${NAMESPACE}" &> /dev/null; then
        log_error "Backup ${backup_name} not found"
        return 1
    fi
    
    # Get backup information
    local status=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}')
    local start_time=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.startTimestamp}')
    local completion_time=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.completionTimestamp}')
    local total_items=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.totalItems}')
    local included_namespaces=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.spec.includedNamespaces[*]}')
    local excluded_namespaces=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.spec.excludedNamespaces[*]}')
    
    echo "  Status: ${status}"
    echo "  Start time: ${start_time}"
    echo "  Completion time: ${completion_time}"
    echo "  Total items: ${total_items}"
    echo "  Included namespaces: ${included_namespaces:-"All"}"
    echo "  Excluded namespaces: ${excluded_namespaces:-"None"}"
}

# Restore from backup
restore_backup() {
    local backup_name="$1"
    local target_namespaces="${2:-}"
    local restore_name="${3:-}"
    
    # Generate restore name if not provided
    if [[ -z "${restore_name}" ]]; then
        restore_name="restore-${backup_name}-$(date +%Y%m%d-%H%M%S)"
    fi
    
    log_info "Creating restore: ${restore_name} from backup: ${backup_name}"
    
    # Check if backup exists and is completed
    local backup_status=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
    
    if [[ "${backup_status}" != "Completed" ]]; then
        log_error "Backup ${backup_name} is not in Completed state (current: ${backup_status})"
        return 1
    fi
    
    # Create restore manifest
    local restore_manifest="/tmp/restore-${restore_name}.yaml"
    
    cat > "${restore_manifest}" <<EOF
apiVersion: velero.io/v1
kind: Restore
metadata:
  name: ${restore_name}
  namespace: ${NAMESPACE}
spec:
  backupName: ${backup_name}
  restorePVs: true
EOF
    
    # Add namespace filter if specified
    if [[ -n "${target_namespaces}" ]]; then
        echo "  includedNamespaces:" >> "${restore_manifest}"
        IFS=',' read -ra NAMESPACES <<< "${target_namespaces}"
        for ns in "${NAMESPACES[@]}"; do
            echo "    - ${ns}" >> "${restore_manifest}"
        done
    fi
    
    # Apply the restore
    kubectl apply -f "${restore_manifest}"
    
    log_info "Restore ${restore_name} created successfully"
    log_info "Monitor progress with: kubectl get restore ${restore_name} -n ${NAMESPACE} -w"
    
    # Clean up temporary file
    rm -f "${restore_manifest}"
}

# Monitor restore progress
monitor_restore() {
    local restore_name="$1"
    local timeout="${2:-600}"  # 10 minutes default
    
    log_info "Monitoring restore: ${restore_name} (timeout: ${timeout}s)"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    while [[ $(date +%s) -lt ${end_time} ]]; do
        local status=$(kubectl get restore "${restore_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
        
        case "${status}" in
            "Completed")
                log_info "✅ Restore ${restore_name} completed successfully"
                
                # Get restore details
                local warnings=$(kubectl get restore "${restore_name}" -n "${NAMESPACE}" -o jsonpath='{.status.warnings}')
                local errors=$(kubectl get restore "${restore_name}" -n "${NAMESPACE}" -o jsonpath='{.status.errors}')
                
                if [[ -n "${warnings}" && "${warnings}" != "0" ]]; then
                    log_warn "Restore completed with ${warnings} warnings"
                fi
                
                if [[ -n "${errors}" && "${errors}" != "0" ]]; then
                    log_error "Restore completed with ${errors} errors"
                fi
                
                return 0
                ;;
            "Failed")
                log_error "❌ Restore ${restore_name} failed"
                kubectl describe restore "${restore_name}" -n "${NAMESPACE}"
                return 1
                ;;
            "InProgress")
                log_info "🔄 Restore ${restore_name} is in progress..."
                ;;
            "NotFound")
                log_error "❌ Restore ${restore_name} not found"
                return 1
                ;;
            *)
                log_debug "Restore ${restore_name} status: ${status}"
                ;;
        esac
        
        sleep 10
    done
    
    log_error "Timeout waiting for restore ${restore_name} to complete"
    return 1
}

# Application-specific restore functions
restore_vaultwarden() {
    local backup_name="$1"
    local restore_name="vaultwarden-restore-$(date +%Y%m%d-%H%M%S)"
    
    log_info "Restoring Vaultwarden from backup: ${backup_name}"
    
    # Stop Vaultwarden deployment to avoid conflicts
    log_info "Scaling down Vaultwarden deployment..."
    kubectl scale deployment vaultwarden -n vaultwarden --replicas=0 || log_warn "Failed to scale down Vaultwarden"
    
    # Restore
    restore_backup "${backup_name}" "vaultwarden" "${restore_name}"
    
    # Monitor restore
    if monitor_restore "${restore_name}"; then
        log_info "Vaultwarden restore completed successfully"
        log_info "You may need to manually scale up the deployment:"
        log_info "kubectl scale deployment vaultwarden -n vaultwarden --replicas=1"
    else
        log_error "Vaultwarden restore failed"
        return 1
    fi
}

restore_postgresql() {
    local backup_name="$1"
    local restore_name="postgresql-restore-$(date +%Y%m%d-%H%M%S)"
    
    log_info "Restoring PostgreSQL from backup: ${backup_name}"
    
    log_warn "WARNING: This will restore the entire PostgreSQL namespace"
    log_warn "Make sure to backup current data if needed"
    
    read -p "Continue with PostgreSQL restore? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "PostgreSQL restore cancelled"
        return 0
    fi
    
    # Restore
    restore_backup "${backup_name}" "postgresql" "${restore_name}"
    
    # Monitor restore
    monitor_restore "${restore_name}"
}

restore_mariadb() {
    local backup_name="$1"
    local restore_name="mariadb-restore-$(date +%Y%m%d-%H%M%S)"
    
    log_info "Restoring MariaDB from backup: ${backup_name}"
    
    log_warn "WARNING: This will restore the entire MariaDB namespace"
    log_warn "Make sure to backup current data if needed"
    
    read -p "Continue with MariaDB restore? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "MariaDB restore cancelled"
        return 0
    fi
    
    # Restore
    restore_backup "${backup_name}" "mariadb" "${restore_name}"
    
    # Monitor restore
    monitor_restore "${restore_name}"
}

# Show usage
usage() {
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  list [app]                    List available backups (optionally filter by app)"
    echo "  details <backup-name>         Show backup details"
    echo "  restore <backup-name> [ns]    Restore from backup (optionally specify namespaces)"
    echo "  monitor <restore-name>        Monitor restore progress"
    echo "  vaultwarden <backup-name>     Restore Vaultwarden specifically"
    echo "  postgresql <backup-name>      Restore PostgreSQL specifically"
    echo "  mariadb <backup-name>         Restore MariaDB specifically"
    echo ""
    echo "Examples:"
    echo "  $0 list                                    # List all backups"
    echo "  $0 list vaultwarden                        # List Vaultwarden backups"
    echo "  $0 details daily-backup-20240610-020000    # Show backup details"
    echo "  $0 restore daily-backup-20240610-020000    # Restore entire backup"
    echo "  $0 restore daily-backup-20240610-020000 vaultwarden,postgresql  # Restore specific namespaces"
    echo "  $0 vaultwarden daily-backup-20240610-020000  # Restore Vaultwarden"
}

# Main function
main() {
    local command="${1:-}"
    
    if [[ -z "${command}" ]]; then
        usage
        exit 1
    fi
    
    case "${command}" in
        "list")
            list_backups "${2:-}"
            ;;
        "details")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            get_backup_details "$2"
            ;;
        "restore")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            restore_backup "$2" "${3:-}"
            ;;
        "monitor")
            if [[ -z "${2:-}" ]]; then
                log_error "Restore name required"
                usage
                exit 1
            fi
            monitor_restore "$2" "${3:-600}"
            ;;
        "vaultwarden")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            restore_vaultwarden "$2"
            ;;
        "postgresql")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            restore_postgresql "$2"
            ;;
        "mariadb")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            restore_mariadb "$2"
            ;;
        "-h"|"--help")
            usage
            ;;
        *)
            log_error "Unknown command: ${command}"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
