#!/bin/bash

# PV-Only <PERSON><PERSON> Script for Critical Data Stores
# This script restores only PVs and PVCs for postgres, mariadb, and vaultwarden
# Useful when you want to delete and recreate the stack but keep the data

set -euo pipefail

# Configuration
NAMESPACE="velero"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# List available backups for PV restoration
list_pv_backups() {
    log_info "Available backups for PV restoration:"
    
    # Show transactional backups (most recent)
    echo ""
    log_info "=== Transactional Backups (Every 15 min, 6h retention) ==="
    kubectl get backups -n "${NAMESPACE}" -l backup-type=transactional \
        --sort-by=.metadata.creationTimestamp \
        -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,CREATED:.metadata.creationTimestamp,AGE:.metadata.creationTimestamp"
    
    # Show hourly backups
    echo ""
    log_info "=== Hourly Backups (24h retention) ==="
    kubectl get backups -n "${NAMESPACE}" -l backup-type=hourly \
        --sort-by=.metadata.creationTimestamp \
        -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,CREATED:.metadata.creationTimestamp,AGE:.metadata.creationTimestamp" | tail -10
    
    # Show daily backups
    echo ""
    log_info "=== Daily Backups (30 days retention) ==="
    kubectl get backups -n "${NAMESPACE}" -l backup-type=daily \
        --sort-by=.metadata.creationTimestamp \
        -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,CREATED:.metadata.creationTimestamp,AGE:.metadata.creationTimestamp" | tail -5
}

# Get backup details with focus on PV content
get_backup_pv_details() {
    local backup_name="$1"
    
    log_info "PV-related details for backup: ${backup_name}"
    
    if ! kubectl get backup "${backup_name}" -n "${NAMESPACE}" &> /dev/null; then
        log_error "Backup ${backup_name} not found"
        return 1
    fi
    
    # Get backup information
    local status=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}')
    local start_time=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.startTimestamp}')
    local completion_time=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.completionTimestamp}')
    local total_items=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.totalItems}')
    local included_namespaces=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.spec.includedNamespaces[*]}')
    local included_resources=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.spec.includedResources[*]}')
    
    echo "  Status: ${status}"
    echo "  Start time: ${start_time}"
    echo "  Completion time: ${completion_time}"
    echo "  Total items: ${total_items}"
    echo "  Included namespaces: ${included_namespaces}"
    echo "  Included resources: ${included_resources:-"All"}"
    
    # Check if this backup includes PVs
    if [[ "${included_resources}" == *"persistentvolumes"* ]] || [[ -z "${included_resources}" ]]; then
        log_info "✅ This backup includes PersistentVolumes"
    else
        log_warn "⚠️ This backup may not include PersistentVolumes"
    fi
}

# Restore only PVs and PVCs from backup
restore_pvs_only() {
    local backup_name="$1"
    local target_namespaces="${2:-vaultwarden,postgresql,mariadb}"
    local restore_name="pv-restore-${backup_name}-$(date +%Y%m%d-%H%M%S)"
    
    log_info "Creating PV-only restore: ${restore_name} from backup: ${backup_name}"
    log_info "Target namespaces: ${target_namespaces}"
    
    # Check if backup exists and is completed
    local backup_status=$(kubectl get backup "${backup_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
    
    if [[ "${backup_status}" != "Completed" ]]; then
        log_error "Backup ${backup_name} is not in Completed state (current: ${backup_status})"
        return 1
    fi
    
    # Create restore manifest for PVs only
    local restore_manifest="/tmp/restore-${restore_name}.yaml"
    
    cat > "${restore_manifest}" <<EOF
apiVersion: velero.io/v1
kind: Restore
metadata:
  name: ${restore_name}
  namespace: ${NAMESPACE}
spec:
  backupName: ${backup_name}
  restorePVs: true
  includedResources:
    - persistentvolumes
    - persistentvolumeclaims
    - secrets
    - configmaps
  excludedResources:
    - pods
    - replicasets
    - deployments
    - statefulsets
    - services
    - ingresses
    - events
    - events.events.k8s.io
EOF
    
    # Add namespace filter
    if [[ -n "${target_namespaces}" ]]; then
        echo "  includedNamespaces:" >> "${restore_manifest}"
        IFS=',' read -ra NAMESPACES <<< "${target_namespaces}"
        for ns in "${NAMESPACES[@]}"; do
            echo "    - ${ns}" >> "${restore_manifest}"
        done
    fi
    
    # Show the restore manifest for review
    log_info "Restore manifest:"
    cat "${restore_manifest}"
    echo ""
    
    # Confirm before proceeding
    log_warn "This will restore PVs and PVCs for: ${target_namespaces}"
    log_warn "Make sure the target namespaces exist and applications are scaled down"
    read -p "Continue with PV restore? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "PV restore cancelled"
        rm -f "${restore_manifest}"
        return 0
    fi
    
    # Apply the restore
    kubectl apply -f "${restore_manifest}"
    
    log_info "PV restore ${restore_name} created successfully"
    log_info "Monitor progress with: kubectl get restore ${restore_name} -n ${NAMESPACE} -w"
    
    # Clean up temporary file
    rm -f "${restore_manifest}"
    
    # Monitor the restore
    monitor_pv_restore "${restore_name}"
}

# Monitor PV restore progress
monitor_pv_restore() {
    local restore_name="$1"
    local timeout="${2:-600}"  # 10 minutes default
    
    log_info "Monitoring PV restore: ${restore_name} (timeout: ${timeout}s)"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    while [[ $(date +%s) -lt ${end_time} ]]; do
        local status=$(kubectl get restore "${restore_name}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
        
        case "${status}" in
            "Completed")
                log_info "✅ PV restore ${restore_name} completed successfully"
                
                # Get restore details
                local warnings=$(kubectl get restore "${restore_name}" -n "${NAMESPACE}" -o jsonpath='{.status.warnings}')
                local errors=$(kubectl get restore "${restore_name}" -n "${NAMESPACE}" -o jsonpath='{.status.errors}')
                
                if [[ -n "${warnings}" && "${warnings}" != "0" ]]; then
                    log_warn "Restore completed with ${warnings} warnings"
                fi
                
                if [[ -n "${errors}" && "${errors}" != "0" ]]; then
                    log_error "Restore completed with ${errors} errors"
                fi
                
                # Show next steps
                log_info ""
                log_info "Next steps after PV restore:"
                log_info "1. Verify PVCs are bound: kubectl get pvc -n vaultwarden,postgresql,mariadb"
                log_info "2. Redeploy your applications"
                log_info "3. Verify data integrity"
                
                return 0
                ;;
            "Failed")
                log_error "❌ PV restore ${restore_name} failed"
                kubectl describe restore "${restore_name}" -n "${NAMESPACE}"
                return 1
                ;;
            "InProgress")
                log_info "🔄 PV restore ${restore_name} is in progress..."
                ;;
            "NotFound")
                log_error "❌ Restore ${restore_name} not found"
                return 1
                ;;
            *)
                log_debug "Restore ${restore_name} status: ${status}"
                ;;
        esac
        
        sleep 10
    done
    
    log_error "Timeout waiting for PV restore ${restore_name} to complete"
    return 1
}

# Prepare for stack deletion and PV restoration
prepare_for_stack_deletion() {
    log_info "Preparing for stack deletion and PV restoration..."
    
    # Check current PVCs
    log_info "Current PVCs in critical namespaces:"
    for ns in vaultwarden postgresql mariadb; do
        if kubectl get namespace "${ns}" &> /dev/null; then
            echo ""
            log_info "=== ${ns} namespace ==="
            kubectl get pvc -n "${ns}" -o custom-columns="NAME:.metadata.name,STATUS:.status.phase,VOLUME:.spec.volumeName,SIZE:.spec.resources.requests.storage,STORAGECLASS:.spec.storageClassName"
        else
            log_warn "Namespace ${ns} does not exist"
        fi
    done
    
    echo ""
    log_info "Before deleting your stack:"
    log_info "1. Create a fresh backup: kubectl create -f - <<EOF"
    log_info "apiVersion: velero.io/v1"
    log_info "kind: Backup"
    log_info "metadata:"
    log_info "  name: pre-deletion-backup-\$(date +%Y%m%d-%H%M%S)"
    log_info "  namespace: velero"
    log_info "spec:"
    log_info "  includedNamespaces: [vaultwarden, postgresql, mariadb]"
    log_info "  storageLocation: default"
    log_info "  ttl: 168h"
    log_info "EOF"
    log_info ""
    log_info "2. Wait for backup completion"
    log_info "3. Delete your stack"
    log_info "4. Recreate namespaces if needed"
    log_info "5. Run this script to restore PVs"
    log_info "6. Redeploy applications"
}

# Show usage
usage() {
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  list                          List available backups for PV restoration"
    echo "  details <backup-name>         Show backup details with PV information"
    echo "  restore <backup-name> [ns]    Restore only PVs and PVCs from backup"
    echo "  monitor <restore-name>        Monitor PV restore progress"
    echo "  prepare                       Show preparation steps for stack deletion"
    echo ""
    echo "Examples:"
    echo "  $0 list                                           # List all backups"
    echo "  $0 details transactional-backup-20240610-120000   # Show backup details"
    echo "  $0 restore transactional-backup-20240610-120000   # Restore PVs from backup"
    echo "  $0 restore daily-backup-20240610-020000 vaultwarden,postgresql  # Restore specific namespaces"
    echo "  $0 prepare                                        # Show preparation steps"
    echo ""
    echo "Note: This script focuses on restoring only PVs and PVCs, not the full application stack."
}

# Main function
main() {
    local command="${1:-}"
    
    if [[ -z "${command}" ]]; then
        usage
        exit 1
    fi
    
    case "${command}" in
        "list")
            list_pv_backups
            ;;
        "details")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            get_backup_pv_details "$2"
            ;;
        "restore")
            if [[ -z "${2:-}" ]]; then
                log_error "Backup name required"
                usage
                exit 1
            fi
            restore_pvs_only "$2" "${3:-}"
            ;;
        "monitor")
            if [[ -z "${2:-}" ]]; then
                log_error "Restore name required"
                usage
                exit 1
            fi
            monitor_pv_restore "$2" "${3:-600}"
            ;;
        "prepare")
            prepare_for_stack_deletion
            ;;
        "-h"|"--help")
            usage
            ;;
        *)
            log_error "Unknown command: ${command}"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
