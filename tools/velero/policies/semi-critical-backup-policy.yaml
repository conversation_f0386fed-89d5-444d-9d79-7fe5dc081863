# Semi-critical applications backup policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: semi-critical-backup-policy
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-policy
data:
  policy.yaml: |
    # Semi-Critical Applications Backup Policy
    # Applications: infisical, unleash, metabase

    backup_frequency: "0 */4 * * *"  # Every 4 hours
    retention_period: "168h"  # 7 days

    included_namespaces:
      - infisical
      - unleash
      - metabase

    included_resources:
      - persistentvolumes
      - persistentvolumeclaims
      - secrets
      - configmaps
      - deployments
      - services
      - ingresses

    excluded_resources:
      - events
      - events.events.k8s.io
      - pods
      - replicasets
