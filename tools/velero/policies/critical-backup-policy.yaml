# Critical applications backup policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: critical-backup-policy
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-policy
data:
  policy.yaml: |
    # Critical Applications Backup Policy
    # Applications: vaultwarden, postgresql, mariadb

    backup_frequency: "*/2 * * * *"  # Every 2 hours
    retention_period: "48h"

    included_namespaces:
      - vaultwarden
      - postgresql
      - mariadb

    included_resources:
      - persistentvolumes
      - persistentvolumeclaims
      - secrets
      - configmaps
      - deployments
      - statefulsets
      - services

    excluded_resources:
      - events
      - events.events.k8s.io
      - pods
      - replicasets

    backup_hooks:
      pre_backup:
        - name: database-consistency
          container: postgresql
          command: ["psql", "-U", "postgres", "-c", "CHECKPOINT;"]
        - name: mariadb-flush
          container: mariadb
          command:
            - "mysql"
            - "-u"
            - "root"
            - "-p${MARIADB_ROOT_PASSWORD}"
            - "-e"
            - "FLUSH TABLES WITH READ LOCK; FLUSH LOGS;"

      post_backup:
        - name: release-locks
          container: mariadb
          command: ["mysql", "-u", "root", "-p${MARIADB_ROOT_PASSWORD}", "-e", "UNLOCK TABLES;"]
