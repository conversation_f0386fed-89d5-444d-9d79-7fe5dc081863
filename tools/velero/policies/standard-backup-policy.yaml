# Standard applications backup policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: standard-backup-policy
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-policy
data:
  policy.yaml: |
    # Standard Applications Backup Policy
    # Applications: flowise, strapi, drone

    backup_frequency: "0 2 * * *"  # Daily at 2 AM
    retention_period: "720h"  # 30 days

    included_namespaces:
      - flowise
      - strapi
      - drone

    included_resources:
      - persistentvolumes
      - persistentvolumeclaims
      - secrets
      - configmaps
      - deployments
      - services
      - ingresses

    excluded_resources:
      - events
      - events.events.k8s.io
      - pods
      - replicasets
