# Backup validation policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-validation-policy
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-policy
data:
  validation.yaml: |
    # Backup Validation Policy

    validation_schedule: "0 6 * * *"  # Daily at 6 AM

    validation_checks:
      - name: backup_completion
        description: "Check if scheduled backups completed successfully"
        frequency: "hourly"

      - name: backup_size_validation
        description: "Validate backup size is within expected range"
        frequency: "daily"
        min_size_mb: 10
        max_size_gb: 100

      - name: restore_test
        description: "Perform test restore to validate backup integrity"
        frequency: "weekly"
        test_namespace: "velero-test"

      - name: storage_location_health
        description: "Check backup storage location accessibility"
        frequency: "hourly"

      - name: retention_compliance
        description: "Verify backup retention policy compliance"
        frequency: "daily"

    notification_channels:
      - type: "slack"
        webhook_url: "${SLACK_WEBHOOK_URL}"
        severity_levels: ["critical", "warning"]

      - type: "email"
        smtp_server: "${SMTP_SERVER}"
        recipients: ["<EMAIL>"]
        severity_levels: ["critical"]
