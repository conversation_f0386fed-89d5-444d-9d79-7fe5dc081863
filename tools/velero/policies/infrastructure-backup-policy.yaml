# Infrastructure backup policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: infrastructure-backup-policy
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-policy
data:
  policy.yaml: |
    # Infrastructure Backup Policy
    # Applications: monitoring, karpenter, external-secrets

    backup_frequency: "0 3 * * 0"  # Weekly on Sunday at 3 AM
    retention_period: "2016h"  # 12 weeks

    included_namespaces:
      - monitoring
      - karpenter
      - external-secrets
      - velero

    included_resources:
      - persistentvolumes
      - persistentvolumeclaims
      - secrets
      - configmaps
      - deployments
      - daemonsets
      - services
      - customresourcedefinitions

    excluded_resources:
      - events
      - events.events.k8s.io
      - pods
      - replicasets
