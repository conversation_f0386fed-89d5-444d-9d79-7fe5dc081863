# Backup retention policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-retention-policy
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-policy
data:
  retention.yaml: |
    # Backup Retention Policy

    # Critical data retention
    critical:
      frequent: "48h"    # Every 2 hours, keep for 48 hours
      hourly: "168h"     # Every hour during business hours, keep for 7 days
      daily: "720h"      # Daily, keep for 30 days
      weekly: "2016h"    # Weekly, keep for 12 weeks

    # Semi-critical data retention
    semi_critical:
      frequent: "24h"    # Every 4 hours, keep for 24 hours
      daily: "720h"      # Daily, keep for 30 days
      weekly: "1344h"    # Weekly, keep for 8 weeks

    # Standard data retention
    standard:
      daily: "720h"      # Daily, keep for 30 days
      weekly: "1344h"    # Weekly, keep for 8 weeks
      monthly: "4320h"   # Monthly, keep for 6 months

    # Infrastructure retention
    infrastructure:
      weekly: "2016h"    # Weekly, keep for 12 weeks
      monthly: "8760h"   # Monthly, keep for 1 year
