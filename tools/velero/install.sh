#!/bin/bash

# Velero Installation Script for Treebo Tools Cluster
# This script sets up Velero backup solution with proper S3 configuration

set -euo pipefail

# Configuration
CLUSTER_NAME="cluster-treebo-tools-common"
REGION="ap-south-1"
BUCKET_NAME="s3-velero-treebo-v1"
NAMESPACE="velero"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi

    # Check if we can connect to the cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check if AWS CLI is available
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi

    # Check if S3 bucket exists
    if ! aws s3 ls "s3://${BUCKET_NAME}" &> /dev/null; then
        log_error "S3 bucket ${BUCKET_NAME} does not exist. Please run the S3 setup script first:"
        log_error "cd clusters && npx ts-node scripts/setup-s3-buckets-and-roles.ts treebo-tools-common"
        exit 1
    fi

    log_info "Prerequisites check passed"
}

# Install Velero CRDs
install_crds() {
    log_info "Installing Velero CRDs..."

    # Get the latest Velero version
    VELERO_VERSION="v1.16.0"

    # Install CRDs
    kubectl apply -f "https://github.com/vmware-tanzu/velero/releases/download/${VELERO_VERSION}/00-crds.yaml" || {
        log_warn "Failed to install CRDs from GitHub, trying alternative method..."

        # Alternative: Install via Helm
        helm repo add vmware-tanzu https://vmware-tanzu.github.io/helm-charts
        helm repo update

        # Install CRDs only
        helm template velero vmware-tanzu/velero \
            --namespace ${NAMESPACE} \
            --include-crds \
            --set installCRDs=true \
            --set image.tag=${VELERO_VERSION} | \
            kubectl apply -f -
    }

    log_info "Velero CRDs installed successfully"
}

# Create namespace

create_namespace() {
    log_info "Creating Velero namespace..."

    # Check if namespace already exists
    if kubectl get namespace "${NAMESPACE}" &> /dev/null; then
        log_warn "Namespace ${NAMESPACE} already exists"

        # Show current namespace details
        log_info "Current namespace details:"
        kubectl get namespace "${NAMESPACE}" -o yaml

        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Operation cancelled"
            exit 0
        fi
    else
        # Apply namespace
        log_info "Applying namespace configuration..."
        kubectl apply -f "tools/velero/base/namespace.yaml"

        # Wait for namespace to be ready
        log_info "Waiting for namespace to be ready..."
        kubectl wait --for=condition=Ready namespace/${NAMESPACE} --timeout=30s || true

        log_info "✅ Velero namespace created successfully"
    fi

    # Show final namespace status
    log_info "Final namespace status:"
    kubectl get namespace "${NAMESPACE}" -o wide
}


# Deploy Velero
deploy_velero() {
    log_info "Deploying Velero..."

    # Apply the Velero configuration
     kustomize build --enable-helm --load-restrictor=LoadRestrictionsNone tools/velero/overlays/treebo-tools-common | kubectl apply -f -

    # Wait for Velero to be ready
    log_info "Waiting for Velero deployment to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/velero -n ${NAMESPACE}

    # Wait for node agent to be ready
    log_info "Waiting for Velero node agent to be ready..."
    kubectl wait --for=condition=ready --timeout=300s pods -l name=node-agent -n ${NAMESPACE}

    log_info "Velero deployed successfully"
}

# Verify installation
verify_installation() {
    log_info "Verifying Velero installation..."

    # Check if Velero pods are running
    if ! kubectl get pods -n ${NAMESPACE} | grep -q "Running"; then
        log_error "Velero pods are not running"
        kubectl get pods -n ${NAMESPACE}
        exit 1
    fi

    # Check backup storage location
    if ! kubectl get backupstoragelocation default -n ${NAMESPACE} &> /dev/null; then
        log_error "Backup storage location not found"
        exit 1
    fi

    # Check volume snapshot location
    if ! kubectl get volumesnapshotlocation default -n ${NAMESPACE} &> /dev/null; then
        log_error "Volume snapshot location not found"
        exit 1
    fi

    # Check if schedules are created
    log_info "Checking backup schedules..."
    kubectl get schedules -n ${NAMESPACE}

    log_info "Velero installation verified successfully"
}

# Create test backup
create_test_backup() {
    log_info "Creating test backup..."

    # Create a test backup of the velero namespace itself
    kubectl create -f - <<EOF
apiVersion: velero.io/v1
kind: Backup
metadata:
  name: velero-test-backup-$(date +%Y%m%d-%H%M%S)
  namespace: ${NAMESPACE}
spec:
  includedNamespaces:
    - ${NAMESPACE}
  storageLocation: default
  ttl: 24h
  metadata:
    labels:
      backup-type: test
EOF

    log_info "Test backup created. Check status with: kubectl get backups -n ${NAMESPACE}"
}

# Main installation function
main() {
    log_info "Starting Velero installation for ${CLUSTER_NAME}..."

    check_prerequisites
    create_namespace
    install_crds
    deploy_velero
    verify_installation
    create_test_backup

    log_info "Velero installation completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Monitor backup schedules: kubectl get schedules -n ${NAMESPACE}"
    log_info "2. Check backup status: kubectl get backups -n ${NAMESPACE}"
    log_info "3. View Velero logs: kubectl logs -n ${NAMESPACE} deployment/velero"
    log_info "4. Install Velero CLI for manual operations (optional)"
    log_info ""
    log_info "Backup schedules configured:"
    log_info "- Transactional: Every 15 minutes (6h retention) - PVs only for critical data"
    log_info "- Hourly: Every hour (24h retention) - All applications"
    log_info "- Daily: Once per day (30 days retention) - Complete infrastructure"
}

# Run main function
main "$@"
