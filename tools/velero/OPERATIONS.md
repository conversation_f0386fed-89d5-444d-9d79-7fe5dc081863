# Velero Operations Guide

This guide covers day-to-day operations, troubleshooting, and maintenance procedures for the Velero backup system.

## Daily Operations

### Check Backup Status
```bash
# Check recent backups
kubectl get backups -n velero --sort-by=.metadata.creationTimestamp

# Check backup schedules
kubectl get schedules -n velero

# Check failed backups
kubectl get backups -n velero -o json | jq '.items[] | select(.status.phase == "Failed") | .metadata.name'
```

### Validate Backups
```bash
# Run backup validation script
./tools/velero/scripts/backup-validation.sh

# Check specific backup
./tools/velero/scripts/backup-validation.sh 12  # Last 12 hours
```

## Weekly Operations

### Storage Usage Review
```bash
# Check S3 bucket usage
aws s3 ls s3://s3-velero-treebo-v1 --recursive --human-readable --summarize

# Check backup sizes
kubectl get backups -n velero -o custom-columns="NAME:.metadata.name,SIZE:.status.progress.totalBytes,ITEMS:.status.totalItems"
```

### Test Restore
```bash
# Perform test restore to validate backup integrity
./tools/velero/scripts/restore.sh list vaultwarden
./tools/velero/scripts/restore.sh details <backup-name>

# Test restore to temporary namespace (manual verification)
kubectl create namespace velero-test
velero restore create test-restore-$(date +%Y%m%d) \
  --from-backup <backup-name> \
  --namespace-mappings vaultwarden:velero-test
```

## Monthly Operations

### Cleanup Old Backups
```bash
# List old backups (older than retention policy)
kubectl get backups -n velero -o json | \
  jq '.items[] | select(.metadata.creationTimestamp < "'$(date -d '30 days ago' -u +%Y-%m-%dT%H:%M:%SZ)'") | .metadata.name'

# Cleanup is automatic via TTL, but verify
velero backup get | grep -E "(Expired|Deleting)"
```

### Review and Update Policies
```bash
# Review backup schedules
kubectl get schedules -n velero -o yaml

# Check storage location health
kubectl get backupstoragelocation -n velero
kubectl get volumesnapshotlocation -n velero
```

## Emergency Procedures

### Disaster Recovery

#### Full Cluster Restore
```bash
# 1. Install Velero on new cluster
./tools/velero/install.sh

# 2. List available backups
velero backup get

# 3. Restore entire cluster
velero restore create cluster-restore-$(date +%Y%m%d) \
  --from-backup <latest-weekly-backup>

# 4. Monitor restore progress
kubectl get restore cluster-restore-$(date +%Y%m%d) -n velero -w
```

#### Application-Specific Recovery

##### Vaultwarden Recovery
```bash
# 1. Scale down current deployment
kubectl scale deployment vaultwarden -n vaultwarden --replicas=0

# 2. Delete PVCs (if corrupted)
kubectl delete pvc -n vaultwarden --all

# 3. Restore from backup
./tools/velero/scripts/restore.sh vaultwarden <backup-name>

# 4. Verify and scale up
kubectl scale deployment vaultwarden -n vaultwarden --replicas=1
```

##### Database Recovery
```bash
# PostgreSQL
./tools/velero/scripts/restore.sh postgresql <backup-name>

# MariaDB
./tools/velero/scripts/restore.sh mariadb <backup-name>
```

## Troubleshooting

### Common Issues

#### Backup Failures
```bash
# Check backup logs
kubectl logs -n velero deployment/velero

# Check specific backup logs
velero backup logs <backup-name>

# Check backup storage location
kubectl describe backupstoragelocation default -n velero
```

#### S3 Access Issues
```bash
# Verify IAM role
kubectl describe serviceaccount velero -n velero

# Test S3 access
kubectl run -it --rm debug --image=amazon/aws-cli --restart=Never -- \
  aws s3 ls s3://s3-velero-treebo-v1/

# Check bucket policy
aws s3api get-bucket-policy --bucket s3-velero-treebo-v1
```

#### Volume Snapshot Issues
```bash
# Check EBS CSI driver
kubectl get pods -n kube-system | grep ebs-csi

# Check volume snapshot class
kubectl get volumesnapshotclass

# Check snapshot location
kubectl describe volumesnapshotlocation default -n velero
```

### Performance Issues

#### Slow Backups
```bash
# Check resource usage
kubectl top pods -n velero

# Check backup progress
kubectl get backup <backup-name> -n velero -o yaml | grep progress

# Increase resources if needed
kubectl patch deployment velero -n velero -p '{"spec":{"template":{"spec":{"containers":[{"name":"velero","resources":{"limits":{"cpu":"2000m","memory":"1Gi"}}}]}}}}'
```

#### High Memory Usage
```bash
# Check memory usage
kubectl top pods -n velero

# Check for memory leaks
kubectl logs -n velero deployment/velero | grep -i "memory\|oom"

# Restart Velero if needed
kubectl rollout restart deployment/velero -n velero
```

## Monitoring and Alerting

### Prometheus Metrics
```bash
# Check Velero metrics
kubectl port-forward -n velero deployment/velero 8085:8085
curl http://localhost:8085/metrics | grep velero
```

### Key Metrics to Monitor
- `velero_backup_failure_total`: Failed backup count
- `velero_backup_success_total`: Successful backup count
- `velero_backup_duration_seconds`: Backup duration
- `velero_restore_failed_total`: Failed restore count
- `velero_backup_storage_location_available`: Storage location availability

### Alerts Configuration
Alerts are configured in `tools/velero/monitoring/alerts.yaml`:
- Backup failures
- Storage location unavailability
- Pod health issues
- Performance degradation

## Maintenance

### Velero Upgrades
```bash
# 1. Check current version
kubectl get deployment velero -n velero -o yaml | grep image:

# 2. Update Helm chart version in kustomization.yaml
# 3. Apply updates
kubectl apply -k tools/velero/overlays/treebo-tools-common/

# 4. Verify upgrade
kubectl rollout status deployment/velero -n velero
```

### Configuration Updates
```bash
# Update backup schedules
kubectl apply -f tools/velero/overlays/treebo-tools-common/backup-schedules.yaml

# Update backup hooks
kubectl apply -f tools/velero/overlays/treebo-tools-common/backup-hooks.yaml

# Restart Velero to pick up changes
kubectl rollout restart deployment/velero -n velero
```

## Security

### Access Control
- Velero service account has minimal required permissions
- S3 bucket access is restricted to Velero service account
- Backup encryption is enabled at rest

### Audit
```bash
# Check backup access logs
aws logs filter-log-events --log-group-name /aws/s3/s3-velero-treebo-v1

# Review IAM role usage
aws iam get-role-policy --role-name role-eks-s3-velero-treebo-v1 --policy-name S3Access
```

## Cost Optimization

### Storage Costs
- Lifecycle policies automatically transition to cheaper storage
- Old backups are automatically deleted per retention policy
- Monitor S3 costs in AWS Cost Explorer

### Backup Optimization
```bash
# Exclude unnecessary resources
kubectl patch schedule daily-backup -n velero --type='merge' -p='{"spec":{"template":{"excludedResources":["events","pods"]}}}'

# Use incremental backups where possible
kubectl patch schedule frequent-backup -n velero --type='merge' -p='{"spec":{"template":{"defaultVolumesToFsBackup":true}}}'
```
