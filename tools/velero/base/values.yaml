# Velero Helm Chart Configuration
# Backup and disaster recovery for Kubernetes

# Image configuration
image:
  repository: velero/velero
  tag: v1.14.1
  pullPolicy: IfNotPresent

# Init container for plugins
initContainers:
  - name: velero-plugin-for-aws
    image: velero/velero-plugin-for-aws:v1.10.0
    imagePullPolicy: IfNotPresent
    volumeMounts:
      - mountPath: /target
        name: plugins

# Service account configuration
serviceAccount:
  server:
    create: true
    name: velero
    annotations: {}

# RBAC configuration
rbac:
  create: true
  clusterAdministrator: true

# Credentials configuration (will be overridden in overlays)
credentials:
  useSecret: false
  name: ""
  secretContents: {}

# Configuration for backup storage location
configuration:
  # Cloud provider
  provider: aws

  # Backup storage location
  backupStorageLocation:
    - name: default
      provider: aws
      bucket: ""  # Will be set in overlay
      config:
        region: ap-south-1
        s3ForcePathStyle: false
        s3Url: ""
        kmsKeyId: ""
        serverSideEncryption: AES256
        insecureSkipTLSVerify: false

  # Volume snapshot location
  volumeSnapshotLocation:
    - name: default
      provider: aws
      config:
        region: ap-south-1

  # Backup retention
  defaultBackupTTL: 720h  # 30 days

  # Restore resource priorities
  restoreResourcePriorities: >-
    namespaces,
    storageclasses,
    volumesnapshotclass.snapshot.storage.k8s.io,
    customresourcedefinitions,
    persistentvolumes,
    persistentvolumeclaims,
    secrets,
    configmaps,
    serviceaccounts,
    limitranges,
    pods,
    replicasets.apps,
    clusterroles.rbac.authorization.k8s.io,
    clusterrolebindings.rbac.authorization.k8s.io,
    roles.rbac.authorization.k8s.io,
    rolebindings.rbac.authorization.k8s.io,
    services,
    daemonsets.apps,
    deployments.apps,
    replicationcontrollers,
    statefulsets.apps,
    cronjobs.batch,
    jobs.batch,
    ingresses.extensions,
    networkpolicies.networking.k8s.io,
    poddisruptionbudgets.policy

# Deployment configuration
deployNodeAgent: true

# Node agent configuration (for file-level backups)
nodeAgent:
  podVolumePath: /var/lib/kubelet/pods
  privileged: false
  tolerations: []
  resources:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 1Gi

# Velero server configuration
resources:
  requests:
    cpu: 500m
    memory: 128Mi
  limits:
    cpu: 1000m
    memory: 512Mi

# Tolerations for scheduling
tolerations: []

# Node selector
nodeSelector: {}

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 65534
  fsGroup: 65534

# Container security context
containerSecurityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true

# Metrics configuration
metrics:
  enabled: true
  scrapeInterval: 30s
  scrapeTimeout: 10s
  service:
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "8085"
      prometheus.io/path: "/metrics"
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8085"
    prometheus.io/path: "/metrics"

# Backup schedules (will be created separately)
schedules: {}

# Cleanup configuration
cleanUpCRDs: false

# Log level
logLevel: info

# Log format
logFormat: text

# Default volumes to snapshot
defaultVolumesToFsBackup: false

# Features
features: ""

# Upgrade job configuration
upgradeCRDs: true
