# Prometheus alerts for Velero backup monitoring
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: velero-alerts
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: monitoring
spec:
  groups:
    - name: velero.backup
      interval: 30s
      rules:
        - alert: VeleroBackupFailed
          expr: increase(velero_backup_failure_total[1h]) > 0
          for: 0m
          labels:
            severity: critical
            component: velero
          annotations:
            summary: "Velero backup failed"
            description: "Velero backup has failed in the last hour. Check backup logs for details."

        - alert: VeleroBackupPartialFailure
          expr: increase(velero_backup_partial_failure_total[1h]) > 0
          for: 0m
          labels:
            severity: warning
            component: velero
          annotations:
            summary: "Velero backup partially failed"
            description: "Velero backup has partially failed in the last hour. Some resources may not be backed up."

        - alert: VeleroBackupTooOld
          expr: time() - velero_backup_last_successful_timestamp > 86400
          for: 0m
          labels:
            severity: warning
            component: velero
          annotations:
            summary: "Velero backup is too old"
            description: "The last successful Velero backup is older than 24 hours."

        - alert: VeleroRestoreFailed
          expr: increase(velero_restore_failed_total[1h]) > 0
          for: 0m
          labels:
            severity: critical
            component: velero
          annotations:
            summary: "Velero restore failed"
            description: "Velero restore operation has failed in the last hour."

        - alert: VeleroScheduleNotRunning
          expr: velero_backup_schedule_last_backup_timestamp == 0
          for: 30m
          labels:
            severity: warning
            component: velero
          annotations:
            summary: "Velero backup schedule not running"
            description: "Velero backup schedule {{ $labels.schedule }} has not created any backups."

    - name: velero.storage
      interval: 30s
      rules:
        - alert: VeleroBackupStorageLocationUnavailable
          expr: velero_backup_storage_location_available == 0
          for: 5m
          labels:
            severity: critical
            component: velero
          annotations:
            summary: "Velero backup storage location unavailable"
            description: "Velero backup storage location {{ $labels.backup_storage_location }} is unavailable."

        - alert: VeleroVolumeSnapshotLocationUnavailable
          expr: velero_volume_snapshot_location_available == 0
          for: 5m
          labels:
            severity: critical
            component: velero
          annotations:
            summary: "Velero volume snapshot location unavailable"
            description: "Velero volume snapshot location {{ $labels.volume_snapshot_location }} is unavailable."

    - name: velero.pod
      interval: 30s
      rules:
        - alert: VeleroPodNotReady
          expr: kube_pod_status_ready{namespace="velero", condition="true"} == 0
          for: 5m
          labels:
            severity: critical
            component: velero
          annotations:
            summary: "Velero pod not ready"
            description: "Velero pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is not ready."

        - alert: VeleroPodCrashLooping
          expr: rate(kube_pod_container_status_restarts_total{namespace="velero"}[15m]) > 0
          for: 5m
          labels:
            severity: warning
            component: velero
          annotations:
            summary: "Velero pod crash looping"
            description: "Velero pod {{ $labels.pod }} is crash looping."

    - name: velero.performance
      interval: 30s
      rules:
        - alert: VeleroHighMemoryUsage
          expr: >
            container_memory_usage_bytes{namespace="velero", container="velero"} /
            container_spec_memory_limit_bytes{namespace="velero", container="velero"} > 0.9
          for: 5m
          labels:
            severity: warning
            component: velero
          annotations:
            summary: "Velero high memory usage"
            description: "Velero container is using more than 90% of its memory limit."

        - alert: VeleroHighCPUUsage
          expr: >
            rate(container_cpu_usage_seconds_total{namespace="velero", container="velero"}[5m]) /
            container_spec_cpu_quota{namespace="velero", container="velero"} *
            container_spec_cpu_period{namespace="velero", container="velero"} > 0.9
          for: 5m
          labels:
            severity: warning
            component: velero
          annotations:
            summary: "Velero high CPU usage"
            description: "Velero container is using more than 90% of its CPU limit."
