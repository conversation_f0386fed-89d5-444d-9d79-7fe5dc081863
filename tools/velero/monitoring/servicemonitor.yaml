# ServiceMonitor for Velero metrics collection
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: velero
  namespace: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: velero
  endpoints:
    - port: monitoring
      interval: 30s
      path: /metrics
      scheme: http
  namespaceSelector:
    matchNames:
      - velero
