# Velero Backup Solution

Velero is a backup and disaster recovery solution for Kubernetes clusters. This setup provides comprehensive backup strategies for critical applications including vaultwarden, PostgreSQL, and MariaDB.

## Overview

### Backup Strategy

- **Transactional Backups**: Every 15 minutes for critical data stores (6h retention)
- **Hourly Backups**: Every hour for all applications (24h retention)
- **Daily Backups**: Once per day including infrastructure (30 days retention)

### Supported Applications

- **Vaultwarden**: Password manager data and attachments
- **PostgreSQL**: Database backups with pre/post hooks
- **MariaDB**: Database backups with pre/post hooks
- **All PVCs**: Persistent volume backups

## Prerequisites

1. **S3 Bucket Setup**: Run the S3 setup script to create required buckets and IAM roles

   ```bash
   cd clusters
   npx ts-node scripts/setup-s3-buckets-and-roles.ts treebo-tools-common
   ```

2. **AWS Credentials**: Ensure the cluster has proper IAM roles for S3 access

## Installation

1. **Install Velero CLI** (optional, for manual operations):

   ```bash
   # macOS
   brew install velero

   # Linux
   wget https://github.com/vmware-tanzu/velero/releases/latest/download/velero-linux-amd64.tar.gz
   tar -xvf velero-linux-amd64.tar.gz
   sudo mv velero-linux-amd64/velero /usr/local/bin/
   ```

2. **Deploy Velero**:

   ```bash
   # Option 1: Use the install script (recommended)
   ./tools/velero/install.sh

   # Option 2: Manual deployment
   # First create the namespace
   kubectl apply -f tools/velero/base/namespace.yaml
   # Then deploy Velero
   kubectl apply -k tools/velero/overlays/treebo-tools-common/

   # Option 3: Create namespace separately (useful for troubleshooting)
   ./tools/velero/scripts/create-namespace.sh
   ```

## Backup Schedules

### Automatic Schedules

- `transactional-backup`: Every 15 minutes, 6h retention (PVs only for postgres/mariadb/vaultwarden)
- `hourly-backup`: Every hour, 24h retention (all applications)
- `daily-backup`: Daily at 2 AM, 30 days retention (complete infrastructure)

### Manual Backups

```bash
# Create immediate backup
velero backup create manual-backup-$(date +%Y%m%d-%H%M%S) \
  --include-namespaces vaultwarden,postgresql,mariadb

# Backup specific application
velero backup create vaultwarden-backup-$(date +%Y%m%d-%H%M%S) \
  --include-namespaces vaultwarden
```

## Restore Operations

### Full Cluster Restore

```bash
# List available backups
velero backup get

# Restore from backup
velero restore create --from-backup <backup-name>
```

### Application-Specific Restore

```bash
# Restore vaultwarden
velero restore create vaultwarden-restore-$(date +%Y%m%d-%H%M%S) \
  --from-backup <backup-name> \
  --include-namespaces vaultwarden

# Restore PostgreSQL
velero restore create postgres-restore-$(date +%Y%m%d-%H%M%S) \
  --from-backup <backup-name> \
  --include-namespaces postgresql
```

### PV-Only Restore (For Stack Recreation)

```bash
# List available backups for PV restoration
./tools/velero/scripts/restore-pvs-only.sh list

# Show preparation steps for stack deletion
./tools/velero/scripts/restore-pvs-only.sh prepare

# Restore only PVs and PVCs (useful when recreating stack)
./tools/velero/scripts/restore-pvs-only.sh restore <backup-name>

# Restore specific namespaces only
./tools/velero/scripts/restore-pvs-only.sh restore <backup-name> vaultwarden,postgresql
```

## Monitoring

### Check Backup Status

```bash
# List all backups
kubectl get backups -n velero

# Check backup details
kubectl describe backup <backup-name> -n velero

# Check backup logs
kubectl logs -n velero deployment/velero
```

### Backup Validation

```bash
# Check backup completion
velero backup describe <backup-name>

# Validate backup contents
velero backup logs <backup-name>
```

## Troubleshooting

### Common Issues

1. **S3 Access Denied**: Verify IAM roles and bucket permissions
2. **Backup Failures**: Check pod logs and resource quotas
3. **Restore Issues**: Ensure target namespaces exist

### Debug Commands

```bash
# Check Velero pod status
kubectl get pods -n velero

# Check Velero logs
kubectl logs -n velero deployment/velero

# Check backup storage location
kubectl get backupstoragelocation -n velero

# Check volume snapshot location
kubectl get volumesnapshotlocation -n velero
```

## Security

- **IAM Roles**: Least privilege access to S3 buckets
- **Encryption**: S3 server-side encryption enabled
- **Network**: Internal cluster communication only
- **RBAC**: Proper Kubernetes RBAC for Velero service account

## Cost Optimization

- **Lifecycle Policies**: Automatic transition to cheaper storage classes
- **Retention Policies**: Automatic cleanup of old backups
- **Compression**: Backup compression enabled
- **Incremental Backups**: Only changed data is backed up

## Best Practices

1. **Test Restores**: Regularly test backup restoration
2. **Monitor Storage**: Keep track of S3 storage usage
3. **Validate Backups**: Ensure backups complete successfully
4. **Document Procedures**: Maintain disaster recovery runbooks
5. **Access Control**: Limit access to backup operations
