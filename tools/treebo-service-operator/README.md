# TreeboService Operator (Deprecated use treebo-helm-charts)

A modern Kubernetes operator that simplifies the deployment and management of Treebo microservices using **Pydantic v2** for type safety, **uv** for fast Python package management, and **intelligent dependency orchestration**.

## 📚 Table of Contents

- [🎯 Overview](#-overview)
- [🚀 Key Features](#-key-features)
- [🏗️ Architecture](#-architecture)
- [⚙️ Workload Management](#-workload-management)
- [📋 Tasks & Jobs](#-tasks--jobs)
- [🔧 Technical Implementation](#-technical-implementation)
- [🚀 Quick Start](#-quick-start)
- [📋 Configuration Examples](#-configuration-examples)
- [🛠️ Development Workflow](#-development-workflow)
- [📈 Migration Benefits](#-migration-benefits)
- [🎉 Conclusion](#-conclusion)

## 🎯 Overview

The TreeboService operator transforms complex microservice deployments from **15+ YAML files** into a **single, type-safe resource definition** with automatic validation, monitoring, resource generation, and intelligent dependency orchestration.

### **🚀 Key Transformation**

**Before**: 15+ separate YAML files per service
```
apps/my-service/overlays/staging/
├── deployment.yaml          ├── celery-deployment.yaml
├── service.yaml             ├── celery-hpa.yaml
├── configmap.yaml           ├── cleanup-cronjob.yaml
├── hpa.yaml                 ├── backup-cronjob.yaml
├── ingress.yaml             ├── migration-job.yaml
├── externalsecret.yaml      ├── servicemonitor.yaml
├── prometheusrule.yaml      └── kustomization.yaml
```

**After**: 1 TreeboService resource
```yaml
apiVersion: treebo.com/v1
kind: TreeboService
metadata:
  name: my-service
spec:
  serviceName: my-service
  environment: staging
  services: [...]  # Multiple web services
  workers: [...]   # Background workers
  tasks: [...]     # Migrations & jobs
  # ... automatic generation of all resources
```

**Benefits**: **90% reduction** in configuration files, type-safe validation, consistent patterns, intelligent dependency management

## 🚀 Key Features

### **✅ Unified Workload Management**
- **Individual WorkloadType**: Each component (service/worker/task) has its own criticality level
- **Dependency Orchestration**: `depends_on` field with circular detection and validation
- **Base Workload Class**: All components inherit from common `Workload` base class
- **Type Safety**: Full Pydantic v2 validation with clear error messages

### **✅ Multi-Service Architecture**
- **Services Array**: Multiple web services in one TreeboService resource
- **Independent Configuration**: Each service has its own health checks, scaling, resources
- **Service-Specific Images**: Override images per service for different components
- **Cross-Service Dependencies**: Services can depend on tasks/other services

### **✅ Advanced Health Checks**
- **Unified Mode**: Single `/health` endpoint for all probes (simple setup)
- **Separate Mode**: Dedicated `/health/live`, `/health/ready`, `/health/startup` (production)
- **Worker Health Checks**: exec, http, tcp probes for background processes
- **Configurable Timeouts**: Per-probe timing configuration

### **✅ Comprehensive Scaling**
- **HPA**: CPU/Memory-based horizontal scaling
- **KEDA**: Event-driven autoscaling with scale-to-zero
- **Worker Scaling**: Redis, Kafka, RabbitMQ, Prometheus triggers
- **Per-Component Scaling**: Independent scaling per service/worker

### **✅ Tasks & Jobs Consolidation**
- **Unified Tasks**: Merged migration + adhocJobs into single `tasks` section
- **Built-in Categories**: migration, cleanup, backup, report, export, import, maintenance, custom
- **Job Types**: One-time jobs and scheduled cronjobs
- **Dependency Blocking**: Migrations can block services from starting

### **✅ Resource Generation**
The operator automatically generates:
- **Deployments**: One per service + workers with proper labels
- **Services**: ClusterIP with correct selectors and ports
- **ConfigMaps**: Service-specific configuration with framework defaults
- **HPA/KEDA**: Horizontal scaling based on configuration
- **Ingresses**: Multiple ingresses with service targeting and path routing
- **Jobs/CronJobs**: Task execution with proper resource limits
- **ExternalSecrets**: AWS Secrets Manager integration
- **ServiceMonitors**: Prometheus scraping configuration
- **PrometheusRules**: Alerting with default + custom rules

## 🏗️ Architecture

### **📁 Project Structure**
```
tools/treebo-service-operator/
├── 📂 controller-src/                    # 🎯 MAIN CONTROLLER CODE
│   ├── 📂 models/                        # 🏗️ Pydantic v2 Models (Modular)
│   │   ├── __init__.py                   # Main models & base Workload class
│   │   ├── alerts.py                     # Monitoring & alerting models
│   │   ├── healthchecks.py               # Health check configurations
│   │   ├── ingress.py                    # Ingress specifications
│   │   ├── jobs.py                       # Task/Job configurations
│   │   ├── resources.py                  # Resource requirements
│   │   ├── scaling.py                    # HPA/KEDA scaling configs
│   │   └── secrets.py                    # Environment & secrets
│   ├── main.py                           # 🚀 Kopf-based operator controller
│   ├── resource_generators.py            # 🔧 Kubernetes resource generation
│   ├── pyproject.toml                    # 📦 Modern Python packaging (uv)
│   └── Dockerfile                        # 🐳 Container build
├── 📂 crds/
│   └── treeboservice.yaml                # 📋 CRD with workloadType & depends_on
├── 📂 base/                              # 🎛️ Operator deployment
├── 📂 examples/
│   └── nexus-treeboservice.yaml          # 🎯 Production Nexus example
└── 📂 scripts/                           # 🛠️ Development tools
```

### **🏗️ Base Workload Class**
All components inherit from a unified `Workload` base class:

```python
class Workload(BaseModel):
    name: str                             # Unique workload name
    enabled: bool = True                  # Enable/disable workload
    workloadType: WorkloadType = CRITICAL # Individual criticality
    depends_on: List[str] = []            # Dependency management
    image: Optional[ImageConfig] = None   # Image overrides
    command: Optional[List[str]] = None   # Custom commands
    args: Optional[List[str]] = None      # Command arguments
    resources: Optional[Resources] = None # Resource requirements
    env: List[EnvVar] = []               # Environment variables
```

### **🔧 Specialized Workload Types**

#### **ServiceSpec (Web Services)**
```python
class ServiceSpec(Workload):
    type: ServiceType = FLASK             # flask, django, fastapi, nodejs, custom
    port: int                             # Service port (1-65535)
    healthCheck: Optional[ServiceHealthCheck] # HTTP health checks
    scaling: Optional[ScalingConfig]      # HPA configuration
```

#### **WorkerSpec (Background Workers)**
```python
class WorkerSpec(Workload):
    type: WorkerType = CELERY             # celery, rabbitmq, kafka, sidekiq, custom
    scaling: Optional[WorkerScaling]      # HPA/KEDA scaling
    healthCheck: Optional[WorkerHealthCheck] # exec, http, tcp health checks
```

#### **TaskSpec (Jobs & Migrations)**
```python
class TaskSpec(Workload):
    category: TaskCategory = CUSTOM       # migration, cleanup, backup, report, etc.
    type: TaskType = JOB                  # job, cronjob
    job: Optional[JobConfig]              # Job-specific configuration
    cronJob: Optional[CronJobConfig]      # CronJob-specific configuration
```

## ⚙️ Workload Management

### **🎯 Individual WorkloadType**
Each component can now have its own criticality level:

- **`critical`**: High availability, on-demand nodes, priority scheduling
- **`semi-critical`**: Balanced availability and cost
- **`sub-critical`**: Cost-optimized with spot instances
- **`non-critical`**: Maximum cost optimization, lowest priority

### **🔗 Dependency Management**
- **Existence Check**: All dependencies must exist in the same TreeboService
- **Circular Detection**: DFS-based circular dependency prevention
- **Cross-Type Dependencies**: Services can depend on tasks, workers on services, etc.

### **📊 Example: Nexus Application**
```yaml
# Migration runs first (critical, no dependencies)
tasks:
  - name: database-migration
    workloadType: critical
    depends_on: []

# Core API waits for migration
services:
  - name: server
    workloadType: critical
    depends_on: ["database-migration"]

# Frontend needs backend API
  - name: frontend
    workloadType: critical
    depends_on: ["server"]

# MCP service is less critical
  - name: mcp-service
    workloadType: semi-critical
    depends_on: ["server"]

# Worker needs main service
workers:
  - name: celery-worker
    workloadType: semi-critical
    depends_on: ["server"]
```

## 📋 Tasks & Jobs

### **🔄 Unified Tasks Section**
Merged `migration` and `adhocJobs` into a single `tasks` section:

#### **Before (Fragmented)**
```yaml
migration:
  enabled: true
  command: ["flask", "db", "upgrade"]

adhocJobs:
  - name: cleanup
    type: job
    command: ["python", "cleanup.py"]
```

#### **After (Unified)**
```yaml
tasks:
  - name: database-migration
    category: migration
    type: job
    command: ["flask", "db", "upgrade"]
    workloadType: critical
    depends_on: []
  
  - name: data-cleanup
    category: cleanup
    type: job
    command: ["python", "cleanup.py"]
    workloadType: sub-critical
    depends_on: ["api"]
```

### **📋 Built-in Categories**
- **`migration`**: Database migrations and schema updates
- **`cleanup`**: Data cleanup and maintenance operations
- **`backup`**: Backup operations and data archiving
- **`report`**: Report generation and analytics
- **`export`**: Data export tasks
- **`import`**: Data import operations
- **`maintenance`**: General system maintenance
- **`custom`**: Custom tasks

### **⚡ Task Types**
- **`job`**: One-time execution (Kubernetes Job)
- **`cronjob`**: Scheduled execution (Kubernetes CronJob)

## 🔧 Technical Implementation

### **🐍 Modern Python Stack**
- **Pydantic v2**: 5-50x faster validation with clear error messages
- **uv**: 10-100x faster dependency resolution and package management
- **structlog**: Structured logging with context
- **kopf**: Kubernetes operator framework with async support

### **📊 Validation & Safety**
- **Type Safety**: Full Pydantic validation with IDE autocomplete
- **Dependency Validation**: Existence check and circular detection
- **Field Validation**: Port ranges, naming conventions, enum values
- **Operational Safety**: Unique names, resource limits, health check validation

### **🔧 Resource Generation**
The `ResourceGenerator` class creates all Kubernetes resources:
- Proper labels and annotations for all resources
- Service-specific ConfigMaps with framework defaults
- Health probes based on service/worker configuration
- Scaling resources (HPA/KEDA) based on configuration
- Ingress resources with service targeting
- Task execution (Jobs/CronJobs) with proper limits

## 🚀 Quick Start

### **1. Setup Development Environment**
```bash
# Setup development environment with uv and Pydantic
./scripts/dev-setup.sh

# Activate virtual environment
cd controller-src && source .venv/bin/activate
```

### **2. Run Tests**
```bash
# Test Pydantic models and validation
cd controller-src
python test_models.py

# Run code quality checks
uv run black --check *.py
uv run ruff check *.py
```

### **3. Build and Deploy Operator**
```bash
# Build Docker image and deploy to Kubernetes
./scripts/build-and-deploy.sh

# Verify operator is running
kubectl get pods -n treebo-service-operator
kubectl logs -f deployment/treebo-service-operator -n treebo-service-operator
```

### **4. Deploy Example Application**
```bash
# Deploy Nexus example
kubectl apply -f examples/nexus-treeboservice.yaml

# Check status
kubectl get treeboservice -A
kubectl describe treeboservice nexus -n nexus
```

## 📋 Configuration Examples

### **🎯 Complete Nexus Example**
```yaml
apiVersion: treebo.com/v1
kind: TreeboService
metadata:
  name: nexus
  namespace: nexus
spec:
  serviceName: nexus
  environment: staging

  # Default image configuration
  image:
    registry: "605536185498.dkr.ecr.ap-south-1.amazonaws.com"
    name: nexus-backend-service
    tag: latest
    pullSecrets: ["ecr-registry-secret"]

  # Multiple services configuration
  services:
    # Main backend API service
    - name: server
      type: flask
      port: 8000
      command: ["./entrypoint.sh"]
      args: ["server"]
      workloadType: critical
      depends_on: ["database-migration"]  # Wait for migration
      healthCheck:
        unified:
          enabled: true
          path: "/health"
          initialDelaySeconds: 60
          periodSeconds: 30
      scaling:
        replicas: 2
        minReplicas: 2
        maxReplicas: 4
        targetCPU: 80
      resources:
        requests:
          cpu: 200m
          memory: 512Mi
        limits:
          cpu: 1000m
          memory: 2Gi

    # MCP service
    - name: mcp-service
      type: custom
      port: 8080
      workloadType: semi-critical
      depends_on: ["server"]  # Depends on main API
      image:
        name: nexus-mcp-server
        tag: latest
      command: ["uv", "run", "gunicorn"]
      args: ["treebo_mcp.asgi:asgi_application", "-c", "gunicorn.conf.py"]

    # Frontend service
    - name: frontend
      type: nodejs
      port: 80
      workloadType: critical
      depends_on: ["server"]  # Frontend needs backend API
      image:
        name: nexus-frontend-service
        tag: latest
      env:
        - name: NODE_ENV
          value: "production"

  # Workers configuration
  workers:
    - name: celery-worker
      type: celery
      enabled: true
      workloadType: semi-critical
      depends_on: ["server"]  # Worker needs main service
      command: ["./entrypoint.sh"]
      args: ["celery-worker"]
      healthCheck:
        type: exec
        exec:
          command: ["celery", "-A", "app.core.celery_app", "inspect", "ping"]
      scaling:
        type: hpa
        replicas: 2
        minReplicas: 1
        maxReplicas: 4

  # Tasks for migrations, jobs, and scheduled operations
  tasks:
    # Database migration task (runs first, blocks services)
    - name: database-migration
      category: migration
      type: job
      enabled: true
      workloadType: critical  # Migrations are always critical
      depends_on: []  # Migration runs first, no dependencies
      command: ["./entrypoint.sh", "migrate"]
      job:
        backoffLimit: 3
        ttlSecondsAfterFinished: 86400  # 24 hours
        restartPolicy: "Never"

  # External secrets configuration
  secrets:
    awsSecretPath: "tools/nexus"
    refreshInterval: "24h"
    secretStore: "aws-secrets-store"

  # Ingress configuration
  ingress:
    # Frontend ingress
    - name: frontend-ingress
      serviceName: frontend
      servicePort: 80
      host: nexus.ekscraving1775.treebo.com
      type: nginx
      paths:
        - path: "/"
          pathType: "Prefix"

    # Backend API ingress
    - name: server-ingress
      serviceName: server
      servicePort: 8000
      host: nexus.ekscraving1775.treebo.com
      type: nginx
      paths:
        - path: "/api"
          pathType: "Prefix"

  # Monitoring and alerting
  monitoring:
    enabled: true
    serviceMonitor:
      enabled: true
      interval: "30s"
      path: "/metrics"
    alerts:
      enabled: true
      rules:
        - name: NexusHighErrorRate
          expr: 'rate(http_requests_total{job="nexus-server",status=~"5.."}[5m]) > 0.1'
          severity: "warning"

  # Global environment variables
  env:
    - name: LOG_LEVEL
      value: "INFO"
    - name: ENVIRONMENT
      value: "tools"
```

### **🔧 Health Check Strategies**

#### **Unified Health Checks (Simple)**
```yaml
services:
  - name: api
    healthCheck:
      unified:
        enabled: true
        path: "/health"
        initialDelaySeconds: 30
        periodSeconds: 30
```

#### **Separate Health Checks (Production)**
```yaml
services:
  - name: api
    healthCheck:
      liveness:
        path: "/health/live"    # App alive, no dependencies
        periodSeconds: 10
      readiness:
        path: "/health/ready"   # Can serve traffic
        periodSeconds: 5
      startup:
        path: "/health/startup" # Initialization complete
        failureThreshold: 30
```

#### **Worker Health Checks**
```yaml
workers:
  - name: celery-worker
    healthCheck:
      type: exec
      exec:
        command: ["celery", "-A", "app", "inspect", "ping"]
      initialDelaySeconds: 60
      periodSeconds: 30
```

### **📈 Scaling Strategies**

#### **HPA (CPU/Memory)**
```yaml
services:
  - name: api
    scaling:
      replicas: 2
      minReplicas: 1
      maxReplicas: 10
      targetCPU: 80
      targetMemory: 85
```

#### **KEDA (Event-Driven)**
```yaml
workers:
  - name: kafka-consumer
    scaling:
      type: keda
      minReplicas: 0
      maxReplicas: 20
      keda:
        pollingInterval: 30
        cooldownPeriod: 300
        idleReplicaCount: 0
        triggers:
          - type: kafka
            metadata:
              bootstrapServers: "kafka:9092"
              consumerGroup: "my-group"
              topic: "events"
              lagThreshold: "10"
```

## 🛠️ Development Workflow

### **Local Development**
```bash
# Setup development environment
./scripts/dev-setup.sh

# Run tests and validation
cd controller-src
python test_models.py
uv run black --check *.py
uv run ruff check *.py

# Run operator locally (for testing)
python main.py
```

### **Docker Development**
```bash
# Build container image
cd controller-src
docker build -t treebo-service-operator .

# Run container locally
docker run -p 8080:8080 -p 8081:8081 treebo-service-operator
```

### **Kubernetes Development**
```bash
# Deploy to cluster
./scripts/build-and-deploy.sh

# Watch operator logs
kubectl logs -f deployment/treebo-service-operator -n treebo-service-operator

# Test with examples
kubectl apply -f examples/nexus-treeboservice.yaml
kubectl get treeboservice -A
```

## 📈 Migration Benefits

### **📊 Before vs After Comparison**

| **Aspect** | **Before** | **After** |
|------------|------------|-----------|
| **Configuration Files** | 15+ YAML files | 1 TreeboService resource |
| **WorkloadType** | Global (one-size-fits-all) | Individual (fine-grained) |
| **Dependencies** | Manual coordination | Automatic orchestration |
| **Validation** | Runtime errors | Compile-time validation |
| **Health Checks** | Manual configuration | Unified + separate modes |
| **Scaling** | Basic HPA | HPA + KEDA with triggers |
| **Tasks/Jobs** | Separate sections | Unified with categories |
| **Type Safety** | None | Full Pydantic v2 validation |
| **IDE Support** | None | Full autocomplete + validation |
| **Error Messages** | Cryptic K8s errors | Clear Pydantic messages |

### **🎯 Key Improvements**

#### **1. Unified Workload Management**
- **Individual WorkloadType**: Each component has its own criticality level
- **Dependency Orchestration**: `depends_on` field with circular detection
- **Type Safety**: Full Pydantic v2 validation with clear error messages
- **Resource Optimization**: Per-component resource allocation

#### **2. Tasks Consolidation**
- **Unified Interface**: Single `tasks` section for all job types
- **Built-in Categories**: 8 predefined categories for organization
- **Enhanced Functionality**: More configuration options than before
- **Migration Path**: Easy migration from old configurations

#### **3. Advanced Features**
- **Multi-Service Support**: Multiple web services in one resource
- **Health Check Flexibility**: Unified or separate endpoints
- **Comprehensive Scaling**: HPA and KEDA with various triggers
- **Intelligent Dependencies**: Automatic startup order management

### **🚀 Real-World Impact**

#### **Nexus Application Migration**
- **Before**: 15+ YAML files across multiple directories
- **After**: 1 TreeboService resource with complete configuration
- **Dependencies**: Migration → Server → Frontend/MCP/Worker
- **WorkloadTypes**: Critical (core services) + Semi-critical (workers/MCP)
- **Result**: 90% reduction in configuration complexity

#### **Operational Benefits**
- 🎯 **Precise Resource Allocation**: Each component gets appropriate resources
- 🔄 **Controlled Startup Order**: Dependencies ensure proper initialization
- 💰 **Cost Optimization**: Non-critical workloads use cheaper resources
- 🛡️ **Improved Reliability**: Proper dependency management prevents failures

#### **Developer Benefits**
- 🔍 **Clear Dependencies**: Explicit dependency declarations
- ✅ **Validation**: Catch configuration errors early
- 📝 **Self-Documenting**: Dependencies show service relationships
- 🔧 **Flexible Configuration**: Fine-grained control per component

## 🎉 Conclusion

The TreeboService operator represents a **significant advancement** in Treebo's infrastructure automation:

### **🎯 Achievements**
- ✅ **90% Reduction**: From 15+ YAML files to 1 TreeboService resource
- ✅ **Type Safety**: Compile-time validation instead of runtime errors
- ✅ **Dependency Management**: Automatic orchestration with circular detection
- ✅ **Fine-Grained Control**: Per-component workload classification
- ✅ **Modern Stack**: uv, Pydantic v2, structlog, kopf with full type annotations
- ✅ **Production Ready**: Real-world validation with Nexus application

### **🚀 Future Roadmap**
- ⏳ **KEDA Integration**: Event-driven autoscaling for workers
- ⏳ **Service Mesh**: Istio/Linkerd integration
- ⏳ **GitOps Integration**: ArgoCD/Flux compatibility
- ⏳ **Cost Optimization**: Resource recommendations and right-sizing

### **💡 Key Takeaways**
The TreeboService operator transforms microservice management from **complex, error-prone manual processes** to **simple, type-safe, automated workflows**. By consolidating configuration, adding intelligent dependency management, and providing fine-grained workload control, it enables teams to focus on **business logic** rather than **infrastructure complexity**.

**The future of microservice deployment is here** - unified, intelligent, and production-ready! 🎉
