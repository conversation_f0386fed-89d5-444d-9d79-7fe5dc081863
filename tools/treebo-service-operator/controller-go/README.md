# TreeboService Operator - Simple Go Implementation

A minimal Kubernetes operator for managing TreeboService custom resources. This is a simplified, flat-file structure implementation with just 4 Go files.

## Files

- **go.mod** - Go module dependencies
- **types.go** - All CRD types and models in one file
- **controller.go** - Controller reconciliation logic
- **main.go** - Entry point and manager setup

## Quick Start

### **🚀 Production Deployment (Recommended)**

The controller automatically generates and applies CRDs when deployed:

1. **Build Docker image** (CRDs generated during build):
   ```bash
   make docker-build
   ```

2. **Deploy to Kubernetes** (CRDs applied automatically on startup):
   ```bash
   kubectl apply -f ../base/
   ```

3. **Create a TreeboService**:
   ```bash
   kubectl apply -f example-treeboservice.yaml
   ```

### **🛠️ Local Development**

1. **Setup local development** (generates and applies CRDs):
   ```bash
   make dev-setup
   ```

2. **Run controller locally**:
   ```bash
   make run
   ```

3. **Create a TreeboService**:
   ```bash
   kubectl apply -f example-treeboservice.yaml
   ```

## Building

```bash
# Build binary
make build

# Build Docker image
make docker-build IMG=your-registry/treebo-service-operator:latest

# Push Docker image
make docker-push IMG=your-registry/treebo-service-operator:latest
```

## Features

- **Services**: HTTP services with deployments and services
- **Workers**: Background worker deployments
- **Tasks**: Job and CronJob support (basic)
- **Ingress**: NGINX/ALB ingress creation
- **Health Checks**: HTTP/TCP health checks
- **Resource Management**: CPU/Memory requests and limits
- **Environment Variables**: Global and workload-specific env vars

## Example TreeboService

```yaml
apiVersion: treebo.com/v1
kind: TreeboService
metadata:
  name: my-app
spec:
  serviceName: my-app
  environment: staging
  services:
    - name: api
      type: python
      port: 8000
      workloadType: critical
  workers:
    - name: worker
      type: python
      workloadType: semi-critical
  ingress:
    - host: my-app.staging.example.com
      serviceName: my-app-api
      servicePort: 8000
```

## Architecture

This simplified implementation:
- Uses a flat directory structure with just 4 Go files
- Consolidates all types in `types.go`
- Implements basic reconciliation in `controller.go`
- Provides a minimal but functional operator

## Differences from Complex Kubebuilder Structure

- **No nested directories**: Everything in one folder
- **Single types file**: All models consolidated
- **Simplified build**: Basic Makefile and Dockerfile
- **Manual CRD**: Hand-written CRD YAML instead of generated
- **Minimal dependencies**: Only essential controller-runtime imports

## Development

```bash
# Format code
make fmt

# Vet code
make vet

# Run tests
make test

# Clean build artifacts
make clean
```

This implementation provides the same functionality as the complex Kubebuilder structure but with a much simpler, more maintainable codebase.

## 🔄 Automated CRD Management

This controller uses **build-time CRD generation + runtime application** for zero-maintenance CRD management:

### **How It Works**

1. **Build Time**: Docker build generates CRDs from Go types using `controller-gen`
2. **Runtime**: Controller automatically applies CRDs to cluster on startup
3. **Always In Sync**: CRDs are generated from the same Go code being deployed

### **Benefits**

- ✅ **Zero maintenance** - No manual CRD generation steps
- ✅ **Always in sync** - CRDs match the deployed controller version
- ✅ **Developer friendly** - Just change Go types and deploy
- ✅ **GitOps compatible** - No generated files to commit

### **Manual CRD Generation (Development)**

```bash
# Generate CRDs locally for development
make crds

# Apply CRDs for local development
make dev-setup

# Clean generated CRDs
make clean-crds
```

### **Kubebuilder Annotations**

The types.go file includes full kubebuilder annotations:
- `+kubebuilder:validation:Required` - Required fields
- `+kubebuilder:validation:Enum` - Enum validation
- `+kubebuilder:validation:Minimum/Maximum` - Numeric validation
- `+kubebuilder:default` - Default values
- `+kubebuilder:printcolumn` - Additional printer columns
