// Package main contains the TreeboService API definitions
// +kubebuilder:object:generate=true
// +groupName=treebo.com
package main

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

// Enums
type WorkloadType string

const (
	WorkloadTypeCritical    WorkloadType = "critical"
	WorkloadTypeSemiCritical WorkloadType = "semi-critical"
	WorkloadTypeSubCritical WorkloadType = "sub-critical"
	WorkloadTypeNonCritical WorkloadType = "non-critical"
)

type EnvironmentType string

const (
	EnvironmentTypeStaging    EnvironmentType = "staging"
	EnvironmentTypeProduction EnvironmentType = "production"
)

type TaskType string

const (
	TaskTypeJob     TaskType = "job"
	TaskTypeCronJob TaskType = "cronjob"
)

// Basic types
type ImageConfig struct {
	Registry    *string  `json:"registry,omitempty"`
	Name        string   `json:"name"`
	Tag         string   `json:"tag"`
	PullPolicy  string   `json:"pullPolicy,omitempty"`
	PullSecrets []string `json:"pullSecrets,omitempty"`
}

type EnvVar struct {
	Name      string  `json:"name"`
	Value     *string `json:"value,omitempty"`
	// ValueFrom defines a source for the environment variable's value
	// +optional
	// +kubebuilder:pruning:PreserveUnknownFields
	// ValueFrom *map[string]string `json:"valueFrom,omitempty"`
}

type ResourceEntitlements struct {
	CPU    string `json:"cpu,omitempty"`
	Memory string `json:"memory,omitempty"`
}

type ResourceThresholds struct {
	Requests *ResourceEntitlements `json:"requests,omitempty"`
	Limits   *ResourceEntitlements `json:"limits,omitempty"`
}

type HealthCheck struct {
	Type                string `json:"type,omitempty"`
	InitialDelaySeconds int32  `json:"initialDelaySeconds,omitempty"`
	PeriodSeconds       int32  `json:"periodSeconds,omitempty"`
	TimeoutSeconds      int32  `json:"timeoutSeconds,omitempty"`
	FailureThreshold    int32  `json:"failureThreshold,omitempty"`
	Path                string `json:"path,omitempty"`
	Port                int32  `json:"port,omitempty"`
}

type ScalingConfig struct {
	Replicas    int32 `json:"replicas,omitempty"`
	MinReplicas int32 `json:"minReplicas,omitempty"`
	MaxReplicas int32 `json:"maxReplicas,omitempty"`
	TargetCPU   int32 `json:"targetCPU,omitempty"`
	TargetMemory int32 `json:"targetMemory,omitempty"`
}

type JobConfig struct {
	BackoffLimit            int32  `json:"backoffLimit,omitempty"`
	ActiveDeadlineSeconds   *int32 `json:"activeDeadlineSeconds,omitempty"`
	TTLSecondsAfterFinished int32  `json:"ttlSecondsAfterFinished,omitempty"`
	RestartPolicy           string `json:"restartPolicy,omitempty"`
}

type CronJobConfig struct {
	Schedule                   string `json:"schedule"`
	TimeZone                   string `json:"timeZone,omitempty"`
	ConcurrencyPolicy          string `json:"concurrencyPolicy,omitempty"`
	SuccessfulJobsHistoryLimit int32  `json:"successfulJobsHistoryLimit,omitempty"`
	FailedJobsHistoryLimit     int32  `json:"failedJobsHistoryLimit,omitempty"`
	StartingDeadlineSeconds    *int32 `json:"startingDeadlineSeconds,omitempty"`
}

type SecretsConfig struct {
	Name            string `json:"name"`                    // Name for this secret config (used in resource names)
	SecretName      string `json:"secretName,omitempty"`    // Target Kubernetes secret name (optional)
	AWSSecretPath   string `json:"awsSecretPath"`           // AWS secret path
	RefreshInterval string `json:"refreshInterval,omitempty"` // Refresh interval
	SecretStore     string `json:"secretStore,omitempty"`   // SecretStore reference
}

type IngressPath struct {
	Path     string `json:"path,omitempty"`
	PathType string `json:"pathType,omitempty"`
}

type IngressSpec struct {
	Name        *string               `json:"name,omitempty"`
	Enabled     bool                  `json:"enabled,omitempty"`
	Type        string                `json:"type,omitempty"`
	Host        string                `json:"host"`
	ServiceName string                `json:"serviceName"`
	ServicePort int32                 `json:"servicePort"`
	Paths       []IngressPath         `json:"paths,omitempty"`
	Annotations map[string]string     `json:"annotations,omitempty"`
	TLS         bool                  `json:"tls,omitempty"`
}

type MonitoringConfig struct {
	Enabled bool `json:"enabled,omitempty"`
}

// Workload defines the base configuration for all workload types
type Workload struct {
	// Name of the workload (must be unique within type)
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:Pattern="^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"
	Name string `json:"name"`

	// Enabled indicates if the workload is enabled
	// +kubebuilder:default=true
	// +optional
	Enabled bool `json:"enabled,omitempty"`

	// WorkloadType defines the criticality classification
	// +kubebuilder:default="critical"
	// +kubebuilder:validation:Enum=critical;semi-critical;sub-critical;non-critical
	// +optional
	WorkloadType WorkloadType `json:"workloadType,omitempty"`

	// DependsOn lists workload names this depends on
	// +optional
	DependsOn []string `json:"dependsOn,omitempty"`

	// Image configuration
	// +optional
	Image *ImageConfig `json:"image,omitempty"`

	// Command to run in the container
	// +optional
	Command []string `json:"command,omitempty"`

	// Args for the container command
	// +optional
	Args []string `json:"args,omitempty"`

	// Env defines environment variables
	// +optional
	Env []EnvVar `json:"env,omitempty"`

	// Resources defines resource requirements
	// +optional
	Resources *ResourceThresholds `json:"resources,omitempty"`
}

// ServiceSpec defines a service workload
type ServiceSpec struct {
	Workload `json:",inline"`

	// Type of service
	// +kubebuilder:validation:Required
	Type string `json:"type"`

	// Port for the service
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:validation:Maximum=65535
	// +kubebuilder:validation:Required
	Port int32 `json:"port"`

	// HealthCheck configuration
	// +optional
	HealthCheck *HealthCheck `json:"healthCheck,omitempty"`

	// Scaling configuration
	// +optional
	Scaling *ScalingConfig `json:"scaling,omitempty"`
}

type WorkerSpec struct {
	Workload    `json:",inline"`
	Type        string         `json:"type"`
	HealthCheck *HealthCheck   `json:"healthCheck,omitempty"`
	Scaling     *ScalingConfig `json:"scaling,omitempty"`
}

type TaskSpec struct {
	Workload      `json:",inline"`
	Type          TaskType       `json:"type,omitempty"`
	JobConfig     *JobConfig     `json:"jobConfig,omitempty"`
	CronJobConfig *CronJobConfig `json:"cronJobConfig,omitempty"`
}

// TreeboServiceSpec defines the desired state of TreeboService
type TreeboServiceSpec struct {
	// ServiceName is the name of the Treebo service
	// +kubebuilder:validation:Required
	ServiceName string `json:"serviceName"`

	// Environment is the deployment environment
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:Enum=staging;production
	Environment EnvironmentType `json:"environment"`

	// Image is the default image configuration
	// +optional
	Image *ImageConfig `json:"image,omitempty"`

	// Services defines service workloads
	// +kubebuilder:validation:MinItems=1
	// +kubebuilder:validation:Required
	Services []ServiceSpec `json:"services"`

	// Workers defines worker workloads
	// +optional
	Workers []WorkerSpec `json:"workers,omitempty"`

	// Tasks defines task workloads for migrations, jobs, and scheduled operations
	// +optional
	Tasks []TaskSpec `json:"tasks,omitempty"`

	// Secrets defines external secrets configuration
	// +optional
	Secrets []SecretsConfig `json:"secrets,omitempty"`

	// Env defines global environment variables
	// +optional
	Env []EnvVar `json:"env,omitempty"`

	// Ingress defines ingress configurations
	// +optional
	Ingress []IngressSpec `json:"ingress,omitempty"`

	// Monitoring defines monitoring and alerting configuration
	// +optional
	Monitoring *MonitoringConfig `json:"monitoring,omitempty"`
}

type TreeboServiceStatus struct {
	Conditions         []metav1.Condition `json:"conditions,omitempty"`
	Phase              string             `json:"phase,omitempty"`
	ObservedGeneration int64              `json:"observedGeneration,omitempty"`
	Services           map[string]string  `json:"services,omitempty"`
	Workers            map[string]string  `json:"workers,omitempty"`
	Tasks              map[string]string  `json:"tasks,omitempty"`
}

// TreeboService is the Schema for the treeboservices API
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Namespaced,shortName=tbs
// +kubebuilder:printcolumn:name="Service Name",type=string,JSONPath=`.spec.serviceName`
// +kubebuilder:printcolumn:name="Environment",type=string,JSONPath=`.spec.environment`
// +kubebuilder:printcolumn:name="Phase",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="Age",type=date,JSONPath=`.metadata.creationTimestamp`
type TreeboService struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              TreeboServiceSpec   `json:"spec,omitempty"`
	Status            TreeboServiceStatus `json:"status,omitempty"`
}

// TreeboServiceList contains a list of TreeboService
// +kubebuilder:object:root=true
type TreeboServiceList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []TreeboService `json:"items"`
}

// Implement runtime.Object interface
func (in *TreeboService) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

func (in *TreeboServiceList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopy methods (simplified)
func (in *TreeboService) DeepCopy() *TreeboService {
	if in == nil {
		return nil
	}
	out := new(TreeboService)
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return out
}

func (in *TreeboServiceList) DeepCopy() *TreeboServiceList {
	if in == nil {
		return nil
	}
	out := new(TreeboServiceList)
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]TreeboService, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return out
}

func (in *TreeboServiceSpec) DeepCopyInto(out *TreeboServiceSpec) {
	*out = *in
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(ImageConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make([]ServiceSpec, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	// Add other fields as needed...
}

func (in *TreeboServiceStatus) DeepCopyInto(out *TreeboServiceStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	// Add other fields as needed...
}

func (in *TreeboService) DeepCopyInto(out *TreeboService) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// Add simplified DeepCopyInto methods for other types
func (in *ImageConfig) DeepCopyInto(out *ImageConfig) {
	*out = *in
	if in.Registry != nil {
		in, out := &in.Registry, &out.Registry
		*out = new(string)
		**out = **in
	}
	if in.PullSecrets != nil {
		in, out := &in.PullSecrets, &out.PullSecrets
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

func (in *ServiceSpec) DeepCopyInto(out *ServiceSpec) {
	*out = *in
	in.Workload.DeepCopyInto(&out.Workload)
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		**out = **in
	}
	if in.Scaling != nil {
		in, out := &in.Scaling, &out.Scaling
		*out = new(ScalingConfig)
		**out = **in
	}
}

func (in *Workload) DeepCopyInto(out *Workload) {
	*out = *in
	if in.DependsOn != nil {
		in, out := &in.DependsOn, &out.DependsOn
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(ImageConfig)
		(*in).DeepCopyInto(*out)
	}
	// Add other fields as needed...
}

// GroupVersionKind
var (
	// GroupVersion is group version used to register these objects
	GroupVersion = schema.GroupVersion{Group: "treebo.com", Version: "v1"}

	// SchemeBuilder is used to add go types to the GroupVersionKind scheme
	SchemeBuilder = runtime.NewSchemeBuilder(addKnownTypes)

	// AddToScheme adds the types in this group-version to the given scheme.
	AddToScheme = SchemeBuilder.AddToScheme
)

// addKnownTypes adds the known types to the scheme
func addKnownTypes(scheme *runtime.Scheme) error {
	scheme.AddKnownTypes(GroupVersion,
		&TreeboService{},
		&TreeboServiceList{},
	)
	metav1.AddToGroupVersion(scheme, GroupVersion)
	return nil
}

func init() {
	// The SchemeBuilder is already initialized with addKnownTypes
}
