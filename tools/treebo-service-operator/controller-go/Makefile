# Simple Makefile for TreeboService Operator

# Image URL to use all building/pushing image targets
IMG ?= treebo-service-operator:latest

# Tool Binaries
LOCALBIN ?= $(shell pwd)/bin
CONTROLLER_GEN ?= $(LOCALBIN)/controller-gen

# Tool Versions
CONTROLLER_TOOLS_VERSION ?= v0.16.5

# Build the manager binary
.PHONY: build
build:
	go build -o dist/tso main.go types.go controller.go

# Generate manifests e.g. CRD, RBAC etc.
.PHONY: crds
crds: controller-gen
	$(CONTROLLER_GEN) crd paths="./..." output:crd:dir=./crds

# Generate code containing DeepCopy, DeepCopyInto, and DeepCopyObject method implementations.
.PHONY: generate
generate: controller-gen
	$(CONTROLLER_GEN) object:headerFile="hack/boilerplate.go.txt" paths="./..."

# Run the controller locally
.PHONY: run
run:
	go run main.go types.go controller.go

# Build and push docker image
.PHONY: docker-build
docker-build:
	docker build -t ${IMG} .

.PHONY: docker-push
docker-push:
	docker push ${IMG}

# Run tests
.PHONY: test
test:
	go test ./...

# Run go fmt against code
.PHONY: fmt
fmt:
	go fmt ./...

# Run go vet against code
.PHONY: vet
vet:
	go vet ./...

# Clean build artifacts
.PHONY: clean
clean:
	rm -rf bin/

# Install dependencies
.PHONY: deps
deps:
	go mod tidy
	go mod download

# Clean generated CRDs (for local development)
.PHONY: clean-crds
clean-crds:
	rm -rf crds/

# Local development setup - apply CRDs manually
.PHONY: dev-setup
dev-setup: crds
	@echo "Applying CRDs for local development..."
	kubectl apply -f crds/treebo.com_treeboservices.yaml
	@echo "CRDs applied. You can now run 'make run' to start the controller locally."

## Location to install dependencies to
$(LOCALBIN):
	mkdir -p $(LOCALBIN)

.PHONY: controller-gen
controller-gen: $(CONTROLLER_GEN) ## Download controller-gen locally if necessary.
$(CONTROLLER_GEN): $(LOCALBIN)
	test -s $(LOCALBIN)/controller-gen && $(LOCALBIN)/controller-gen --version | grep -q $(CONTROLLER_TOOLS_VERSION) || \
	GOBIN=$(LOCALBIN) go install sigs.k8s.io/controller-tools/cmd/controller-gen@$(CONTROLLER_TOOLS_VERSION)

.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build       - Build the manager binary"
	@echo "  run         - Run the controller locally"
	@echo "  crds        - Generate CRD manifests"
	@echo "  generate    - Generate code (DeepCopy methods)"
	@echo "  dev-setup   - Apply CRDs for local development"
	@echo "  clean-crds  - Remove generated CRDs"
	@echo "  docker-build - Build docker image"
	@echo "  docker-push - Push docker image"
	@echo "  test        - Run tests"
	@echo "  fmt         - Run go fmt"
	@echo "  vet         - Run go vet"
	@echo "  clean       - Clean build artifacts"
	@echo "  deps        - Install dependencies"
