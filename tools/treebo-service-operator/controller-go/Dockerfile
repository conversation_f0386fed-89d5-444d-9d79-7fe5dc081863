# Build the manager binary
FROM golang:1.24 as builder

WORKDIR /workspace

COPY go.mod go.sum /workspace/
RUN go mod download

COPY . /workspace/

# Install controller-gen for CRD generation
RUN go install sigs.k8s.io/controller-tools/cmd/controller-gen@v0.16.5

# Generate CRDs from Go types
RUN controller-gen crd paths="./..." output:crd:dir=./crds

# Build the manager binary
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o tso

FROM alpine:3.18
WORKDIR /
COPY --from=builder /workspace/tso .
COPY --from=builder /workspace/crds /crds

ENTRYPOINT ["/tso"]
