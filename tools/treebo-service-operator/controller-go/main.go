package main

import (
	"context"
	"flag"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/apimachinery/pkg/types"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics/server"
)

var (
	runtimeScheme = runtime.NewScheme()
	setupLog      = ctrl.Log.WithName("setup")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(runtimeScheme))
	utilruntime.Must(AddToScheme(runtimeScheme))
	// Add apiextensions scheme for CRD management
	utilruntime.Must(apiextensionsv1.AddToScheme(runtimeScheme))
}

func main() {
	var metricsAddr string
	var enableLeaderElection bool
	var probeAddr string

	flag.StringVar(&metricsAddr, "metrics-bind-address", ":8080", "The address the metric endpoint binds to.")
	flag.StringVar(&probeAddr, "health-probe-bind-address", ":8081", "The address the probe endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "leader-elect", false,
		"Enable leader election for controller manager. "+
			"Enabling this will ensure there is only one active controller manager.")

	opts := zap.Options{
		Development: true,
	}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme: runtimeScheme,
		Metrics: metricsserver.Options{
			BindAddress: metricsAddr,
		},
		HealthProbeBindAddress: probeAddr,
		LeaderElection:         enableLeaderElection,
		LeaderElectionID:       "treebo-service-operator.treebo.com",
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	// Apply CRDs before starting the controller using direct client
	setupLog.Info("applying CRDs")
	// if err := applyCRDs(ctrl.GetConfigOrDie()); err != nil {
	// 	setupLog.Error(err, "failed to apply CRDs")
	// 	os.Exit(1)
	// }
	setupLog.Info("CRDs applied successfully")

	// Debug: Check if TreeboService is registered in the scheme
	gvk := GroupVersion.WithKind("TreeboService")
	setupLog.Info("Checking scheme registration", "gvk", gvk)

	// List all known types in the scheme for debugging
	allKnownTypes := mgr.GetScheme().AllKnownTypes()
	setupLog.Info("Total known types in scheme", "count", len(allKnownTypes))

	// Check specifically for our GVK
	if _, err := mgr.GetScheme().New(gvk); err != nil {
		setupLog.Error(err, "TreeboService not properly registered in scheme", "gvk", gvk)

		// Debug: Show some known types
		for registeredGVK := range allKnownTypes {
			if registeredGVK.Group == "treebo.com" {
				setupLog.Info("Found treebo.com type", "gvk", registeredGVK)
			}
		}
		os.Exit(1)
	}
	setupLog.Info("TreeboService successfully registered in scheme", "gvk", gvk)

	if err = buildReconciler(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "TreeboService")
		os.Exit(1)
	}

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}

func buildReconciler(mgr ctrl.Manager) error {
		externalSecretGVK := schema.GroupVersionKind{
		Group:   "external-secrets.io",
		Version: "v1beta1",
		Kind:    "ExternalSecret",
	}
	externalSecretObj := &unstructured.Unstructured{}
	externalSecretObj.SetGroupVersionKind(externalSecretGVK)

	return ctrl.NewControllerManagedBy(mgr).
			For(&TreeboService{}).
			Owns(&appsv1.Deployment{}).
			Owns(&corev1.Service{}).
			Owns(&networkingv1.Ingress{}).
			Owns(externalSecretObj).
			Complete(&TreeboServiceReconciler{
		Client: mgr.GetClient(),
		Scheme: mgr.GetScheme(),
	})
}

// applyCRDs reads CRD files from /crds directory and applies them to the cluster
func applyCRDs(config *rest.Config) error {
	// Create a direct client (not cached) for CRD operations
	directClient, err := client.New(config, client.Options{
		Scheme: runtimeScheme,
	})
	if err != nil {
		return fmt.Errorf("failed to create direct client: %w", err)
	}
	crdsDir := "/crds"

	// Check if running in container (CRDs in /crds) or local dev (CRDs in ./crds)
	if _, err := os.Stat(crdsDir); os.IsNotExist(err) {
		crdsDir = "./crds"
	}

	return filepath.WalkDir(crdsDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// Only process YAML files
		if d.IsDir() || filepath.Ext(path) != ".yaml" {
			return nil
		}

		setupLog.Info("applying CRD", "file", path)

		// Read the CRD file
		data, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("failed to read CRD file %s: %w", path, err)
		}

		// Decode the YAML into a CRD object
		decoder := serializer.NewCodecFactory(runtimeScheme).UniversalDeserializer()
		obj, _, err := decoder.Decode(data, nil, nil)
		if err != nil {
			return fmt.Errorf("failed to decode CRD file %s: %w", path, err)
		}

		crd, ok := obj.(*apiextensionsv1.CustomResourceDefinition)
		if !ok {
			return fmt.Errorf("file %s does not contain a CRD", path)
		}

		// Apply the CRD to the cluster with retry
		ctx := context.Background()

		// Retry logic for CRD operations
		maxRetries := 3
		for attempt := 1; attempt <= maxRetries; attempt++ {
			existing := &apiextensionsv1.CustomResourceDefinition{}
			err = directClient.Get(ctx, types.NamespacedName{Name: crd.Name}, existing)

			if err != nil {
				if errors.IsNotFound(err) {
					// Create new CRD
					setupLog.Info("creating CRD", "name", crd.Name, "attempt", attempt)
					if err := directClient.Create(ctx, crd); err != nil {
						if attempt == maxRetries {
							return fmt.Errorf("failed to create CRD %s after %d attempts: %w", crd.Name, maxRetries, err)
						}
						setupLog.Info("retrying CRD creation", "name", crd.Name, "attempt", attempt, "error", err.Error())
						time.Sleep(time.Second * time.Duration(attempt))
						continue
					}
					break
				} else {
					if attempt == maxRetries {
						return fmt.Errorf("failed to get existing CRD %s after %d attempts: %w", crd.Name, maxRetries, err)
					}
					setupLog.Info("retrying CRD get", "name", crd.Name, "attempt", attempt, "error", err.Error())
					time.Sleep(time.Second * time.Duration(attempt))
					continue
				}
			} else {
				// Update existing CRD
				setupLog.Info("updating CRD", "name", crd.Name, "attempt", attempt)
				crd.ResourceVersion = existing.ResourceVersion
				if err := directClient.Update(ctx, crd); err != nil {
					if attempt == maxRetries {
						return fmt.Errorf("failed to update CRD %s after %d attempts: %w", crd.Name, maxRetries, err)
					}
					setupLog.Info("retrying CRD update", "name", crd.Name, "attempt", attempt, "error", err.Error())
					time.Sleep(time.Second * time.Duration(attempt))
					continue
				}
				break
			}
		}

		return nil
	})
}
