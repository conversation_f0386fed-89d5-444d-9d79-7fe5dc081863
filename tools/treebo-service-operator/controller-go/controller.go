package main

import (
	"context"
	"fmt"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

// TreeboServiceReconciler reconciles a TreeboService object
type TreeboServiceReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// Reconcile handles TreeboService reconciliation
func (r *TreeboServiceReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the TreeboService instance
	var treeboService TreeboService
	if err := r.Get(ctx, req.NamespacedName, &treeboService); err != nil {
		if errors.IsNotFound(err) {
			logger.Info("TreeboService resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get TreeboService")
		return ctrl.Result{}, err
	}

	logger.Info("Reconciling TreeboService", "name", treeboService.Name, "namespace", treeboService.Namespace, "generation", treeboService.Generation, "observedGeneration", treeboService.Status.ObservedGeneration)

	// Skip reconciliation if already processed this generation and status is Ready
	if treeboService.Status.ObservedGeneration == treeboService.Generation && treeboService.Status.Phase == "Ready" {
		logger.Info("TreeboService already reconciled for this generation", "generation", treeboService.Generation)
		return ctrl.Result{RequeueAfter: time.Minute * 10}, nil
	}

	// Update status to indicate reconciliation is in progress
	if err := r.updateStatus(ctx, &treeboService, "Reconciling", "Starting reconciliation"); err != nil {
		logger.Error(err, "Failed to update TreeboService status to Reconciling")
		return ctrl.Result{}, err
	}

	// Reconcile services
	if err := r.reconcileServices(ctx, &treeboService); err != nil {
		logger.Error(err, "Failed to reconcile services")
		// Update status to indicate failure
		if statusErr := r.updateStatus(ctx, &treeboService, "Failed", fmt.Sprintf("Failed to reconcile services: %v", err)); statusErr != nil {
			logger.Error(statusErr, "Failed to update status after services reconciliation failure")
		}
		return ctrl.Result{}, err
	}

	// Reconcile workers
	if err := r.reconcileWorkers(ctx, &treeboService); err != nil {
		logger.Error(err, "Failed to reconcile workers")
		// Update status to indicate failure
		if statusErr := r.updateStatus(ctx, &treeboService, "Failed", fmt.Sprintf("Failed to reconcile workers: %v", err)); statusErr != nil {
			logger.Error(statusErr, "Failed to update status after workers reconciliation failure")
		}
		return ctrl.Result{}, err
	}

	// Reconcile external secrets
	if err := r.reconcileExternalSecrets(ctx, &treeboService); err != nil {
		logger.Error(err, "Failed to reconcile external secrets")
		// Update status to indicate failure
		if statusErr := r.updateStatus(ctx, &treeboService, "Failed", fmt.Sprintf("Failed to reconcile external secrets: %v", err)); statusErr != nil {
			logger.Error(statusErr, "Failed to update status after external secrets reconciliation failure")
		}
		return ctrl.Result{}, err
	}

	// Reconcile ingresses
	if err := r.reconcileIngresses(ctx, &treeboService); err != nil {
		logger.Error(err, "Failed to reconcile ingresses")
		// Update status to indicate failure
		if statusErr := r.updateStatus(ctx, &treeboService, "Failed", fmt.Sprintf("Failed to reconcile ingresses: %v", err)); statusErr != nil {
			logger.Error(statusErr, "Failed to update status after ingresses reconciliation failure")
		}
		return ctrl.Result{}, err
	}

	// Update status to indicate successful reconciliation
	if err := r.updateStatus(ctx, &treeboService, "Ready", "All components reconciled successfully"); err != nil {
		logger.Error(err, "Failed to update TreeboService status to Ready")
		return ctrl.Result{}, err
	}

	logger.Info("Successfully reconciled TreeboService")
	// Longer requeue time to reduce unnecessary reconciliation
	return ctrl.Result{RequeueAfter: time.Minute * 15}, nil
}

// reconcileServices handles the reconciliation of service workloads
func (r *TreeboServiceReconciler) reconcileServices(ctx context.Context, treeboService *TreeboService) error {
	logger := log.FromContext(ctx)

	for _, serviceSpec := range treeboService.Spec.Services {
		if !serviceSpec.Enabled {
			continue
		}

		logger.Info("Reconciling service", "service", serviceSpec.Name)

		// Create deployment for service
		deployment := r.createDeploymentForService(treeboService, &serviceSpec)
		if err := controllerutil.SetControllerReference(treeboService, deployment, r.Scheme); err != nil {
			return err
		}

		found := &appsv1.Deployment{}
		err := r.Get(ctx, types.NamespacedName{Name: deployment.Name, Namespace: deployment.Namespace}, found)
		if err != nil && errors.IsNotFound(err) {
			logger.Info("Creating new Deployment", "Deployment.Namespace", deployment.Namespace, "Deployment.Name", deployment.Name)
			if err := r.Create(ctx, deployment); err != nil {
				return err
			}
		} else if err != nil {
			return err
		} else {
			// Update existing deployment
			logger.Info("Updating existing Deployment", "Deployment.Namespace", deployment.Namespace, "Deployment.Name", deployment.Name)
			if err := r.Update(ctx, deployment); err != nil {
				return err
			}
		}

		// Create service for the deployment
		service := r.createServiceForService(treeboService, &serviceSpec)
		if err := controllerutil.SetControllerReference(treeboService, service, r.Scheme); err != nil {
			return err
		}

		foundService := &corev1.Service{}
		err = r.Get(ctx, types.NamespacedName{Name: service.Name, Namespace: service.Namespace}, foundService)
		if err != nil && errors.IsNotFound(err) {
			logger.Info("Creating new Service", "Service.Namespace", service.Namespace, "Service.Name", service.Name)
			if err := r.Create(ctx, service); err != nil {
				return err
			}
		} else if err != nil {
			return err
		}
	}

	return nil
}

// reconcileWorkers handles the reconciliation of worker workloads
func (r *TreeboServiceReconciler) reconcileWorkers(ctx context.Context, treeboService *TreeboService) error {
	logger := log.FromContext(ctx)

	for _, workerSpec := range treeboService.Spec.Workers {
		if !workerSpec.Enabled {
			continue
		}

		logger.Info("Reconciling worker", "worker", workerSpec.Name)

		// Create deployment for worker
		deployment := r.createDeploymentForWorker(treeboService, &workerSpec)
		if err := controllerutil.SetControllerReference(treeboService, deployment, r.Scheme); err != nil {
			return err
		}

		found := &appsv1.Deployment{}
		err := r.Get(ctx, types.NamespacedName{Name: deployment.Name, Namespace: deployment.Namespace}, found)
		if err != nil && errors.IsNotFound(err) {
			logger.Info("Creating new Worker Deployment", "Deployment.Namespace", deployment.Namespace, "Deployment.Name", deployment.Name)
			if err := r.Create(ctx, deployment); err != nil {
				return err
			}
		} else if err != nil {
			return err
		} else {
			// Update existing deployment
			logger.Info("Updating existing Worker Deployment", "Deployment.Namespace", deployment.Namespace, "Deployment.Name", deployment.Name)
			if err := r.Update(ctx, deployment); err != nil {
				return err
			}
		}
	}

	return nil
}

// reconcileIngresses handles the reconciliation of ingress resources
func (r *TreeboServiceReconciler) reconcileIngresses(ctx context.Context, treeboService *TreeboService) error {
	logger := log.FromContext(ctx)

	for _, ingressSpec := range treeboService.Spec.Ingress {
		if !ingressSpec.Enabled {
			continue
		}

		logger.Info("Reconciling ingress", "host", ingressSpec.Host)

		// Create ingress
		ingress := r.createIngress(treeboService, &ingressSpec)
		if err := controllerutil.SetControllerReference(treeboService, ingress, r.Scheme); err != nil {
			return err
		}

		found := &networkingv1.Ingress{}
		err := r.Get(ctx, types.NamespacedName{Name: ingress.Name, Namespace: ingress.Namespace}, found)
		if err != nil && errors.IsNotFound(err) {
			logger.Info("Creating new Ingress", "Ingress.Namespace", ingress.Namespace, "Ingress.Name", ingress.Name)
			if err := r.Create(ctx, ingress); err != nil {
				return err
			}
		} else if err != nil {
			return err
		}
	}

	return nil
}

// reconcileExternalSecrets handles the reconciliation of external secret resources
func (r *TreeboServiceReconciler) reconcileExternalSecrets(ctx context.Context, treeboService *TreeboService) error {
	logger := log.FromContext(ctx)

	// Only create external secrets if secrets configuration is provided
	if len(treeboService.Spec.Secrets) == 0 {
		logger.Info("No secrets configuration found, skipping external secrets reconciliation")
		return nil
	}

	logger.Info("Reconciling external secrets", "secretCount", len(treeboService.Spec.Secrets))

	// Create ExternalSecret resource for each secret configuration
	for _, secretConfig := range treeboService.Spec.Secrets {
		logger.Info("Processing secret config", "name", secretConfig.Name, "secretStore", secretConfig.SecretStore, "awsSecretPath", secretConfig.AWSSecretPath)

		externalSecret := r.createExternalSecret(treeboService, secretConfig)
		if err := controllerutil.SetControllerReference(treeboService, externalSecret, r.Scheme); err != nil {
			return fmt.Errorf("failed to set controller reference for secret %s: %w", secretConfig.Name, err)
		}

		found := &unstructured.Unstructured{}
		found.SetGroupVersionKind(externalSecret.GroupVersionKind())
		err := r.Get(ctx, types.NamespacedName{Name: externalSecret.GetName(), Namespace: externalSecret.GetNamespace()}, found)
		if err != nil && errors.IsNotFound(err) {
			logger.Info("Creating new ExternalSecret", "ExternalSecret.Namespace", externalSecret.GetNamespace(), "ExternalSecret.Name", externalSecret.GetName(), "secretConfig", secretConfig.Name)
			if err := r.Create(ctx, externalSecret); err != nil {
				return fmt.Errorf("failed to create external secret for %s: %w", secretConfig.Name, err)
			}
		} else if err != nil {
			return fmt.Errorf("failed to get external secret for %s: %w", secretConfig.Name, err)
		} else {
			// Update existing external secret if needed
			logger.Info("ExternalSecret already exists", "ExternalSecret.Namespace", externalSecret.GetNamespace(), "ExternalSecret.Name", externalSecret.GetName(), "secretConfig", secretConfig.Name)
		}
	}

	return nil
}

// createDeploymentForService creates a deployment for a service workload
func (r *TreeboServiceReconciler) createDeploymentForService(treeboService *TreeboService, serviceSpec *ServiceSpec) *appsv1.Deployment {
	labels := map[string]string{
		"app":                      serviceSpec.Name,
		"treebo.com/service":       treeboService.Spec.ServiceName,
		"treebo.com/environment":   string(treeboService.Spec.Environment),
		"treebo.com/workload-type": string(serviceSpec.WorkloadType),
		"treebo.com/component":     "service",
	}

	replicas := int32(1)
	if serviceSpec.Scaling != nil {
		replicas = serviceSpec.Scaling.Replicas
	}

	// Build container spec
	container := corev1.Container{
		Name:  serviceSpec.Name,
		Image: r.getImageForWorkload(treeboService, &serviceSpec.Workload),
		Ports: []corev1.ContainerPort{
			{
				ContainerPort: serviceSpec.Port,
				Protocol:      corev1.ProtocolTCP,
			},
		},
	}

	// Add environment variables
	envVars := r.buildEnvVars(treeboService, serviceSpec.Env)

	container.Env = append(container.Env, envVars...)
	container.EnvFrom = r.buildEnvFrom(treeboService)

	// Add resource requirements
	if serviceSpec.Resources != nil {
		container.Resources = r.buildResourceRequirements(serviceSpec.Resources)
	}

	// Add health checks
	if serviceSpec.HealthCheck != nil {
		container.LivenessProbe = r.buildProbe(serviceSpec.HealthCheck)
		container.ReadinessProbe = r.buildProbe(serviceSpec.HealthCheck)
	}

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", treeboService.Spec.ServiceName, serviceSpec.Name),
			Namespace: treeboService.ObjectMeta.Namespace,
			Labels:    labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: labels,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: labels,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{container},
				},
			},
		},
	}

	return deployment
}

// createServiceForService creates a Kubernetes service for a service workload
func (r *TreeboServiceReconciler) createServiceForService(treeboService *TreeboService, serviceSpec *ServiceSpec) *corev1.Service {
	labels := map[string]string{
		"app":                      serviceSpec.Name,
		"treebo.com/service":       treeboService.Spec.ServiceName,
		"treebo.com/environment":   string(treeboService.Spec.Environment),
		"treebo.com/workload-type": string(serviceSpec.WorkloadType),
		"treebo.com/component":     "service",
	}

	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", treeboService.Spec.ServiceName, serviceSpec.Name),
			Namespace: treeboService.ObjectMeta.Namespace,
			Labels:    labels,
		},
		Spec: corev1.ServiceSpec{
			Selector: labels,
			Ports: []corev1.ServicePort{
				{
					Port:     serviceSpec.Port,
					Protocol: corev1.ProtocolTCP,
				},
			},
		},
	}

	return service
}

// createDeploymentForWorker creates a deployment for a worker workload
func (r *TreeboServiceReconciler) createDeploymentForWorker(treeboService *TreeboService, workerSpec *WorkerSpec) *appsv1.Deployment {
	labels := map[string]string{
		"app":                      workerSpec.Name,
		"treebo.com/service":       treeboService.Spec.ServiceName,
		"treebo.com/environment":   string(treeboService.Spec.Environment),
		"treebo.com/workload-type": string(workerSpec.WorkloadType),
		"treebo.com/component":     "worker",
	}

	replicas := int32(1)
	if workerSpec.Scaling != nil {
		replicas = workerSpec.Scaling.Replicas
	}

	// Build container spec
	container := corev1.Container{
		Name:  workerSpec.Name,
		Image: r.getImageForWorkload(treeboService, &workerSpec.Workload),
	}

	// Add environment variables
	container.Env = r.buildEnvVars(treeboService, workerSpec.Env)

	// Add resource requirements
	if workerSpec.Resources != nil {
		container.Resources = r.buildResourceRequirements(workerSpec.Resources)
	}

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", treeboService.Spec.ServiceName, workerSpec.Name),
			Namespace: treeboService.ObjectMeta.Namespace,
			Labels:    labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: labels,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: labels,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{container},
				},
			},
		},
	}

	return deployment
}

// createIngress creates an ingress resource
func (r *TreeboServiceReconciler) createIngress(treeboService *TreeboService, ingressSpec *IngressSpec) *networkingv1.Ingress {
	labels := map[string]string{
		"treebo.com/service":     treeboService.Spec.ServiceName,
		"treebo.com/environment": string(treeboService.Spec.Environment),
		"treebo.com/component":   "ingress",
	}

	ingressName := fmt.Sprintf("%s-ingress", treeboService.Spec.ServiceName)
	if ingressSpec.Name != nil {
		ingressName = *ingressSpec.Name
	}

	pathType := networkingv1.PathTypePrefix
	path := "/"
	if len(ingressSpec.Paths) > 0 {
		path = ingressSpec.Paths[0].Path
		if ingressSpec.Paths[0].PathType == "Exact" {
			pathType = networkingv1.PathTypeExact
		}
	}

	ingress := &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:        ingressName,
			Namespace:   treeboService.ObjectMeta.Namespace,
			Labels:      labels,
			Annotations: ingressSpec.Annotations,
		},
		Spec: networkingv1.IngressSpec{
			Rules: []networkingv1.IngressRule{
				{
					Host: ingressSpec.Host,
					IngressRuleValue: networkingv1.IngressRuleValue{
						HTTP: &networkingv1.HTTPIngressRuleValue{
							Paths: []networkingv1.HTTPIngressPath{
								{
									Path:     path,
									PathType: &pathType,
									Backend: networkingv1.IngressBackend{
										Service: &networkingv1.IngressServiceBackend{
											Name: ingressSpec.ServiceName,
											Port: networkingv1.ServiceBackendPort{
												Number: ingressSpec.ServicePort,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	return ingress
}

// createExternalSecret creates an ExternalSecret resource for AWS secrets
func (r *TreeboServiceReconciler) createExternalSecret(treeboService *TreeboService, secretConfig SecretsConfig) *unstructured.Unstructured {
	externalSecret := &unstructured.Unstructured{}
	externalSecret.SetGroupVersionKind(schema.GroupVersionKind{
		Group:   "external-secrets.io",
		Version: "v1beta1",
		Kind:    "ExternalSecret",
	})

	externalSecretName := fmt.Sprintf("%s-%s-external-secret", treeboService.Spec.ServiceName, secretConfig.Name)
	targetSecretName := secretConfig.SecretName
	if targetSecretName == "" {
		targetSecretName = fmt.Sprintf("%s-%s-secrets", treeboService.Spec.ServiceName, secretConfig.Name)
	}

	labels := map[string]string{
		"treebo.com/service":     treeboService.Spec.ServiceName,
		"treebo.com/environment": string(treeboService.Spec.Environment),
		"treebo.com/component":   "external-secret",
		"treebo.com/secret-name": secretConfig.Name,
	}

	externalSecret.SetName(externalSecretName)
	externalSecret.SetNamespace(treeboService.Namespace)
	externalSecret.SetLabels(labels)

	// Build the ExternalSecret spec
	spec := map[string]interface{}{
		"secretStoreRef": map[string]interface{}{
			"name": secretConfig.SecretStore,
			"kind": "SecretStore",
		},
		"target": map[string]interface{}{
			"name":           targetSecretName,
			"creationPolicy": "Owner",
		},
		"data": []map[string]interface{}{
			{
				"secretKey": "config",
				"remoteRef": map[string]interface{}{
					"key": secretConfig.AWSSecretPath,
				},
			},
		},
	}

	// Add refresh interval if specified
	if secretConfig.RefreshInterval != "" {
		spec["refreshInterval"] = secretConfig.RefreshInterval
	}

	externalSecret.Object["spec"] = spec

	return externalSecret
}

// getImageForWorkload returns the container image for a workload
func (r *TreeboServiceReconciler) getImageForWorkload(treeboService *TreeboService, workload *Workload) string {
	var imageConfig *ImageConfig

	// Use workload-specific image if available, otherwise use global image
	if workload.Image != nil {
		imageConfig = workload.Image
	} else if treeboService.Spec.Image != nil {
		imageConfig = treeboService.Spec.Image
	} else {
		// Default image configuration
		return "nginx:latest"
	}

	registry := ""
	if imageConfig.Registry != nil {
		registry = *imageConfig.Registry + "/"
	}

	return fmt.Sprintf("%s%s:%s", registry, imageConfig.Name, imageConfig.Tag)
}

// buildEnvVars builds environment variables for a container
func (r *TreeboServiceReconciler) buildEnvVars(treeboService *TreeboService, workloadEnv []EnvVar) []corev1.EnvVar {
	var envVars []corev1.EnvVar

	// Add global environment variables
	for _, env := range treeboService.Spec.Env {
		envVar := corev1.EnvVar{
			Name: env.Name,
		}
		if env.Value != nil {
			envVar.Value = *env.Value
		}
		envVars = append(envVars, envVar)
	}

	// Add workload-specific environment variables
	for _, env := range workloadEnv {
		envVar := corev1.EnvVar{
			Name: env.Name,
		}
		if env.Value != nil {
			envVar.Value = *env.Value
		}
		envVars = append(envVars, envVar)
	}

	return envVars
}

func (r *TreeboServiceReconciler) buildEnvFrom(treeboService *TreeboService) []corev1.EnvFromSource {
	var envFrom []corev1.EnvFromSource

	// Add each secret configuration as an envFrom source
	for _, secretConfig := range treeboService.Spec.Secrets {
		secretName := secretConfig.SecretName
		if secretName == "" {
			secretName = fmt.Sprintf("%s-%s-secrets", treeboService.Spec.ServiceName, secretConfig.Name)
		}
		envFrom = append(envFrom, corev1.EnvFromSource{
			SecretRef: &corev1.SecretEnvSource{
				LocalObjectReference: corev1.LocalObjectReference{
					Name: fmt.Sprintf("%s-secrets", secretName),
				},
			},
		})
	}

	return envFrom
}

// buildResourceRequirements builds resource requirements for a container
func (r *TreeboServiceReconciler) buildResourceRequirements(resources *ResourceThresholds) corev1.ResourceRequirements {
	requirements := corev1.ResourceRequirements{}

	if resources.Requests != nil {
		requirements.Requests = corev1.ResourceList{}
		if resources.Requests.CPU != "" {
			requirements.Requests[corev1.ResourceCPU] = resource.MustParse(resources.Requests.CPU)
		}
		if resources.Requests.Memory != "" {
			requirements.Requests[corev1.ResourceMemory] = resource.MustParse(resources.Requests.Memory)
		}
	}

	if resources.Limits != nil {
		requirements.Limits = corev1.ResourceList{}
		if resources.Limits.CPU != "" {
			requirements.Limits[corev1.ResourceCPU] = resource.MustParse(resources.Limits.CPU)
		}
		if resources.Limits.Memory != "" {
			requirements.Limits[corev1.ResourceMemory] = resource.MustParse(resources.Limits.Memory)
		}
	}

	return requirements
}

// buildProbe builds a probe for health checks
func (r *TreeboServiceReconciler) buildProbe(healthCheck *HealthCheck) *corev1.Probe {
	if healthCheck == nil {
		return nil
	}

	probe := &corev1.Probe{
		InitialDelaySeconds: healthCheck.InitialDelaySeconds,
		PeriodSeconds:       healthCheck.PeriodSeconds,
		TimeoutSeconds:      healthCheck.TimeoutSeconds,
		FailureThreshold:    healthCheck.FailureThreshold,
	}

	switch healthCheck.Type {
	case "http":
		probe.ProbeHandler = corev1.ProbeHandler{
			HTTPGet: &corev1.HTTPGetAction{
				Path: healthCheck.Path,
				Port: intstr.FromInt(int(healthCheck.Port)),
			},
		}
	case "tcp":
		probe.ProbeHandler = corev1.ProbeHandler{
			TCPSocket: &corev1.TCPSocketAction{
				Port: intstr.FromInt(int(healthCheck.Port)),
			},
		}
	default:
		// Default to HTTP health check
		probe.ProbeHandler = corev1.ProbeHandler{
			HTTPGet: &corev1.HTTPGetAction{
				Path: "/health",
				Port: intstr.FromInt(8080),
			},
		}
	}

	return probe
}

// updateStatus updates the TreeboService status with retry logic to handle resource version conflicts
func (r *TreeboServiceReconciler) updateStatus(ctx context.Context, treeboService *TreeboService, phase, message string) error {
	logger := log.FromContext(ctx)

	// Retry logic for status updates to handle resource version conflicts
	maxRetries := 5
	for attempt := 1; attempt <= maxRetries; attempt++ {
		// Get the latest version of the object to avoid resource version conflicts
		latest := &TreeboService{}
		if err := r.Get(ctx, types.NamespacedName{
			Name:      treeboService.Name,
			Namespace: treeboService.Namespace,
		}, latest); err != nil {
			return fmt.Errorf("failed to get latest TreeboService: %w", err)
		}

		// Update the status fields
		latest.Status.Phase = phase
		latest.Status.ObservedGeneration = latest.Generation

		// Add or update condition
		condition := metav1.Condition{
			Type:               "Ready",
			Status:             metav1.ConditionTrue,
			Reason:             phase,
			Message:            message,
			LastTransitionTime: metav1.Now(),
		}

		if phase == "Failed" {
			condition.Status = metav1.ConditionFalse
		} else if phase == "Reconciling" {
			condition.Status = metav1.ConditionUnknown
		}

		// Update or add the condition
		updated := false
		for i, existingCondition := range latest.Status.Conditions {
			if existingCondition.Type == "Ready" {
				latest.Status.Conditions[i] = condition
				updated = true
				break
			}
		}
		if !updated {
			latest.Status.Conditions = append(latest.Status.Conditions, condition)
		}

		// Attempt to update the status
		if err := r.Status().Update(ctx, latest); err != nil {
			if attempt == maxRetries {
				return fmt.Errorf("failed to update status after %d attempts: %w", maxRetries, err)
			}
			logger.Info("Retrying status update due to conflict", "attempt", attempt, "error", err.Error())

			// Exponential backoff: wait longer between retries
			backoffDuration := time.Millisecond * time.Duration(100*attempt*attempt)
			time.Sleep(backoffDuration)
			continue
		}

		logger.Info("Successfully updated status", "phase", phase, "attempt", attempt)
		return nil
	}

	return fmt.Errorf("failed to update status after %d attempts", maxRetries)
}
