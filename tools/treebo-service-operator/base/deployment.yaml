apiVersion: apps/v1
kind: Deployment
metadata:
  name: treebo-service-operator
  namespace: treebo-service-operator
  labels:
    app.kubernetes.io/name: treebo-service-operator
    app.kubernetes.io/component: operator
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: treebo-service-operator
      app.kubernetes.io/component: operator
  template:
    metadata:
      labels:
        app.kubernetes.io/name: treebo-service-operator
        app.kubernetes.io/component: operator
    spec:
      serviceAccountName: treebo-service-operator
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
        - name: operator
          image: ************.dkr.ecr.ap-south-1.amazonaws.com/eks-treebo-service-operator:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          env:
            - name: OPERATOR_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: OPERATOR_NAME
              value: "treebo-service-operator"
            - name: LOG_LEVEL
              value: "INFO"
            - name: RECONCILE_INTERVAL
              value: "30"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          # readinessProbe:
          #   httpGet:
          #     path: /readyz
          #     port: metrics
          #   initialDelaySeconds: 10
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          #   failureThreshold: 3
          # startupProbe:
          #   httpGet:
          #     path: /readyz
          #     port: metrics
          #   initialDelaySeconds: 10
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          #   failureThreshold: 30
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      imagePullSecrets:
        - name: ecr-registry-secret
