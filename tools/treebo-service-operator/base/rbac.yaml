apiVersion: v1
kind: ServiceAccount
metadata:
  name: treebo-service-operator
  namespace: treebo-service-operator
  labels:
    app.kubernetes.io/name: treebo-service-operator
    app.kubernetes.io/component: operator

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: treebo-service-operator
  labels:
    app.kubernetes.io/name: treebo-service-operator
    app.kubernetes.io/component: operator
rules:
  # TreeboService CRD permissions
  - apiGroups: ["treebo.com"]
    resources: ["treeboservices"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["treebo.com"]
    resources: ["treeboservices/status"]
    verbs: ["get", "update", "patch"]
  - apiGroups: ["treebo.com"]
    resources: ["treeboservices/finalizers"]
    verbs: ["update"]

  # Core Kubernetes resources
  - apiGroups: [""]
    resources: ["pods", "services", "configmaps", "secrets", "serviceaccounts"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["apps"]
    resources: ["deployments", "replicasets"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["batch"]
    resources: ["jobs", "cronjobs"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # Networking
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # Autoscaling
  - apiGroups: ["autoscaling"]
    resources: ["horizontalpodautoscalers"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # KEDA
  - apiGroups: ["keda.sh"]
    resources: ["scaledobjects", "triggerauthentications"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # External Secrets
  - apiGroups: ["external-secrets.io"]
    resources: ["externalsecrets", "secretstores"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # Monitoring
  - apiGroups: ["monitoring.coreos.com"]
    resources: ["servicemonitors", "prometheusrules"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # RBAC
  - apiGroups: ["rbac.authorization.k8s.io"]
    resources: ["roles", "rolebindings"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

  # Events
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch", "get", "list", "watch"]

  # Node access for scheduling awareness
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "watch"]

  # Namespace access
  - apiGroups: [""]
    resources: ["namespaces"]
    verbs: ["get", "list", "watch"]

  # API Resources discovery
  - apiGroups: [""]
    resources: ["apiservices"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: treebo-service-operator
  labels:
    app.kubernetes.io/name: treebo-service-operator
    app.kubernetes.io/component: operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: treebo-service-operator
subjects:
  - kind: ServiceAccount
    name: treebo-service-operator
    namespace: treebo-service-operator
