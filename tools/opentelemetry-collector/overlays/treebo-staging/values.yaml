mode: deployment

image:
  repository: ghcr.io/open-telemetry/opentelemetry-collector-releases/opentelemetry-collector-contrib

resources:
  limits:
    cpu: 250m
    memory: 512Mi

config:
  processors:
    batch: {}
    memory_limiter:
      # check_interval is the time between measurements of memory usage.
      check_interval: 5s
      # By default limit_mib is set to 80% of ".Values.resources.limits.memory"
      limit_percentage: 80
      # By default spike_limit_mib is set to 25% of ".Values.resources.limits.memory"
      spike_limit_percentage: 25

  exporters:
    otlphttp/tempo:
      endpoint: https://tempo.ekscraving1775.treebo.com
    prometheusremotewrite:
      endpoint: https://mimir.ekscraving1775.treebo.com/api/v1/push
      headers:
        X-Scope-OrgID: "otel-collector-staging"

  service:
    pipelines:
      traces:
        receivers: [otlp]
        processors: [memory_limiter, batch]
        exporters: [otlphttp/tempo]
      metrics:
        receivers: [otlp, prometheus]
        processors: [memory_limiter, batch]
        exporters: [prometheusremotewrite]

clusterRole:
  create: true
  rules:
    - apiGroups:
        - ""
      resources:
        - "pods"
        - "nodes"
      verbs:
        - "get"
        - "list"
        - "watch"

autoscaling:
  enabled: true
  minReplicas: 2

statefulset:
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes:
          - ReadWriteOnce
        storageClassName: ebs-sc
        resources:
          requests:
            storage: 10Gi

  persistentVolumeClaimRetentionPolicy:
    enabled: true

ingress:
  enabled: true
  ingressClassName: nginx
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  hosts:
    - host: otel-collector.eksdose7983.treebo.com
      paths:
        - path: /
          pathType: Prefix
          port: 4318
