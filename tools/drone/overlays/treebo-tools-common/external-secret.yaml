apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: drone-external-secret
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-parameters-store
    kind: ClusterSecretStore
  target:
    name: drone-secrets
    creationPolicy: Owner
  data:
    - secretKey: DRONE_GITHUB_CLIENT_ID
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/drone-secrets
        property: clientId
    - secretKey: DRONE_GITHUB_CLIENT_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/drone-secrets
        property: clientSecret
    - secretKey: DRONE_RPC_SECRET
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/drone-secrets
        property: droneRpcSecret
    - secretKey: DRONE_DATABASE_DATASOURCE
      remoteRef:
        key: /treebo/production/eks/cluster-treebo-tools-common/drone-secrets
        property: droneDatabaseUrl
