# Drone Helm values for community edition
# Set your secrets and config here
env:
  DRONE_SERVER_HOST: "drone.ekscraving1775.treebo.com"
  DRONE_SERVER_PROTO: "http"
  DRONE_USER_CREATE: "username:admin,admin:true"
  DRONE_AGENTS_ENABLED: true
  DRONE_LOGS_DEBUG: true
  DRONE_LOGS_TRACE: true
  DRONE_USER_FILTER: treebo-noss
  DRONE_DATABASE_DRIVER: postgres
  DRONE_LOGS_COLOR: true
  # DRONE_S3_BUCKET: s3-drone-treebo-v1

extraSecretNamesForEnvFrom:
  - drone-secrets

service:
  type: ClusterIP

ingress:
  enabled: true
  className: nginx
  hosts:
    - host: drone.ekscraving1775.treebo.com
      paths:
        - path: /
          pathType: Prefix
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"

persistentVolume:
  enabled: true
  storageClass: ebs-sc
  volumeMode: Filesystem
  size: 8Gi

serviceAccount:
  create: true
  name: drone-sa
  # annotations:
  #   eks.amazonaws.com/role-arn: $(drone_role_arn)
