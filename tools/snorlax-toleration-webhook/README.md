# Snorlax Toleration Webhook

A Kubernetes mutating admission webhook that automatically adds tolerations and nodeSelector to Snorlax wake server pods, allowing them to run on tainted nodepools.

## Problem Statement

S<PERSON><PERSON> creates wake server pods dynamically for each SleepSchedule resource. In environments with tainted nodepools (like <PERSON><PERSON><PERSON>), these wake server pods cannot be scheduled without appropriate tolerations and nodeSelector. The Snorlax CRD doesn't support configuring scheduling constraints for wake server pods, requiring manual patching.

## Solution

This webhook automatically detects Snorlax wake server pods and adds the required scheduling constraints:

```yaml
tolerations:
- key: workload-type
  value: application-critical
  effect: NoSchedule
nodeSelector:
  workload-type: application-critical
```

## How It Works

1. **Pod Detection**: The webhook identifies Snorlax wake server pods by checking for:
   - Label: `app=snorlax`
   - Name pattern: starts with `snorlax-` (wake server deployments)
2. **Scheduling Constraints Injection**: Automatically adds tolerations and nodeSelector for proper scheduling
3. **Multi-Namespace Support**: Processes pods across all namespaces where SleepSchedule resources exist
4. **Idempotent**: Skips pods that already have the required scheduling constraints

## Snorlax Wake Server Pod Identification

The webhook detects Snorlax wake server pods by looking for:
- Label: `app=snorlax`
- Pod name starts with `snorlax-` (indicating it's a wake server deployment)
- Owner reference to a SleepSchedule resource (optional additional check)

## Architecture

- **Language**: Go
- **Framework**: controller-runtime webhook framework
- **Security**: TLS certificates managed by cert-manager
- **RBAC**: Minimal permissions (read pods, handle admission reviews)
- **High Availability**: 2 replicas with proper resource limits

## Deployment

### Prerequisites

1. **cert-manager installed in the cluster**:
   ```bash
   kubectl apply -k tools/cert-manager/overlays/treebo-tools-common
   ```
2. **Snorlax operator deployed and running**
3. **Karpenter nodepools with `workload-type: application-critical` taints**

### Build and Deploy

1. **Build and push the Docker image**:
   ```bash
   cd tools/snorlax-toleration-webhook
   ./build.sh
   ```

2. **Deploy to cluster**:
   ```bash
   kubectl apply -k tools/snorlax-toleration-webhook/overlays/treebo-tools-common
   ```

### Verification

1. **Check webhook deployment**:
   ```bash
   kubectl get pods -n snorlax-toleration-webhook
   kubectl logs -n snorlax-toleration-webhook -l app.kubernetes.io/name=snorlax-toleration-webhook
   ```

2. **Verify webhook configuration**:
   ```bash
   kubectl get mutatingwebhookconfiguration snorlax-toleration-webhook -o yaml
   ```

3. **Test with a SleepSchedule**:
   - Create a SleepSchedule resource
   - Check that the wake server pods have the scheduling constraints:
   ```bash
   kubectl get pods -l app=snorlax -o yaml | grep -A 10 tolerations
   kubectl get pods -l app=snorlax -o yaml | grep -A 5 nodeSelector
   ```

## Configuration

### ConfigMap Configuration

The webhook can be configured using a ConfigMap:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: snorlax-toleration-webhook-config
  namespace: snorlax-toleration-webhook
data:
  # Toleration configuration
  toleration.key: "workload-type"
  toleration.operator: "Equal"
  toleration.value: "application-critical"
  toleration.effect: "NoSchedule"
  
  # NodeSelector configuration
  nodeSelector.key: "workload-type"
  nodeSelector.value: "application-critical"
```

Default values if ConfigMap is not found:
- Toleration: `workload-type=application-critical:NoSchedule`
- NodeSelector: `workload-type=application-critical`

## Security

- Runs as non-root user (65532)
- Read-only root filesystem
- Minimal RBAC permissions
- TLS encryption with cert-manager
- Resource limits to prevent resource exhaustion

## Troubleshooting

### Webhook Not Working

1. Check webhook pod logs:
   ```bash
   kubectl logs -n snorlax-toleration-webhook -l app.kubernetes.io/name=snorlax-toleration-webhook
   ```

2. Verify certificate:
   ```bash
   kubectl get certificate -n snorlax-toleration-webhook
   ```

3. Check webhook configuration:
   ```bash
   kubectl get mutatingwebhookconfiguration snorlax-toleration-webhook -o yaml
   ```

### Wake Server Pods Still Not Scheduled

1. Verify nodepool taints:
   ```bash
   kubectl get nodes -o custom-columns=NAME:.metadata.name,TAINTS:.spec.taints
   ```

2. Check pod scheduling constraints:
   ```bash
   kubectl get pod <snorlax-wake-server-pod> -o yaml | grep -A 10 tolerations
   kubectl get pod <snorlax-wake-server-pod> -o yaml | grep -A 5 nodeSelector
   ```

3. Verify pod labels:
   ```bash
   kubectl get pod <snorlax-wake-server-pod> -o yaml | grep -A 10 labels
   ```
