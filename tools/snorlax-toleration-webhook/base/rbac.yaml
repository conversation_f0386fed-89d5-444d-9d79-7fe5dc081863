apiVersion: v1
kind: ServiceAccount
metadata:
  name: snorlax-toleration-webhook
  namespace: snorlax-toleration-webhook
  labels:
    app.kubernetes.io/name: snorlax-toleration-webhook
    app.kubernetes.io/component: rbac

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: snorlax-toleration-webhook
  labels:
    app.kubernetes.io/name: snorlax-toleration-webhook
    app.kubernetes.io/component: rbac
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: snorlax-toleration-webhook
  labels:
    app.kubernetes.io/name: snorlax-toleration-webhook
    app.kubernetes.io/component: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: snorlax-toleration-webhook
subjects:
- kind: ServiceAccount
  name: snorlax-toleration-webhook
  namespace: snorlax-toleration-webhook
