apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: snorlax-toleration-webhook
  labels:
    app.kubernetes.io/name: snorlax-toleration-webhook
    app.kubernetes.io/component: webhook
  annotations:
    cert-manager.io/inject-ca-from: snorlax-toleration-webhook/snorlax-toleration-webhook-certs
    # Deploy after deployment is ready
    argocd.argoproj.io/sync-wave: "3"
webhooks:
  - name: snorlax-toleration.treebo.com
    clientConfig:
      service:
        name: snorlax-toleration-webhook
        namespace: snorlax-toleration-webhook
        path: "/mutate"
        port: 443
    rules:
      - operations: ["CREATE"]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
    # Target all namespaces - the webhook will filter for Snorlax wake server pods
    namespaceSelector: {}
    # Filter for pods with app=snorlax label
    objectSelector:
      matchLabels:
        app: snorlax
    admissionReviewVersions: ["v1", "v1beta1"]
    sideEffects: None
    failurePolicy: Fail
    timeoutSeconds: 10
