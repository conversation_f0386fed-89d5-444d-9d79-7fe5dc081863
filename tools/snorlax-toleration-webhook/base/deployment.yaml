apiVersion: apps/v1
kind: Deployment
metadata:
  name: snorlax-toleration-webhook
  namespace: snorlax-toleration-webhook
  labels:
    app.kubernetes.io/name: snorlax-toleration-webhook
    app.kubernetes.io/component: webhook
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: snorlax-toleration-webhook
  template:
    metadata:
      labels:
        app.kubernetes.io/name: snorlax-toleration-webhook
        app.kubernetes.io/component: webhook
    spec:
      serviceAccountName: snorlax-toleration-webhook
      securityContext:
        runAsNonRoot: true
        runAsUser: 65532
        fsGroup: 65532
      containers:
      - name: webhook
        image: snorlax-toleration-webhook:latest
        imagePullPolicy: IfNotPresent
        args:
        - --port=9443
        - --cert-dir=/tmp/k8s-webhook-server/serving-certs
        - --log-level=info
        - --config-map-name=snorlax-toleration-webhook-config
        - --config-map-namespace=snorlax-toleration-webhook
        ports:
        - containerPort: 9443
          name: webhook
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /healthz
            port: webhook
            scheme: HTTPS
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: webhook
            scheme: HTTPS
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: certs
          mountPath: /tmp/k8s-webhook-server/serving-certs
          readOnly: true
      volumes:
      - name: certs
        secret:
          secretName: snorlax-toleration-webhook-certs
