apiVersion: v1
kind: ConfigMap
metadata:
  name: snorlax-toleration-webhook-config
  namespace: snorlax-toleration-webhook
  labels:
    app.kubernetes.io/name: snorlax-toleration-webhook
    app.kubernetes.io/component: config
data:
  # Toleration configuration
  toleration.key: "workload-type"
  toleration.operator: "Equal"
  toleration.value: "application-critical"
  toleration.effect: "NoSchedule"
  
  # NodeSelector configuration
  nodeSelector.key: "workload-type"
  nodeSelector.value: "application-critical"
