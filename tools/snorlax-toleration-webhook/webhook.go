package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// WebhookConfig holds the configuration for the webhook
type WebhookConfig struct {
	Toleration  corev1.Toleration
	NodeSelector map[string]string
}

// SnorlaxTolerationMutator implements the mutating webhook for adding tolerations to Snorlax wake server pods
type SnorlaxTolerationMutator struct {
	Client              kubernetes.Interface
	Logger              logr.Logger
	Config              *WebhookConfig
	ConfigMapName       string
	ConfigMapNamespace  string
}

// Handle implements the admission.Handler interface
func (m *SnorlaxTolerationMutator) Handle(ctx context.Context, req admission.Request) admission.Response {
	pod := &corev1.Pod{}

	// Decode the pod from the admission request
	if err := json.Unmarshal(req.Object.Raw, pod); err != nil {
		m.Logger.Error(err, "Failed to unmarshal pod")
		return admission.Errored(http.StatusBadRequest, err)
	}

	m.Logger.Info("Processing pod",
		"name", pod.Name,
		"namespace", pod.Namespace,
		"labels", pod.Labels)

	// Check if this is a Snorlax wake server pod
	if !m.isSnorlaxWakeServerPod(pod) {
		m.Logger.V(1).Info("Pod is not a Snorlax wake server pod, skipping",
			"name", pod.Name,
			"namespace", pod.Namespace)
		return admission.Allowed("Not a Snorlax wake server pod")
	}

	// Load configuration from ConfigMap
	if err := m.loadConfig(ctx); err != nil {
		m.Logger.Error(err, "Failed to load configuration from ConfigMap")
		return admission.Errored(http.StatusInternalServerError, err)
	}

	m.Logger.Info("Detected Snorlax wake server pod, adding scheduling constraints",
		"name", pod.Name,
		"namespace", pod.Namespace)

	// Create a copy of the pod to modify
	modifiedPod := pod.DeepCopy()

	modified := false

	// Add the toleration if it doesn't already exist and is configured
	if m.Config != nil && !m.hasToleration(modifiedPod, m.Config.Toleration) {
		modifiedPod.Spec.Tolerations = append(modifiedPod.Spec.Tolerations, m.Config.Toleration)
		m.Logger.Info("Added toleration to Snorlax wake server pod",
			"name", pod.Name,
			"namespace", pod.Namespace,
			"toleration", m.Config.Toleration)
		modified = true
	}

	// Add nodeSelector if it doesn't already exist and is configured
	if m.Config != nil && len(m.Config.NodeSelector) > 0 {
		if modifiedPod.Spec.NodeSelector == nil {
			modifiedPod.Spec.NodeSelector = make(map[string]string)
		}
		
		for key, value := range m.Config.NodeSelector {
			if existingValue, exists := modifiedPod.Spec.NodeSelector[key]; !exists || existingValue != value {
				modifiedPod.Spec.NodeSelector[key] = value
				m.Logger.Info("Added nodeSelector to Snorlax wake server pod",
					"name", pod.Name,
					"namespace", pod.Namespace,
					"key", key,
					"value", value)
				modified = true
			}
		}
	}

	if !modified {
		m.Logger.Info("No changes needed, skipping",
			"name", pod.Name,
			"namespace", pod.Namespace)
		return admission.Allowed("No changes needed")
	}

	// Create the patch
	originalBytes, err := json.Marshal(pod)
	if err != nil {
		m.Logger.Error(err, "Failed to marshal original pod")
		return admission.Errored(http.StatusInternalServerError, err)
	}

	modifiedBytes, err := json.Marshal(modifiedPod)
	if err != nil {
		m.Logger.Error(err, "Failed to marshal modified pod")
		return admission.Errored(http.StatusInternalServerError, err)
	}

	// Return the response with the patch
	return admission.PatchResponseFromRaw(originalBytes, modifiedBytes)
}

// isSnorlaxWakeServerPod checks if the pod is a Snorlax wake server pod
func (m *SnorlaxTolerationMutator) isSnorlaxWakeServerPod(pod *corev1.Pod) bool {
	if pod.Labels == nil {
		return false
	}

	// Check for Snorlax app label
	if appLabel, exists := pod.Labels["app"]; !exists || appLabel != "snorlax" {
		return false
	}

	// Check if the pod name starts with "snorlax-" (wake server pattern)
	if !strings.HasPrefix(pod.Name, "snorlax-") {
		return false
	}

	m.Logger.V(1).Info("Found Snorlax wake server pod",
		"name", pod.Name,
		"namespace", pod.Namespace,
		"app-label", pod.Labels["app"])

	return true
}

// hasToleration checks if the pod already has the specified toleration
func (m *SnorlaxTolerationMutator) hasToleration(pod *corev1.Pod, targetToleration corev1.Toleration) bool {
	for _, toleration := range pod.Spec.Tolerations {
		if toleration.Key == targetToleration.Key &&
			toleration.Operator == targetToleration.Operator &&
			toleration.Value == targetToleration.Value &&
			toleration.Effect == targetToleration.Effect {
			return true
		}
	}
	return false
}

// loadConfig loads configuration from ConfigMap
func (m *SnorlaxTolerationMutator) loadConfig(ctx context.Context) error {
	if m.ConfigMapName == "" || m.ConfigMapNamespace == "" {
		m.Logger.Info("ConfigMap name or namespace not specified, using default configuration")
		// Use default configuration
		m.Config = &WebhookConfig{
			Toleration: corev1.Toleration{
				Key:      "workload-type",
				Operator: corev1.TolerationOpEqual,
				Value:    "application-critical",
				Effect:   corev1.TaintEffectNoSchedule,
			},
			NodeSelector: map[string]string{
				"workload-type": "application-critical",
			},
		}
		return nil
	}

	configMap, err := m.Client.CoreV1().ConfigMaps(m.ConfigMapNamespace).Get(ctx, m.ConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get ConfigMap %s/%s: %w", m.ConfigMapNamespace, m.ConfigMapName, err)
	}

	config := &WebhookConfig{
		NodeSelector: make(map[string]string),
	}

	// Parse toleration configuration
	if tolerationKey, exists := configMap.Data["toleration.key"]; exists {
		config.Toleration.Key = tolerationKey
	} else {
		config.Toleration.Key = "workload-type"
	}

	if tolerationOperator, exists := configMap.Data["toleration.operator"]; exists {
		switch tolerationOperator {
		case "Equal":
			config.Toleration.Operator = corev1.TolerationOpEqual
		case "Exists":
			config.Toleration.Operator = corev1.TolerationOpExists
		default:
			config.Toleration.Operator = corev1.TolerationOpEqual
		}
	} else {
		config.Toleration.Operator = corev1.TolerationOpEqual
	}

	if tolerationValue, exists := configMap.Data["toleration.value"]; exists {
		config.Toleration.Value = tolerationValue
	} else {
		config.Toleration.Value = "application-critical"
	}

	if tolerationEffect, exists := configMap.Data["toleration.effect"]; exists {
		switch tolerationEffect {
		case "NoSchedule":
			config.Toleration.Effect = corev1.TaintEffectNoSchedule
		case "PreferNoSchedule":
			config.Toleration.Effect = corev1.TaintEffectPreferNoSchedule
		case "NoExecute":
			config.Toleration.Effect = corev1.TaintEffectNoExecute
		default:
			config.Toleration.Effect = corev1.TaintEffectNoSchedule
		}
	} else {
		config.Toleration.Effect = corev1.TaintEffectNoSchedule
	}

	// Parse nodeSelector configuration
	if nodeSelectorKey, exists := configMap.Data["nodeSelector.key"]; exists {
		if nodeSelectorValue, exists := configMap.Data["nodeSelector.value"]; exists {
			config.NodeSelector[nodeSelectorKey] = nodeSelectorValue
		}
	} else {
		// Default nodeSelector
		config.NodeSelector["workload-type"] = "application-critical"
	}

	m.Config = config

	m.Logger.Info("Loaded configuration from ConfigMap",
		"configMap", fmt.Sprintf("%s/%s", m.ConfigMapNamespace, m.ConfigMapName),
		"toleration", config.Toleration,
		"nodeSelector", config.NodeSelector)

	return nil
}
