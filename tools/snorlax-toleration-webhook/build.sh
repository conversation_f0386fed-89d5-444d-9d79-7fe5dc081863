#!/bin/bash

set -e

# Configuration
IMAGE_NAME="snorlax-toleration-webhook"
REGISTRY="${REGISTRY:-your-registry}"
TAG="${TAG:-latest}"
FULL_IMAGE="${REGISTRY}/${IMAGE_NAME}:${TAG}"

echo "Building Snorlax Toleration Webhook..."
echo "Image: ${FULL_IMAGE}"

# Build the Docker image
docker build -t "${FULL_IMAGE}" .

echo "Build completed successfully!"
echo "To push the image, run:"
echo "  docker push ${FULL_IMAGE}"

# Optionally push if PUSH=true
if [ "${PUSH}" = "true" ]; then
    echo "Pushing image..."
    docker push "${FULL_IMAGE}"
    echo "Image pushed successfully!"
fi
