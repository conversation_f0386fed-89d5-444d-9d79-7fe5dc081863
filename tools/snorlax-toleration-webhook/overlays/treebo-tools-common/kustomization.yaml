apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

components:
  - ../../../lib/common/components/system-critical

images:
  - name: snorlax-toleration-webhook
    newName: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/snorlax-toleration-webhook
    newTag: latest

patchesStrategicMerge:
  - deployment-patch.yaml

patches:
  # Use a simple test image and set failurePolicy to Ignore for testing
  - target:
      kind: Deployment
      name: snorlax-toleration-webhook
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/image
        value: nginx:alpine
      - op: replace
        path: /spec/template/spec/containers/0/command
        value: ["sleep", "3600"]
  - target:
      kind: MutatingWebhookConfiguration
      name: snorlax-toleration-webhook
    patch: |-
      - op: replace
        path: /webhooks/0/failurePolicy
        value: Ignore
