# GitOps Repository: Evolution and Current State

This repository serves as the single source of truth for deploying services and tools onto our Kubernetes clusters across various environments using a GitOps methodology. This document outlines the journey of this repository, detailing the different strategies and tools we explored before arriving at our current approach utilizing Kustomize, Helm, and Argo CD.



https://github.com/user-attachments/assets/cbc3265b-d2ab-455b-b5db-aa417cf7051d



## Purpose and Goals

The primary goals of this repository are:

* To define and manage the desired state of all Kubernetes resources (applications and tools) in a declarative manner.  
* To enable automated and reliable deployments and updates through a GitOps workflow orchestrated by Argo CD.  
* To provide a clear, organized, and maintainable structure for managing Kubernetes configurations across multiple environments and services.  
* To ensure consistency and repeatability in our deployments.

## Environment Overview

We manage deployments across the following environments:

* treebo-production: Production environment.  
* treebo-preprod: Pre-production environment.  
* treebo-staging: Staging environment.

Services are broadly categorized into:

1. Application Services: Primarily API servers and message queue consumers (RabbitMQ, Kafka), potentially including batch or ad-hoc job containers. Each application environment typically has its own dedicated Kubernetes cluster.  
2. Tools: Shared infrastructure tools like Sentry, Typesense, Grafana, Prometheus, Loki, etc. While environment-specific tool clusters exist (used sparingly for non-multitenant tools), a dedicated common cluster hosts tools shared across all environments (e.g., Sentry, Typesense).

## Evolution of Our GitOps Strategy

Our approach to managing Kubernetes configurations has evolved significantly based on practical experience and the need to find the most effective tooling for our needs.

### Initial Approach (Version 1): CDK and CDK8s Focus

We began with a strong preference for using AWS CDK for managing the underlying cluster infrastructure (cluster-management) and CDK8s for defining Kubernetes resources for applications (application-management) and tools (tools-management). The rationale was to leverage familiar programming languages and infrastructure-as-code principles across both cloud infrastructure and Kubernetes resources. Helm and Kompose were considered fallback options only if CDK/CDK8s proved unsuitable.

The initial directory structure reflected this separation:

```
.  
├── cluster-management/ \# CDK code for cluster infrastructure (EKS, VPC, etc.)  
├── application-management/ \# CDK8s code for application deployments  
└── tools-management/ \# CDK8s code for tool deployments
```

Each of these top-level directories contained subdirectories per environment.

### Challenges and Transitions (Version 2): Exploring Pulumi

As we implemented the CDK8s-based approach, we encountered several limitations, particularly concerning the integration with Helm charts and the management of Kubernetes namespaces. Referencing resources created by Helm charts within CDK8s constructs proved cumbersome. Additionally, CDK8s' native namespace handling was not ideally suited for idempotent create-or-update operations required in a GitOps flow. These challenges impacted developer experience and the maintainability of our configurations.

This led us to explore alternative tools, including Pulumi. Pulumi offered promising features like seamless Helm support, idempotent resource management (including namespaces), and a flexible multi-language approach, which seemed to address the pain points experienced with CDK8s. We evaluated a workflow using Pulumi to define resources, export manifests, and apply them via kubectl.

### Final Adopted Strategy (Version 3): Kustomize, Helm, and Argo CD

After evaluating different tools, we converged on a strategy leveraging Kustomize and Helm as our primary configuration management tools, orchestrated by Argo CD. This combination provides a robust and flexible solution for managing Kubernetes manifests, handling variations across environments, and automating deployments via GitOps.

#### Rationale for Kustomize, Helm, and Argo CD:

* **Kustomize:** Excellent for customizing base Kubernetes manifests for different environments or use cases without templating. It allows for patching and overlaying configurations effectively.  
* **Helm:** Ideal for packaging and deploying complex applications with configurable values, especially for third-party tools.  
* **Argo CD:** A powerful declarative GitOps continuous delivery tool for Kubernetes. It automatically synchronizes the desired state defined in this repository with the actual state of applications running in the clusters. It provides a clear UI for visualizing application status and dependencies.

**Current Repository Structure:**

The repository is now structured as a monorepo, organized to support this Kustomize/Helm/Argo CD workflow:

```
infrastructure/  
├── apps/ \# Definitions for application services  
│   ├── service-a/  
│   │   ├── base/ \# Base manifests (Kustomize)  
│   │   └── overlays/ \# Environment-specific customizations (Kustomize)  
│   └── service-b/  
│       ├── base/  
│       └── overlays/  
├── tools/ \# Definitions for shared tools  
│   ├── typesense/  
│   │   ├── base/  
│   │   └── overlays/  
│   ├── sentry/  
│   │   ├── base/  
│   │   └── overlays/  
│   └── ... \# Other tools  
├── clusters/ \# Cluster-specific configurations (Argo CD Application definitions)  
│   ├── cluster-treebo-production/ \# Configuration for the production cluster  
│   ├── cluster-treebo-preprod/ \# Configuration for the pre-production cluster  
│   ├── cluster-treebo-staging/ \# Configuration for the staging cluster  
│   └── cluster-treebo-tools-common/ \# Configuration for the tools cluster  
└── manifests/argocd/ \# Bootstrap configuration for Argo CD
```

This structure clearly separates application and tool definitions from cluster-specific deployments. The clusters directory contains Argo CD Application resources that point to the relevant configurations within the apps and tools directories, applying environment-specific overlays as needed.

While the provided text mentioned FluxCD in the final version description, our current implementation utilizes **Argo CD** as the GitOps controller.

## Conventions

We continue to adhere to the following conventions established during the repository's evolution:

* Tagging: Resources are tagged with t-name, t-description, t-env, t-type, and t-owner.  
* Resource Naming: Use lowercase kebab-case with appropriate prefixes (e.g., vpc-, sg-, eks-). Use full environment names where applicable (e.g., vpc-treebo-tools-common).

This repository represents our commitment to a robust, automated, and transparent deployment process for our Kubernetes workloads.



## Best Practices

Given your stack (Kubernetes, ArgoCD GitOps, AWS EKS, External Secrets Operator, Kustomize overlays, Conftest, Kubeconform, and a strong focus on security and automation), here are ultra-thoughtful, advanced best practices tailored for your repo and workflows:

1. GitOps & Repository Hygiene
Immutable, PR-based Deployments: All changes (infra, manifests, secrets references) go through PRs with mandatory reviews.
Overlay Consistency: Use automation/scripts to ensure overlays for all environments are always in sync structurally (e.g., same resources, patches, secrets).
Directory Structure Enforcement: Use tools (e.g., checkov, custom scripts) to enforce directory and naming conventions for overlays, secrets, configmaps, etc.
2. Kubernetes Manifest Quality
Schema Validation: (Already using kubeconform) – consider also running kube-linter for security and reliability checks (e.g., missing resource requests/limits, privileged containers).
YAML Formatting: Use yamllint to ensure clean, consistent YAML formatting.
Kustomize Build Validation: Run kustomize build for each overlay in CI to ensure overlays render correctly and catch merge errors early.
3. Security & Secrets Management
No Secrets in Git: Only references to secrets (never values). Use External Secrets Operator for all sensitive data.
Secret Rotation: Automate secret rotation in AWS and ensure ESO picks up changes without manual intervention.
RBAC Least Privilege: Use strict RBAC for ArgoCD, service accounts, and ESO. Ensure workloads only have access to their own secrets.
Secret/ConfigMap Access Auditing: Periodically audit which workloads can access which secrets/configmaps.
4. Environment Variable and Config Validation
Automated Policy Enforcement: (Already using Conftest) – expand policies to check for:
No default passwords/hardcoded secrets.
No accidental exposure of secrets via logs or envs.
Documentation Automation: Generate markdown docs from your base manifests listing all required envs, their source, and description. Keep docs in sync with code using CI.
5. Deployment & Rollout Safety
ArgoCD Sync Waves/Hooks: (Already using) – for all critical jobs (e.g., migrations), use sync-waves to guarantee order.
Readiness/Liveness Probes: Ensure all Deployments have proper health checks.
PodDisruptionBudgets: Use PDBs for critical services to avoid accidental downtime during node drains.
Resource Requests/Limits: Enforce via policy/linter that all containers have CPU/memory requests and limits.
6. Observability & Monitoring
Prometheus/Grafana Integration: Expose metrics from apps, use ServiceMonitors, and alert on error rates, latency, etc.
Centralized Logging: Ensure all apps log to a central system (e.g., CloudWatch, ELK, Loki).
ArgoCD Notifications: Use ArgoCD notifications to Slack/email for sync failures, health changes, etc.
7. CI/CD Pipeline Quality
Parallel Validation: Run all validation jobs (Conftest, kubeconform, yamllint, kustomize build) in parallel for fast feedback.
Fail Fast: Fail the pipeline on the first error for any validation step.
PR Template: Enforce PR templates requiring description of what’s changed, why, and any required manual steps.
8. Cluster & Infra Best Practices
Namespace-per-Environment: Isolate resources by namespace for prod/staging/dev.
Network Policies: Use K8s NetworkPolicies to restrict pod-to-pod traffic.
Pod Security Standards: Enforce via PSP/OPA that workloads run as non-root, with minimal privileges.
9. Drift Detection & Reconciliation
ArgoCD Drift Alerts: Enable drift detection and notifications for when live state diverges from Git.
Periodic Sync: Schedule regular ArgoCD syncs to ensure reconciliation.
10. Disaster Recovery & Rollback
Automated Backups: Back up critical secrets, configmaps, and persistent data.
Rollback Readiness: Use ArgoCD’s app rollback feature and keep manifests for previous stable releases handy.
11. Scalability & Cost Optimization
HPA/Cluster Autoscaler: Use HPA for workloads and enable cluster autoscaler for EKS.
Resource Quotas: Set quotas per namespace to avoid noisy neighbor problems.
12. Continuous Improvement
Retrospective Automation: After every incident or change, automate checks to prevent recurrence (e.g., if a missing env var caused an outage, add a policy for it).
Regular Policy Review: Periodically review and update Conftest/Kubeconform/OPA policies as your stack evolves.
Bonus: Automation Ideas
Overlay Sync Script: Script to auto-create overlays for new environments, copying structure and validating required files.
Secret Key Sync Checker: Script to ensure all required keys are present in AWS SSM/SecretsManager for each environment.
Kustomize Overlay Visualizer: Tool to show what’s rendered in each overlay for easier review.