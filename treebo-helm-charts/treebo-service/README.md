# TreeboService Helm Chart

A flexible Helm chart for deploying microservices with the TreeboService abstraction, supporting services, workers, tasks, and flexible ingress configuration.

## Features

- **Services**: Web services with health checks, scaling, and resource management
- **Workers**: Background workers with KEDA autoscaling support
- **Worker Templates**: Reusable worker templates to eliminate duplication across tenants
- **Tasks**: Jobs, CronJobs, and migration tasks with dependency management
- **Flexible Ingress**: Multi-path routing, custom annotations, and multiple ingress classes
- **WorkloadCriticality**: Node affinity and tolerations based on criticality levels
- **ArgoCD Integration**: Sync waves for proper deployment ordering
- **External Secrets**: Integration with External Secrets Operator

## Quick Start

```yaml
serviceName: my-app
environment: staging

services:
- name: web
  type: frontend
  port: 3000
  workloadCriticality: application-critical
  image:
    repository: my-app/web
    tag: "v1.0.0"
  healthCheck:
    enabled: true
    type: httpGet
    path: /health
    port: 3000

ingress:
- name: main
  host: my-app.example.com
  ingressClassName: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  tls: true
  paths:
  - path: /
    pathType: Prefix
    serviceName: web
    servicePort: 3000
```

## Namespace Management

The chart can optionally create and manage namespaces:

```yaml
# Create namespace automatically
namespace:
  create: true
  name: my-custom-namespace  # Optional: defaults to Release.Namespace

serviceName: my-app
environment: staging
```

### Namespace Configuration Options

- **`namespace.create`**: Set to `true` to create the namespace (default: `false`)
- **`namespace.name`**: Custom namespace name (optional, defaults to `Release.Namespace`)

### Features

- **Automatic Creation**: Creates namespace with proper labels and annotations
- **Resource Policy**: Includes `helm.sh/resource-policy: keep` to prevent deletion
- **Consistent Labeling**: Applies service and environment labels for organization
- **ArgoCD Compatible**: Works seamlessly with ArgoCD namespace management

### Usage Patterns

**Pattern 1: Use Existing Namespace (Default)**
```yaml
# Namespace managed externally (ArgoCD, kubectl, etc.)
namespace:
  create: false  # Default behavior
```

**Pattern 2: Create Custom Namespace**
```yaml
# Chart creates and manages namespace
namespace:
  create: true
  name: my-app-production
```

**Pattern 3: Use Release Namespace**
```yaml
# Chart creates namespace with same name as Helm release
namespace:
  create: true
  # name defaults to Release.Namespace
```

## Flexible Ingress Configuration

### Multiple Paths on Single Host

Route different paths to different services:

```yaml
ingress:
- name: main
  host: app.example.com
  ingressClassName: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  tls: true
  paths:
  - path: /
    pathType: Prefix
    serviceName: frontend
    servicePort: 3000
  - path: /api
    pathType: Prefix
    serviceName: backend
    servicePort: 8000
  - path: /admin
    pathType: Prefix
    serviceName: admin
    servicePort: 3001
```

### Multiple Hosts with Different Configurations

```yaml
ingress:
- name: main
  host: app.example.com
  ingressClassName: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  tls: true
  paths:
  - path: /
    pathType: Prefix
    serviceName: frontend
    servicePort: 3000

- name: api
  host: api.example.com
  ingressClassName: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
  tls: true
  paths:
  - path: /
    pathType: Prefix
    serviceName: backend
    servicePort: 8000
```

### Advanced Ingress Features

#### Custom TLS Secret Names

```yaml
ingress:
- name: main
  host: app.example.com
  tls: true
  tlsSecretName: custom-tls-secret  # Optional, defaults to auto-generated
```

#### Different Ingress Classes

```yaml
ingress:
- name: nginx-ingress
  host: app.example.com
  ingressClassName: nginx
  
- name: alb-ingress
  host: external.example.com
  ingressClassName: alb
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
```

#### Path Types

```yaml
paths:
- path: /exact-match
  pathType: Exact  # Must match exactly
  serviceName: service1
  servicePort: 8000
  
- path: /prefix
  pathType: Prefix  # Matches /prefix/*
  serviceName: service2
  servicePort: 8001
  
- path: /implementation-specific
  pathType: ImplementationSpecific  # Controller-specific behavior
  serviceName: service3
  servicePort: 8002
```

## Volume Support

TreeboService supports flexible volume configuration for persistent storage across all workload types (services, workers, tasks).

### Quick Start - Volume Configuration

Add volumes to your workloads using the array syntax:

```yaml
# No EFS configuration needed - defaults to "shared" alias
services:
- name: api-server
  volumes:
  - name: uploads
    type: pvc
    pvc:
      size: 500Gi
      storageClass: efs-sc  # Uses "shared" EFS automatically
    mountPath: /app/uploads
  - name: high-perf-data
    type: efs
    efs:
      alias: "high-performance"  # Override for specific volume
    mountPath: /app/data
```

This automatically:
- **Uses "shared" EFS** by default (no configuration needed)
- **Resolves EFS aliases** to actual file system IDs via ConfigMap lookup
- **Creates EFS PVCs** with `ReadWriteMany` access mode
- **Generates access points** named `{serviceName}-{volumeName}`
- **Mounts volumes** at specified path or `/app/{volumeName}`

### Storage Types Supported

| Type | Use Case | Access Mode | Example |
|------|----------|-------------|---------|
| **EFS** | Multi-pod apps, shared storage | ReadWriteMany | File uploads, shared cache |
| **EBS** | Single-pod apps, databases | ReadWriteOnce | Database storage, high IOPS |
| **ConfigMap** | Configuration files | ReadOnlyMany | App config, certificates |
| **Secret** | Sensitive data | ReadOnlyMany | API keys, passwords |
| **EmptyDir** | Temporary storage | - | Temp files, in-memory cache |

### EFS Aliases

EFS volumes use meaningful aliases instead of complex file system IDs:

| Alias | Use Case | Performance | Auto-Selected |
|-------|----------|-------------|---------------|
| `shared` | General storage | Standard (bursting) | ✅ Default |
| `high-performance` | Intensive workloads | Provisioned (100 MiB/s) | Override only |

**Default Behavior (90% of users):**
```yaml
# No EFS configuration needed - uses "shared" automatically
volumes:
- name: uploads
  type: pvc
  pvc:
    storageClass: efs-sc  # Uses "shared" EFS alias
```

**Performance Override (10% of users):**
```yaml
volumes:
- name: ml-data
  type: efs
  efs:
    alias: "high-performance"  # Override for intensive workloads
```

**Advanced: Direct File System ID (infrastructure teams):**
```yaml
global:
  efs:
    fileSystemId: "fs-0123456789abcdef0"  # Skip alias system entirely
```

### Volume Types

**PVC Volumes (Recommended):**
```yaml
volumes:
- name: uploads
  type: pvc
  pvc:
    size: 500Gi
    storageClass: efs-sc        # EFS for multi-pod
    performance: false          # Standard performance
- name: database
  type: pvc
  pvc:
    size: 100Gi
    storageClass: ebs-sc        # EBS for single-pod
    accessModes: ["ReadWriteOnce"]
```

**Direct EFS Volumes:**
```yaml
volumes:
- name: shared-storage
  type: efs
  efs:
    fileSystemId: "fs-custom"   # Override global EFS ID
    accessPoint: "fsap-custom"  # Use existing access point
    path: "/custom/path"
```

**Other Volume Types:**
```yaml
volumes:
- name: config
  type: configMap
  configMap:
    name: app-config
  mountPath: /etc/config
  readOnly: true
- name: temp
  type: emptyDir
  emptyDir:
    sizeLimit: 1Gi
    medium: Memory  # Use tmpfs
```

### Auto Access Point Generation

EFS volumes automatically generate access points with the pattern:
- **Pattern**: `{serviceName}-{volumeName}`
- **Example**: Service `nexus` with volume `uploads` → Access point `nexus-uploads`
- **Path**: `/treebo-services/{serviceName}/{volumeName}`

### Storage Classes

| Storage Class | Type | Use Case | Access Mode | Performance |
|---------------|------|----------|-------------|-------------|
| `efs-sc` | EFS | Multi-pod applications | ReadWriteMany | Standard |
| `efs-performance-sc` | EFS | High-performance workloads | ReadWriteMany | High (with caching) |
| `ebs-sc` | EBS | Single-pod applications | ReadWriteOnce | High IOPS |

### Usage Patterns

**Pattern 1: Simple Applications (90% of users)**
```yaml
serviceName: blog-app
services:
- name: web
  volumes:
  - name: uploads
    type: pvc
    pvc:
      size: 500Gi
      storageClass: efs-sc  # Uses "shared" EFS automatically
```

**Pattern 2: Performance-Aware Applications**
```yaml
serviceName: ml-training
services:
- name: trainer
  volumes:
  - name: datasets
    type: efs
    efs:
      alias: "high-performance"  # Override for intensive workloads
    mountPath: /data
```

**Pattern 3: Mixed Workloads**
```yaml
serviceName: media-processor
services:
- name: processor
  volumes:
  - name: uploads
    type: pvc
    pvc:
      storageClass: efs-sc  # Standard performance for uploads
  - name: processing
    type: efs
    efs:
      alias: "high-performance"  # High performance for processing
  - name: archive
    type: pvc
    pvc:
      storageClass: efs-sc  # Back to standard for archive
```

**Pattern 4: Infrastructure Override (Advanced)**
```yaml
# For infrastructure teams who need direct control
global:
  efs:
    fileSystemId: "fs-0123456789abcdef0"  # Skip alias system
```

## Scaling Configuration

TreeboService supports both HPA (HorizontalPodAutoscaler) and KEDA (Kubernetes Event-driven Autoscaling) for automatic scaling based on different metrics and triggers.

### Quick Start - Scaling

**Plain static scaling (default):**
```yaml
services:
- name: web-server
  scaling:
    type: plain
    plainConfig:
      replicas: 3
```

**HPA for CPU/Memory-based scaling:**
```yaml
services:
- name: api-server
  scaling:
    type: hpa
    hpaConfig:
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
      targetMemoryUtilizationPercentage: 80
```

**KEDA for event-driven scaling:**
```yaml
workers:
- name: celery-worker
  scaling:
    type: keda
    kedaConfig:
      minReplicas: 0
      maxReplicas: 20
      triggers:
      - type: redis
        metadata:
          address: redis://redis:6379/0
          listName: celery
          listLength: "5"
```

### Scaling Types

| Type | Use Case | Best For | Metrics |
|------|----------|----------|---------|
| **Plain** | Static replica count | Simple services, fixed workloads | None (static) |
| **HPA** | Resource-based scaling | Web services, APIs | CPU, Memory |
| **KEDA** | Event-driven scaling | Workers, consumers | Queue length, custom metrics |
| **KEDA HTTP** | HTTP request-based scaling | Web services, APIs | HTTP request rate, pending requests |

### HPA Configuration

**Basic HPA Setup:**
```yaml
services:
- name: web-server
  scaling:
    type: hpa
    hpaConfig:
      minReplicas: 2
      maxReplicas: 15
      targetCPUUtilizationPercentage: 70
      targetMemoryUtilizationPercentage: 85
```

**Advanced HPA with Scaling Behavior:**
```yaml
services:
- name: api-server
  scaling:
    type: hpa
    hpaConfig:
      minReplicas: 3
      maxReplicas: 50
      targetCPUUtilizationPercentage: 60
      targetMemoryUtilizationPercentage: 75
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 10
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 0
          policies:
          - type: Percent
            value: 100
            periodSeconds: 15
          - type: Pods
            value: 4
            periodSeconds: 15
          selectPolicy: Max
```

### KEDA Configuration

**Redis Queue Scaling:**
```yaml
workers:
- name: task-processor
  scaling:
    type: keda
    kedaConfig:
      minReplicas: 0
      maxReplicas: 30
      pollingInterval: 30
      cooldownPeriod: 300
      idleReplicaCount: 0
      triggers:
      - type: redis
        metadata:
          address: redis-master.redis.svc.cluster.local:6379
          listName: task_queue
          listLength: "10"
```

**AWS SQS Scaling:**
```yaml
workers:
- name: sqs-consumer
  scaling:
    type: keda
    kedaConfig:
      minReplicas: 0
      maxReplicas: 50
      triggers:
      - type: aws-sqs-queue
        metadata:
          queueURL: https://sqs.ap-south-1.amazonaws.com/123456789012/my-queue
          queueLength: "5"
          awsRegion: ap-south-1
        authenticationRef:
          name: keda-aws-credentials
```

**Prometheus Metrics Scaling:**
```yaml
services:
- name: api-server
  scaling:
    type: keda
    kedaConfig:
      minReplicas: 2
      maxReplicas: 20
      triggers:
      - type: prometheus
        metadata:
          serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
          metricName: http_requests_per_second
          threshold: "100"
          query: sum(rate(http_requests_total{service="api-server"}[2m]))
```

**CPU/Memory with KEDA:**
```yaml
services:
- name: compute-service
  scaling:
    type: keda
    kedaConfig:
      minReplicas: 1
      maxReplicas: 10
      triggers:
      - type: cpu
        metricType: Utilization
        metadata:
          value: "70"
      - type: memory
        metricType: Utilization
        metadata:
          value: "80"
```

### KEDA HTTP Configuration

NOTE: This is exceptional and advanced usecase for scale to zero configs. This is not recommended for most usecases. This scaler intercepts http requests and adds an additional latency to the request. If the scale to zero is not required for the service, use Keda with prometheus query for HTTP scaling instead. This also requires us to enabled keda-http addon within the keda chart

**HTTP Request-Based Scaling:**
```yaml
services:
- name: api-server
  scaling:
    type: keda-http
    httpScaledObjectConfig:
      minReplicas: 2
      maxReplicas: 20
      targetPendingRequests: 100
      scaledownPeriod: 300
      scaleupPeriod: 60
      hosts:
      - "api.example.com"
      pathPrefixes:
      - "/api/v1"
      - "/api/v2"
```

**Multi-Host HTTP Scaling:**
```yaml
services:
- name: web-frontend
  scaling:
    type: keda-http
    httpScaledObjectConfig:
      minReplicas: 1
      maxReplicas: 50
      targetPendingRequests: 50
      scaledownPeriod: 600
      scaleupPeriod: 30
      hosts:
      - "app.example.com"
      - "www.example.com"
      - "api.example.com"
      pathPrefixes:
      - "/"
```

**Worker HTTP Endpoints:**
```yaml
workers:
- name: webhook-processor
  scaling:
    type: keda-http
    httpScaledObjectConfig:
      minReplicas: 0
      maxReplicas: 30
      targetPendingRequests: 25
      scaledownPeriod: 300
      scaleupPeriod: 15
      hosts:
      - "webhooks.example.com"
      pathPrefixes:
      - "/webhook"
      - "/callback"
```

### Scaling Configuration Reference

**Common Fields:**
- `type`: Scaling type - `plain`, `hpa`, `keda`, or `keda-http` (default: `plain`)
- `replicas`: Static replica count when no autoscaling is configured (default: `1`)

**Plain Configuration (`plainConfig`):**
- `replicas`: Number of replicas for static scaling (default: `1`)

**HPA Configuration (`hpaConfig`):**
- `minReplicas`: Minimum number of replicas (default: `1`)
- `maxReplicas`: Maximum number of replicas (default: `10`)
- `targetCPUUtilizationPercentage`: CPU target percentage (1-100)
- `targetMemoryUtilizationPercentage`: Memory target percentage (1-100)
- `behavior`: Advanced scaling behavior configuration

**KEDA Configuration (`kedaConfig`):**
- `minReplicas`: Minimum number of replicas (default: `0`)
- `maxReplicas`: Maximum number of replicas (default: `10`)
- `pollingInterval`: How often to check metrics in seconds (default: `30`)
- `cooldownPeriod`: Cooldown period after scaling in seconds (default: `300`)
- `idleReplicaCount`: Replicas when no activity (default: `0`)
- `triggers`: Array of KEDA scaling triggers

**KEDA HTTP Configuration (`httpScaledObjectConfig`):**
- `minReplicas`: Minimum number of replicas (default: `1` for services, `0` for workers)
- `maxReplicas`: Maximum number of replicas (default: `10`)
- `targetPendingRequests`: Target number of pending HTTP requests (default: `100` for services, `50` for workers)
- `scaledownPeriod`: Cooldown period for scaling down in seconds (default: `300`)
- `scaleupPeriod`: Cooldown period for scaling up in seconds (default: `60` for services, `30` for workers)
- `hosts`: Array of hostnames to monitor for HTTP requests
- `pathPrefixes`: Array of path prefixes to monitor (optional)

### Usage Patterns

**Pattern 1: Web Services with HPA**
```yaml
services:
- name: frontend
  scaling:
    type: hpa
    hpaConfig:
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
```

**Pattern 2: Background Workers with KEDA**
```yaml
workers:
- name: email-worker
  scaling:
    type: keda
    kedaConfig:
      minReplicas: 0
      maxReplicas: 15
      triggers:
      - type: redis
        metadata:
          address: redis://redis:6379/0
          listName: email_queue
          listLength: "5"
```

**Pattern 3: HTTP Request-Based Scaling**
```yaml
services:
- name: api-gateway
  scaling:
    type: keda-http  # HTTP request-based scaling
    httpScaledObjectConfig:
      minReplicas: 2
      maxReplicas: 25
      targetPendingRequests: 75
      scaledownPeriod: 300
      scaleupPeriod: 45
      hosts:
      - "api.example.com"
      pathPrefixes:
      - "/api"
```

**Pattern 4: Mixed Scaling Types**
```yaml
services:
- name: api
  scaling:
    type: hpa  # Resource-based for API
    hpaConfig:
      minReplicas: 3
      maxReplicas: 20
      targetCPUUtilizationPercentage: 60

workers:
- name: processor
  scaling:
    type: keda  # Event-driven for workers
    kedaConfig:
      minReplicas: 0
      maxReplicas: 50
      triggers:
      - type: kafka
        metadata:
          bootstrapServers: kafka:9092
          consumerGroup: processor-group
          topic: events
          lagThreshold: "10"
```

**Pattern 5: Static Scaling**
```yaml
services:
- name: singleton-service
  scaling:
    type: plain
    plainConfig:
      replicas: 1  # Fixed single replica

- name: multi-replica-service
  scaling:
    type: plain
    plainConfig:
      replicas: 5  # Fixed five replicas
```

## Worker Templates

TreeboService supports worker templates to eliminate duplication when deploying similar workers across multiple tenants. Instead of duplicating entire worker configurations, you can define reusable templates and instantiate them with different variables.

### Problem Solved

Previously, deploying the same worker type for different tenants required duplicating the entire worker configuration:

```yaml
# Before: 100 duplicated configurations for 10 workers × 10 tenants
workers:
  - name: worker-tenant-a
    command: ["/app/start_worker.sh", "--tenant", "tenant-a"]
    # ... 50 lines of config
  - name: worker-tenant-b
    command: ["/app/start_worker.sh", "--tenant", "tenant-b"]
    # ... 50 lines of config
  # ... 98 more duplicated configurations
```

### Solution: Template-Based Workers

Define reusable templates and instantiate them with variables:

```yaml
# After: 1 template + 10 variable sets
workerTemplates:
  - templateName: tenant-worker
    type: backend
    enabled: true
    workloadCriticality: application-semi-critical
    image:
      repository: nexus/worker
      tag: "v1.2.3"
    command: ["/app/start_worker.sh", "--tenant", "{{ .Vars.tenantId }}"]
    env:
    - name: TENANT_ID
      value: "{{ .Vars.tenantId }}"
    - name: IS_VIP
      value: "{{ .Vars.isVip | default false }}"
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "300m"

workers:
  # Template-based workers
  - templateName: tenant-worker
    templateVars:
    - tenantId: tenant-a
      isVip: true
    - tenantId: tenant-b
    - tenantId: tenant-c

  # Direct worker configuration (existing approach)
  - name: direct-worker
    type: celery
    enabled: true
    workloadCriticality: application-semi-critical
    image:
      repository: nexus/celery-worker
      tag: "v1.2.3"
    env:
    - name: CELERY_BROKER_URL
      value: redis://redis:6379/0
```

### Template Variables

Template variables are accessible via `{{ .Vars.variableName }}` in:
- `command` arrays
- `args` arrays
- `env` variable values

**Go Template Functions:**
```yaml
env:
- name: IS_VIP
  value: "{{ .Vars.isVip | default false }}"  # Default values
- name: TENANT_NAME
  value: "{{ .Vars.tenantId | upper }}"       # String manipulation
- name: CONFIG_PATH
  value: "{{ if .Vars.isVip }}/vip-config{{ else }}/standard-config{{ end }}"  # Conditional logic
```

### Generated Worker Names

Template-based workers automatically get unique names:
- Template: `tenant-worker`
- Generated names: `tenant-worker-0`, `tenant-worker-1`, `tenant-worker-2`

### Example Output

The configuration above generates these deployments:

1. **tenant-worker-0**:
   - Command: `["/app/start_worker.sh", "--tenant", "tenant-a"]`
   - Env: `TENANT_ID=tenant-a`, `IS_VIP=true`

2. **tenant-worker-1**:
   - Command: `["/app/start_worker.sh", "--tenant", "tenant-b"]`
   - Env: `TENANT_ID=tenant-b`, `IS_VIP=false`

3. **tenant-worker-2**:
   - Command: `["/app/start_worker.sh", "--tenant", "tenant-c"]`
   - Env: `TENANT_ID=tenant-c`, `IS_VIP=false`

4. **direct-worker**:
   - Direct configuration as specified

### Advanced Template Examples

**Multi-Environment Template:**
```yaml
workerTemplates:
  - templateName: api-worker
    type: backend
    command: ["/app/worker", "--env", "{{ .Vars.environment }}", "--region", "{{ .Vars.region }}"]
    env:
    - name: DATABASE_URL
      value: "{{ .Vars.dbUrl }}"
    - name: CACHE_SIZE
      value: "{{ .Vars.cacheSize | default 100 }}"

workers:
  - templateName: api-worker
    templateVars:
    - environment: production
      region: us-east-1
      dbUrl: "postgres://prod-db:5432/app"
      cacheSize: 500
    - environment: staging
      region: us-west-2
      dbUrl: "postgres://staging-db:5432/app"
```

**Conditional Configuration:**
```yaml
workerTemplates:
  - templateName: processor-worker
    type: backend
    command: ["/app/processor"]
    env:
    - name: WORKER_TYPE
      value: "{{ .Vars.workerType }}"
    - name: DEBUG_MODE
      value: "{{ if eq .Vars.environment \"development\" }}true{{ else }}false{{ end }}"
    resources:
      requests:
        memory: "{{ if .Vars.isHighMemory }}1Gi{{ else }}256Mi{{ end }}"
        cpu: "{{ .Vars.cpuRequest | default \"100m\" }}"
```

### Benefits

1. **Reduced Duplication**: Define once, instantiate many times
2. **Easier Maintenance**: Update template to affect all instances
3. **Flexible Variables**: Support for complex templating logic
4. **Type Safety**: JSON schema validation ensures correct structure
5. **Backward Compatible**: No breaking changes to existing configurations

### Usage Patterns

**Pattern 1: Multi-Tenant Applications**
```yaml
# One template for all tenants
workerTemplates:
  - templateName: tenant-processor
    command: ["/app/process", "--tenant", "{{ .Vars.tenantId }}"]

workers:
  - templateName: tenant-processor
    templateVars:
    - tenantId: acme-corp
    - tenantId: globex-inc
    - tenantId: initech-llc
```

**Pattern 2: Environment-Specific Workers**
```yaml
# One template for all environments
workerTemplates:
  - templateName: env-worker
    env:
    - name: ENVIRONMENT
      value: "{{ .Vars.env }}"
    - name: LOG_LEVEL
      value: "{{ if eq .Vars.env \"production\" }}info{{ else }}debug{{ end }}"

workers:
  - templateName: env-worker
    templateVars:
    - env: development
    - env: staging
    - env: production
```

**Pattern 3: Mixed Approach**
```yaml
# Combine templates with direct configuration
workerTemplates:
  - templateName: standard-worker
    # Common configuration

workers:
  # Template-based workers
  - templateName: standard-worker
    templateVars:
    - tenantId: tenant-1
    - tenantId: tenant-2

  # Special worker with direct configuration
  - name: special-worker
    type: custom
    # Unique configuration
```

## WorkloadCriticality

Control node placement and resource allocation:

```yaml
services:
- name: critical-service
  workloadCriticality: application-critical  # High priority, dedicated nodes
  
- name: normal-service
  workloadCriticality: application-semi-critical  # Balanced
  
- name: batch-service
  workloadCriticality: application-non-critical  # Low priority, spot instances
```

### Supported Criticality Levels

- `application-critical`: Highest priority, avoids non-critical nodes
- `application-semi-critical`: Balanced priority and cost
- `application-sub-critical`: Cost-optimized with spot instances
- `application-non-critical`: Maximum cost optimization, lowest priority

## ArgoCD Sync Waves

Automatic sync wave assignment for proper deployment ordering:

- **Migration tasks**: Wave 0 (always first, regardless of dependencies)
- **Other tasks**: Wave 5+ (based on dependencies)
- **Services**: Wave 5+ (based on dependencies)
- **Workers**: Wave 10+ (based on dependencies)

## Examples

See the `examples/` directory for comprehensive configuration examples:

- `volume-examples.yaml` - Complete volume configuration examples
- `flexible-ingress.yaml` - Demonstrates all ingress features
- `workload-criticality.yaml` - Shows workload criticality and node affinity
- `worker-templates.yaml` - Worker template examples for multi-tenant deployments
- `sync-wave-test.yaml` - ArgoCD sync wave examples

## Schema Validation

The chart includes a comprehensive JSON schema (`values.schema.json`) that validates:

- Required fields and data types
- Enum values for criticality levels and path types
- Ingress path configuration
- Resource specifications
- Health check configurations
- Worker template structure and template variables
- Scaling configuration options

## Migration from Old Format

### Old Ingress Format (Deprecated)

```yaml
ingress:
- name: main
  host: app.example.com
  serviceName: frontend  # Direct service reference
  servicePort: 3000
  annotations:
    kubernetes.io/ingress.class: nginx  # Old annotation style
```

### New Flexible Format

```yaml
ingress:
- name: main
  host: app.example.com
  ingressClassName: nginx  # New field
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  tls: true
  paths:  # New paths array
  - path: /
    pathType: Prefix
    serviceName: frontend
    servicePort: 3000
```

## Contributing

When adding new features:

1. Update the JSON schema in `values.schema.json`
2. Add examples to the `examples/` directory
3. Update this README with new configuration options
4. Test with `helm template` to verify output
