{{- range .Values.ingress }}
{{- if .enabled | default true }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: ingress
    treebo.com/ingress-name: {{ .name }}
  {{- if or .annotations $.Values.commonAnnotations }}
  annotations:
    {{- with .annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  ingressClassName: {{ .ingressClassName | default "nginx" }}
  {{- if .tls }}
  tls:
  - hosts:
    - {{ .host }}
    {{- if .tlsSecretName }}
    secretName: {{ .tlsSecretName }}
    {{- else }}
    secretName: {{ $.Values.serviceName }}-{{ .name }}-tls
    {{- end }}
  {{- end }}
  rules:
  - host: {{ .host }}
    http:
      paths:
      {{- if .paths }}
      {{- range .paths }}
      - path: {{ .path | default "/" }}
        pathType: {{ .pathType | default "Prefix" }}
        backend:
          service:
            name: {{ $.Values.serviceName }}-{{ .serviceName }}
            port:
              number: {{ .servicePort }}
      {{- end }}
      {{- else }}
      {{/* Fallback for backward compatibility - use first service if no paths specified */}}
      {{- $firstService := index $.Values.services 0 }}
      {{- if $firstService }}
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ $.Values.serviceName }}-{{ $firstService.name }}
            port:
              number: {{ $firstService.port }}
      {{- end }}
      {{- end }}
{{- end }}
{{- end }}
