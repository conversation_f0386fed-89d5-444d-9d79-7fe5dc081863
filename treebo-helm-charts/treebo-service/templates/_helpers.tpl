{{/*
Expand the name of the chart.
*/}}
{{- define "treebo-service.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
*/}}
{{- define "treebo-service.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "treebo-service.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "treebo-service.labels" -}}
helm.sh/chart: {{ include "treebo-service.chart" . }}
{{ include "treebo-service.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
treebo.com/service: {{ .Values.serviceName }}
treebo.com/environment: {{ .Values.environment }}
{{- with .Values.commonLabels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "treebo-service.selectorLabels" -}}
app.kubernetes.io/name: {{ include "treebo-service.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}



{{/*
Get image repository
*/}}
{{- define "treebo-service.imageRepository" -}}
{{- if and .image .image.repository -}}
{{- .image.repository -}}
{{- else if .Values.image.repository -}}
{{- .Values.image.repository -}}
{{- else -}}
{{- printf "%s/%s" .Values.serviceName .workload.name -}}
{{- end -}}
{{- end }}

{{/*
Get image tag
*/}}
{{- define "treebo-service.imageTag" -}}
{{- if and .image .image.tag -}}
{{- .image.tag -}}
{{- else if .Values.image.tag -}}
{{- .Values.image.tag -}}
{{- else -}}
{{- .Values.environment -}}
{{- end -}}
{{- end }}

{{/*
Get full image name
*/}}
{{- define "treebo-service.image" -}}
{{- $registry := "" -}}
{{- if .image -}}
  {{- $registry = .image.registry | default .Values.image.registry | default .Values.global.imageRegistry -}}
{{- else -}}
  {{- $registry = .Values.image.registry | default .Values.global.imageRegistry -}}
{{- end -}}
{{- $repository := include "treebo-service.imageRepository" . -}}
{{- $tag := include "treebo-service.imageTag" . -}}
{{- if $registry -}}
{{- printf "%s/%s:%s" $registry $repository $tag -}}
{{- else -}}
{{- printf "%s:%s" $repository $tag -}}
{{- end -}}
{{- end }}

{{/*
Get secret name for a secret config
*/}}
{{- define "treebo-service.secretName" -}}
{{- if .secretName -}}
{{- .secretName -}}
{{- else -}}
{{- printf "%s-%s-secrets" $.Values.serviceName .name -}}
{{- end -}}
{{- end }}

{{/*
Get external secret name - use name as-is if present, otherwise construct from serviceName
*/}}
{{- define "treebo-service.externalSecretName" -}}
{{- if .name -}}
{{- .name -}}
{{- else -}}
{{- printf "%s-external-secret-%d" .Values.serviceName .index -}}
{{- end -}}
{{- end }}

{{/*
Resource requirements
*/}}
{{- define "treebo-service.resources" -}}
{{- if .resources }}
resources:
  {{- if .resources.requests }}
  requests:
    {{- if .resources.requests.memory }}
    memory: {{ .resources.requests.memory }}
    {{- end }}
    {{- if .resources.requests.cpu }}
    cpu: {{ .resources.requests.cpu }}
    {{- end }}
  {{- end }}
  {{- if .resources.limits }}
  limits:
    {{- if .resources.limits.memory }}
    memory: {{ .resources.limits.memory }}
    {{- end }}
    {{- if .resources.limits.cpu }}
    cpu: {{ .resources.limits.cpu }}
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}

{{/*
Environment variables
*/}}
{{- define "treebo-service.env" -}}
{{- $globalEnv := .Values.env | default list -}}
{{- $workloadEnv := .workload.env | default list -}}
{{- $allEnv := concat $globalEnv $workloadEnv -}}
{{- if $allEnv }}
env:
{{- range $allEnv }}
- name: {{ .name }}
  {{- if .value }}
  value: {{ .value | quote }}
  {{- else if .valueFrom }}
  valueFrom:
    {{- toYaml .valueFrom | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Environment from (secrets and configmaps)
*/}}
{{- define "treebo-service.envFrom" -}}
{{- $Values := .Values -}}
{{- $workload := .workload -}}
{{- $hasEnvFrom := false -}}

{{/* Check if we have any envFrom sources */}}
{{- if or $Values.envFrom ($workload.envFrom) -}}
{{- $hasEnvFrom = true -}}
{{- end -}}

{{- if $hasEnvFrom }}
envFrom:
{{/* Global envFrom (ConfigMaps and Secrets) */}}
{{- range $Values.envFrom }}
{{- if .configMapRef }}
- configMapRef:
    name: {{ .configMapRef.name }}
    {{- if .configMapRef.optional }}
    optional: {{ .configMapRef.optional }}
    {{- end }}
{{- else if .secretRef }}
- secretRef:
    name: {{ .secretRef.name }}
    {{- if .secretRef.optional }}
    optional: {{ .secretRef.optional }}
    {{- end }}
{{- end }}
{{- end }}

{{/* Workload-specific envFrom (ConfigMaps and Secrets) */}}
{{- if $workload }}
{{- range $workload.envFrom }}
{{- if .configMapRef }}
- configMapRef:
    name: {{ .configMapRef.name }}
    {{- if .configMapRef.optional }}
    optional: {{ .configMapRef.optional }}
    {{- end }}
{{- else if .secretRef }}
- secretRef:
    name: {{ .secretRef.name }}
    {{- if .secretRef.optional }}
    optional: {{ .secretRef.optional }}
    {{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Sync wave annotations for ArgoCD (simplified)
*/}}
{{- define "treebo-service.syncWaveAnnotations" -}}
{{- $workload := .workload -}}
{{- $baseWave := .baseWave | default 5 -}}
{{- $syncWave := $baseWave -}}

{{/* Migration tasks always get wave 0 regardless of dependencies */}}
{{- if and $workload.type (eq $workload.type "migration") -}}
{{- $syncWave = 0 -}}
{{- else if $workload.dependsOn -}}
{{- $syncWave = add $baseWave (len $workload.dependsOn) -}}
{{- end -}}

argocd.argoproj.io/sync-wave: "{{ $syncWave }}"
{{- end }}

{{/*
Node affinity based on workload criticality
*/}}
{{- define "treebo-service.nodeAffinity" -}}
{{- $workloadCriticality := .workloadCriticality | default "application-critical" -}}
nodeAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
  - weight: 100
    preference:
      matchExpressions:
      - key: workload-type
        operator: In
        values:
        - {{ $workloadCriticality }}
      {{- if eq $workloadCriticality "application-critical" }}
      - key: workload-type
        operator: NotIn
        values:
        - application-non-critical
      {{- end }}
{{- end }}

{{/*
Tolerations based on workload criticality
*/}}
{{- define "treebo-service.tolerations" -}}
{{- $workloadCriticality := .workloadCriticality | default "application-critical" -}}
- key: workload-type
  operator: Equal
  value: {{ $workloadCriticality | quote }}
  effect: NoSchedule
{{- end }}

{{/*
ServiceAccount name for a workload
*/}}
{{- define "treebo-service.serviceAccountName" -}}
{{- $workload := .workload -}}
{{- $serviceName := .serviceName -}}

{{- if $workload.serviceAccountName -}}
{{- $workload.serviceAccountName -}}
{{- else -}}
default
{{- end -}}
{{- end }}

{{/*
Get default mount path for volume
*/}}
{{- define "treebo-service.defaultMountPath" -}}
{{- $volumeName := .volumeName -}}
/app/{{ $volumeName }}
{{- end }}

{{/*
Generate access point name for EFS volume
*/}}
{{- define "treebo-service.accessPointName" -}}
{{- $serviceName := .serviceName -}}
{{- $volumeName := .volumeName -}}
{{- printf "%s-%s" $serviceName $volumeName -}}
{{- end }}

{{/*
Process worker templates and expand them into individual workers
This function takes the Values object and returns a list of expanded workers
*/}}
{{- define "treebo-service.expandWorkers" -}}
{{- $Values := .Values -}}
{{- $expandedWorkers := list -}}

{{/* First, add all direct worker configurations */}}
{{- if $Values.workers -}}
{{- range $Values.workers -}}
  {{- if .name -}}
    {{- $expandedWorkers = append $expandedWorkers . -}}
  {{- end -}}
{{- end -}}
{{- end -}}

{{/* Then, process template-based workers */}}
{{- if and $Values.workers $Values.workerTemplates -}}
{{- range $workerIndex, $worker := $Values.workers -}}
  {{- if $worker.templateName -}}
    {{/* Find the matching template */}}
    {{- $template := dict -}}
    {{- range $Values.workerTemplates -}}
      {{- if eq .templateName $worker.templateName -}}
        {{- $template = . -}}
        {{- break -}}
      {{- end -}}
    {{- end -}}

    {{/* If template found, expand it for each templateVars entry */}}
    {{- if $template -}}
      {{- range $index, $vars := $worker.templateVars -}}
        {{/* Create a unique worker name by combining template name with index */}}
        {{- $workerName := printf "%s-%d" $template.templateName $index -}}

        {{/* Start with the template as base */}}
        {{- $worker := deepCopy $template -}}
        {{- $worker = unset $worker "templateName" -}}
        {{- $worker = set $worker "name" $workerName -}}

        {{/* Process template variables in command array */}}
        {{- if $template.command -}}
          {{- $processedCommand := list -}}
          {{- range $template.command -}}
            {{- $processedCmd := tpl . (dict "Values" $Values "Vars" $vars) -}}
            {{- $processedCommand = append $processedCommand $processedCmd -}}
          {{- end -}}
          {{- $worker = set $worker "command" $processedCommand -}}
        {{- end -}}

        {{/* Process template variables in args array */}}
        {{- if $template.args -}}
          {{- $processedArgs := list -}}
          {{- range $template.args -}}
            {{- $processedArg := tpl . (dict "Values" $Values "Vars" $vars) -}}
            {{- $processedArgs = append $processedArgs $processedArg -}}
          {{- end -}}
          {{- $worker = set $worker "args" $processedArgs -}}
        {{- end -}}

        {{/* Process template variables in environment variables */}}
        {{- if $template.env -}}
          {{- $processedEnv := list -}}
          {{- range $template.env -}}
            {{- $envVar := deepCopy . -}}
            {{- if .value -}}
              {{- $processedValue := tpl .value (dict "Values" $Values "Vars" $vars) -}}
              {{- $envVar = set $envVar "value" $processedValue -}}
            {{- end -}}
            {{- $processedEnv = append $processedEnv $envVar -}}
          {{- end -}}
          {{- $worker = set $worker "env" $processedEnv -}}
        {{- end -}}

        {{/* Add the expanded worker to the list */}}
        {{- $expandedWorkers = append $expandedWorkers $worker -}}
      {{- end -}}
    {{- end -}}
  {{- end -}}
{{- end -}}
{{- end -}}

{{/* Store the expanded workers in the root context for reuse */}}
{{- $_ := set . "expandedWorkers" $expandedWorkers -}}

{{/* Return the expanded workers list directly */}}
{{- $expandedWorkers -}}
{{- end }}

{{/*
Generate PVC name for volume
*/}}
{{- define "treebo-service.pvcName" -}}
{{- $serviceName := .serviceName -}}
{{- $workloadName := .workloadName -}}
{{- $volumeName := .volumeName -}}
{{- printf "%s-%s-%s" $serviceName $workloadName $volumeName -}}
{{- end }}

{{/*
Resolve EFS file system ID from alias or direct configuration
*/}}
{{- define "treebo-service.resolveEfsFileSystemId" -}}
{{- $Values := .Values -}}
{{- $volume := .volume -}}
{{- if $volume.efs.fileSystemId -}}
{{/* Direct file system ID specified in volume */}}
{{- $volume.efs.fileSystemId -}}
{{- else if $volume.efs.alias -}}
{{/* Volume-specific alias */}}
{{- $configMap := (lookup "v1" "ConfigMap" "kube-system" "efs-config") -}}
{{- if $configMap -}}
{{- index $configMap.data $volume.efs.alias | default "REPLACE_WITH_EFS_ID" -}}
{{- else -}}
{{- "REPLACE_WITH_EFS_ID" -}}
{{- end -}}
{{- else if and $Values.global.efs $Values.global.efs.fileSystemId -}}
{{/* Global file system ID override (advanced usage) */}}
{{- $Values.global.efs.fileSystemId -}}
{{- else -}}
{{/* Default to "shared" alias */}}
{{- $configMap := (lookup "v1" "ConfigMap" "kube-system" "efs-config") -}}
{{- if $configMap -}}
{{- index $configMap.data "shared" | default "REPLACE_WITH_EFS_ID" -}}
{{- else -}}
{{- "REPLACE_WITH_EFS_ID" -}}
{{- end -}}
{{- end -}}
{{- end }}

{{/*
Generate volumes for a workload
*/}}
{{- define "treebo-service.volumes" -}}
{{- $workload := .workload -}}
{{- $Values := .Values -}}
{{- if $workload.volumes }}
volumes:
{{- range $workload.volumes }}
- name: {{ .name }}
  {{- if eq .type "efs" }}
  csi:
    driver: efs.csi.aws.com
    volumeAttributes:
      {{- if .efs.accessPoint }}
      accessPoint: {{ .efs.accessPoint }}
      {{- else }}
      accessPoint: {{ include "treebo-service.accessPointName" (dict "serviceName" $Values.serviceName "volumeName" .name) }}
      {{- end }}
      fileSystemId: {{ include "treebo-service.resolveEfsFileSystemId" (dict "Values" $Values "volume" .) }}
      path: {{ .efs.path | default "/" }}
  {{- else if eq .type "ebs" }}
  persistentVolumeClaim:
    claimName: {{ include "treebo-service.pvcName" (dict "serviceName" $Values.serviceName "workloadName" $workload.name "volumeName" .name) }}
  {{- else if eq .type "pvc" }}
  persistentVolumeClaim:
    claimName: {{ .pvc.claimName | default (include "treebo-service.pvcName" (dict "serviceName" $Values.serviceName "workloadName" $workload.name "volumeName" .name)) }}
  {{- else if eq .type "configMap" }}
  configMap:
    name: {{ .configMap.name }}
    {{- if .configMap.defaultMode }}
    defaultMode: {{ .configMap.defaultMode }}
    {{- end }}
  {{- else if eq .type "secret" }}
  secret:
    secretName: {{ .secret.secretName }}
    {{- if .secret.defaultMode }}
    defaultMode: {{ .secret.defaultMode }}
    {{- end }}
  {{- else if eq .type "emptyDir" }}
  emptyDir:
    {{- if .emptyDir.sizeLimit }}
    sizeLimit: {{ .emptyDir.sizeLimit }}
    {{- end }}
    {{- if .emptyDir.medium }}
    medium: {{ .emptyDir.medium }}
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Generate volume mounts for a workload
*/}}
{{- define "treebo-service.volumeMounts" -}}
{{- $workload := .workload -}}
{{- if $workload.volumes }}
volumeMounts:
{{- range $workload.volumes }}
- name: {{ .name }}
  mountPath: {{ .mountPath | default (include "treebo-service.defaultMountPath" (dict "volumeName" .name)) }}
  {{- if .subPath }}
  subPath: {{ .subPath }}
  {{- end }}
  {{- if .readOnly }}
  readOnly: {{ .readOnly }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Health check probe configuration
*/}}
{{- define "treebo-service.healthCheck" -}}
{{- $healthCheck := .healthCheck -}}
{{- $defaultPort := .defaultPort -}}
{{- if $healthCheck.enabled }}
{{- if eq $healthCheck.type "httpGet" }}
httpGet:
  path: {{ $healthCheck.path | default "/health" }}
  port: {{ $healthCheck.port | default $defaultPort }}
  scheme: {{ $healthCheck.scheme | default "HTTP" }}
  {{- if $healthCheck.httpHeaders }}
  httpHeaders:
  {{- range $healthCheck.httpHeaders }}
  - name: {{ .name }}
    value: {{ .value }}
  {{- end }}
  {{- end }}
{{- else if eq $healthCheck.type "tcpSocket" }}
tcpSocket:
  port: {{ $healthCheck.port | default $defaultPort }}
{{- else if eq $healthCheck.type "exec" }}
exec:
  command:
  {{- range $healthCheck.command }}
  - {{ . | quote }}
  {{- end }}
{{- end }}
initialDelaySeconds: {{ $healthCheck.initialDelaySeconds | default 30 }}
periodSeconds: {{ $healthCheck.periodSeconds | default 10 }}
timeoutSeconds: {{ $healthCheck.timeoutSeconds | default 5 }}
failureThreshold: {{ $healthCheck.failureThreshold | default 3 }}
{{- if $healthCheck.successThreshold }}
successThreshold: {{ $healthCheck.successThreshold }}
{{- end }}
{{- end }}
{{- end }}
