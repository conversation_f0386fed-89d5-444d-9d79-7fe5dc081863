{{- range .Values.services }}
{{- if .enabled | default true }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
    treebo.com/workload-type: {{ .workloadCriticality | default "application-critical" }}
  {{- $allWorkloads := concat $.Values.services $.Values.workers $.Values.tasks }}
  annotations:
    {{- include "treebo-service.syncWaveAnnotations" (dict "workload" . "allWorkloads" $allWorkloads "baseWave" 10) | nindent 4 }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- if not .scaling }}
  replicas: 1
  {{- else if eq (.scaling.type | default "plain") "plain" }}
  replicas: {{ .scaling.plainConfig.replicas }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "treebo-service.selectorLabels" $ | nindent 6 }}
      treebo.com/component: service
      treebo.com/workload-name: {{ .name }}
  template:
    metadata:
      labels:
        {{- include "treebo-service.labels" $ | nindent 8 }}
        treebo.com/component: service
        treebo.com/workload-name: {{ .name }}
        treebo.com/workload-type: {{ .workloadCriticality | default "application-critical" }}
      {{- with $.Values.commonAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: {{ include "treebo-service.serviceAccountName" (dict "workload" . "serviceName" $.Values.serviceName) }}
      {{- with $.Values.image.pullSecrets | default $.Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .name }}
        image: {{ include "treebo-service.image" (dict "Values" $.Values "workload" . "image" .image) }}
        imagePullPolicy: {{ if .image }}{{ .image.pullPolicy | default $.Values.image.pullPolicy | default "IfNotPresent" }}{{ else }}{{ $.Values.image.pullPolicy | default "IfNotPresent" }}{{ end }}
        {{- if .port }}
        ports:
        - name: http
          containerPort: {{ .port }}
          protocol: TCP
        {{- end }}
        {{- if .healthCheck }}
        {{- if .healthCheck.enabled }}
        livenessProbe:
          {{- include "treebo-service.healthCheck" (dict "healthCheck" .healthCheck "defaultPort" .port) | nindent 10 }}
        readinessProbe:
          {{- $readinessHealthCheck := .healthCheck }}
          {{- if not $readinessHealthCheck.initialDelaySeconds }}
          {{- $_ := set $readinessHealthCheck "initialDelaySeconds" 5 }}
          {{- end }}
          {{- if not $readinessHealthCheck.periodSeconds }}
          {{- $_ := set $readinessHealthCheck "periodSeconds" 5 }}
          {{- end }}
          {{- if not $readinessHealthCheck.timeoutSeconds }}
          {{- $_ := set $readinessHealthCheck "timeoutSeconds" 3 }}
          {{- end }}
          {{- include "treebo-service.healthCheck" (dict "healthCheck" $readinessHealthCheck "defaultPort" .port) | nindent 10 }}
        {{- end }}
        {{- end }}
        {{- include "treebo-service.resources" . | nindent 8 }}
        {{- include "treebo-service.env" (dict "Values" $.Values "workload" .) | nindent 8 }}
        {{- include "treebo-service.envFrom" (dict "Values" $.Values "workload" .) | nindent 8 }}
        {{- if .command }}
        command:
          {{- toYaml .command | nindent 10 }}
        {{- end }}
        {{- if .args }}
        args:
          {{- toYaml .args | nindent 10 }}
        {{- end }}
        {{- with $.Values.podSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- include "treebo-service.volumeMounts" (dict "workload" .) | nindent 8 }}
      {{- with $.Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        {{- include "treebo-service.nodeAffinity" (dict "workloadCriticality" (.workloadCriticality | default "application-critical")) | nindent 8 }}
        {{- with $.Values.affinity }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      tolerations:
        {{- include "treebo-service.tolerations" (dict "workloadCriticality" (.workloadCriticality | default "application-critical")) | nindent 8 }}
        {{- with $.Values.tolerations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- include "treebo-service.volumes" (dict "workload" . "Values" $.Values) | nindent 6 }}
{{- end }}
{{- end }}

{{- $_ := include "treebo-service.expandWorkers" . }}
{{- range .expandedWorkers }}
{{- if .enabled | default true }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: worker
    treebo.com/workload-name: {{ .name }}
    treebo.com/workload-type: {{ .workloadCriticality | default "application-semi-critical" }}
  {{- $allWorkloads := concat $.Values.services $.expandedWorkers $.Values.tasks }}
  annotations:
    {{- include "treebo-service.syncWaveAnnotations" (dict "workload" . "allWorkloads" $allWorkloads "baseWave" 20) | nindent 4 }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- if not .scaling }}
  replicas: 1
  {{- else if eq (.scaling.type | default "plain") "plain" }}
  replicas: {{ .scaling.plainConfig.replicas }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "treebo-service.selectorLabels" $ | nindent 6 }}
      treebo.com/component: worker
      treebo.com/workload-name: {{ .name }}
  template:
    metadata:
      labels:
        {{- include "treebo-service.labels" $ | nindent 8 }}
        treebo.com/component: worker
        treebo.com/workload-name: {{ .name }}
        treebo.com/workload-type: {{ .workloadCriticality | default "application-semi-critical" }}
      {{- with $.Values.commonAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: {{ include "treebo-service.serviceAccountName" (dict "workload" . "serviceName" $.Values.serviceName) }}
      {{- with $.Values.image.pullSecrets | default $.Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .name }}
        image: {{ include "treebo-service.image" (dict "Values" $.Values "workload" . "image" .image) }}
        imagePullPolicy: {{ .image.pullPolicy | default $.Values.image.pullPolicy | default "IfNotPresent" }}
        {{- if .healthCheck }}
        {{- if .healthCheck.enabled }}
        livenessProbe:
          {{- include "treebo-service.healthCheck" (dict "healthCheck" .healthCheck "defaultPort" (.port | default 8080)) | nindent 10 }}
        readinessProbe:
          {{- $readinessHealthCheck := .healthCheck }}
          {{- if not $readinessHealthCheck.initialDelaySeconds }}
          {{- $_ := set $readinessHealthCheck "initialDelaySeconds" 5 }}
          {{- end }}
          {{- if not $readinessHealthCheck.periodSeconds }}
          {{- $_ := set $readinessHealthCheck "periodSeconds" 5 }}
          {{- end }}
          {{- if not $readinessHealthCheck.timeoutSeconds }}
          {{- $_ := set $readinessHealthCheck "timeoutSeconds" 3 }}
          {{- end }}
          {{- include "treebo-service.healthCheck" (dict "healthCheck" $readinessHealthCheck "defaultPort" (.port | default 8080)) | nindent 10 }}
        {{- end }}
        {{- end }}
        {{- include "treebo-service.resources" . | nindent 8 }}
        {{- include "treebo-service.env" (dict "Values" $.Values "workload" .) | nindent 8 }}
        {{- include "treebo-service.envFrom" (dict "Values" $.Values "workload" .) | nindent 8 }}
        {{- if .command }}
        command:
          {{- toYaml .command | nindent 10 }}
        {{- end }}
        {{- if .args }}
        args:
          {{- toYaml .args | nindent 10 }}
        {{- end }}
        {{- with $.Values.podSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- include "treebo-service.volumeMounts" (dict "workload" .) | nindent 8 }}
      {{- with $.Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        {{- include "treebo-service.nodeAffinity" (dict "workloadCriticality" (.workloadCriticality | default "application-semi-critical")) | nindent 8 }}
        {{- with $.Values.affinity }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      tolerations:
        {{- include "treebo-service.tolerations" (dict "workloadCriticality" (.workloadCriticality | default "application-semi-critical")) | nindent 8 }}
        {{- with $.Values.tolerations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- include "treebo-service.volumes" (dict "workload" . "Values" $.Values) | nindent 6 }}
{{- end }}
{{- end }}
