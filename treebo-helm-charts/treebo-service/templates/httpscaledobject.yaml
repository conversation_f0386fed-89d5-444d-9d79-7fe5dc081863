{{- range .Values.services }}
{{- if and (.enabled | default true) .scaling (eq (.scaling.type | default "plain") "keda-http") }}
---
apiVersion: http.keda.sh/v1alpha1
kind: HTTPScaledObject
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-httpscaledobject
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
    treebo.com/scaling-type: keda-http
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    name: {{ $.Values.serviceName }}-{{ .name }}
    service: {{ $.Values.serviceName }}-{{ .name }}
    port: {{ .port | default 8080 }}
  {{- if .scaling.httpScaledObjectConfig.minReplicas }}
  replicas:
    min: {{ .scaling.httpScaledObjectConfig.minReplicas }}
    max: {{ .scaling.httpScaledObjectConfig.maxReplicas | default 10 }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.targetPendingRequests }}
  targetPendingRequests: {{ .scaling.httpScaledObjectConfig.targetPendingRequests }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.scaledownPeriod }}
  scaledownPeriod: {{ .scaling.httpScaledObjectConfig.scaledownPeriod }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.scaleupPeriod }}
  scaleupPeriod: {{ .scaling.httpScaledObjectConfig.scaleupPeriod }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.hosts }}
  hosts:
    {{- toYaml .scaling.httpScaledObjectConfig.hosts | nindent 4 }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.pathPrefixes }}
  pathPrefixes:
    {{- toYaml .scaling.httpScaledObjectConfig.pathPrefixes | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}

{{- $_ := include "treebo-service.expandWorkers" . }}
{{- range .expandedWorkers }}
{{- if and (.enabled | default true) .scaling (eq (.scaling.type | default "plain") "keda-http") }}
---
apiVersion: http.keda.sh/v1alpha1
kind: HTTPScaledObject
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-httpscaledobject
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: worker
    treebo.com/workload-name: {{ .name }}
    treebo.com/scaling-type: keda-http
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    name: {{ $.Values.serviceName }}-{{ .name }}
    service: {{ $.Values.serviceName }}-{{ .name }}
    port: {{ .port | default 8080 }}
  {{- if .scaling.httpScaledObjectConfig.minReplicas }}
  replicas:
    min: {{ .scaling.httpScaledObjectConfig.minReplicas }}
    max: {{ .scaling.httpScaledObjectConfig.maxReplicas | default 10 }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.targetPendingRequests }}
  targetPendingRequests: {{ .scaling.httpScaledObjectConfig.targetPendingRequests }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.scaledownPeriod }}
  scaledownPeriod: {{ .scaling.httpScaledObjectConfig.scaledownPeriod }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.scaleupPeriod }}
  scaleupPeriod: {{ .scaling.httpScaledObjectConfig.scaleupPeriod }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.hosts }}
  hosts:
    {{- toYaml .scaling.httpScaledObjectConfig.hosts | nindent 4 }}
  {{- end }}
  {{- if .scaling.httpScaledObjectConfig.pathPrefixes }}
  pathPrefixes:
    {{- toYaml .scaling.httpScaledObjectConfig.pathPrefixes | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
