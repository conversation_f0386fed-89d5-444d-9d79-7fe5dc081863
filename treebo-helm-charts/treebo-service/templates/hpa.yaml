{{- range .Values.services }}
{{- if and (.enabled | default true) .scaling (eq (.scaling.type | default "plain") "hpa") }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-hpa
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
    treebo.com/scaling-type: hpa
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $.Values.serviceName }}-{{ .name }}
  minReplicas: {{ .scaling.hpaConfig.minReplicas | default 1 }}
  maxReplicas: {{ .scaling.hpaConfig.maxReplicas | default 10 }}
  metrics:
  {{- if .scaling.hpaConfig.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .scaling.hpaConfig.targetCPUUtilizationPercentage }}
  {{- end }}
  {{- if .scaling.hpaConfig.targetMemoryUtilizationPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ .scaling.hpaConfig.targetMemoryUtilizationPercentage }}
  {{- end }}
  {{- if .scaling.hpaConfig.behavior }}
  behavior:
    {{- toYaml .scaling.hpaConfig.behavior | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}

{{- $_ := include "treebo-service.expandWorkers" . }}
{{- range .expandedWorkers }}
{{- if and (.enabled | default true) .scaling (eq (.scaling.type | default "plain") "hpa") }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-hpa
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: worker
    treebo.com/workload-name: {{ .name }}
    treebo.com/scaling-type: hpa
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $.Values.serviceName }}-{{ .name }}
  minReplicas: {{ .scaling.hpaConfig.minReplicas | default 1 }}
  maxReplicas: {{ .scaling.hpaConfig.maxReplicas | default 10 }}
  metrics:
  {{- if .scaling.hpaConfig.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .scaling.hpaConfig.targetCPUUtilizationPercentage }}
  {{- end }}
  {{- if .scaling.hpaConfig.targetMemoryUtilizationPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ .scaling.hpaConfig.targetMemoryUtilizationPercentage }}
  {{- end }}
  {{- if .scaling.hpaConfig.behavior }}
  behavior:
    {{- toYaml .scaling.hpaConfig.behavior | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
