{{- range .Values.services }}
{{- if and (.enabled | default true) .scaling (eq (.scaling.type | default "plain") "keda") }}
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-scaledobject
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
    treebo.com/scaling-type: keda
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    name: {{ $.Values.serviceName }}-{{ .name }}
  {{- if .scaling.kedaConfig.minReplicas }}
  minReplicaCount: {{ .scaling.kedaConfig.minReplicas }}
  {{- end }}
  {{- if .scaling.kedaConfig.maxReplicas }}
  maxReplicaCount: {{ .scaling.kedaConfig.maxReplicas }}
  {{- end }}
  {{- if .scaling.kedaConfig.pollingInterval }}
  pollingInterval: {{ .scaling.kedaConfig.pollingInterval }}
  {{- end }}
  {{- if .scaling.kedaConfig.cooldownPeriod }}
  cooldownPeriod: {{ .scaling.kedaConfig.cooldownPeriod }}
  {{- end }}
  {{- if .scaling.kedaConfig.idleReplicaCount }}
  idleReplicaCount: {{ .scaling.kedaConfig.idleReplicaCount }}
  {{- end }}
  {{- if .scaling.kedaConfig.triggers }}
  triggers:
    {{- toYaml .scaling.kedaConfig.triggers | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}

{{- $_ := include "treebo-service.expandWorkers" . }}
{{- range .expandedWorkers }}
{{- if and (.enabled | default true) .scaling (eq (.scaling.type | default "plain") "keda") }}
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-scaledobject
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: worker
    treebo.com/workload-name: {{ .name }}
    treebo.com/scaling-type: keda
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    name: {{ $.Values.serviceName }}-{{ .name }}
  {{- if .scaling.kedaConfig.minReplicas }}
  minReplicaCount: {{ .scaling.kedaConfig.minReplicas }}
  {{- end }}
  {{- if .scaling.kedaConfig.maxReplicas }}
  maxReplicaCount: {{ .scaling.kedaConfig.maxReplicas }}
  {{- end }}
  {{- if .scaling.kedaConfig.pollingInterval }}
  pollingInterval: {{ .scaling.kedaConfig.pollingInterval }}
  {{- end }}
  {{- if .scaling.kedaConfig.cooldownPeriod }}
  cooldownPeriod: {{ .scaling.kedaConfig.cooldownPeriod }}
  {{- end }}
  {{- if .scaling.kedaConfig.idleReplicaCount }}
  idleReplicaCount: {{ .scaling.kedaConfig.idleReplicaCount }}
  {{- end }}
  {{- if .scaling.kedaConfig.triggers }}
  triggers:
    {{- toYaml .scaling.kedaConfig.triggers | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
