{{- range .Values.services }}
{{- if .enabled | default true }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .serviceType | default "ClusterIP" }}
  ports:
  - port: {{ .port }}
    targetPort: http
    protocol: TCP
    name: http
  selector:
    {{- include "treebo-service.selectorLabels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
{{- end }}
{{- end }}
