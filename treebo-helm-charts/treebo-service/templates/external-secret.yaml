{{- range $index, $secret := .Values.externalSecrets }}
---
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "treebo-service.externalSecretName" (dict "Values" $.Values "name" $secret.name "index" $index) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: external-secret
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  secretStoreRef:
    name: {{ $secret.secretStore }}
    kind: ClusterSecretStore
  target:
    name: {{ $secret.secretName }}
    creationPolicy: Owner
  dataFrom:
  - extract:
      key: {{ $secret.awsSecretPath }}
  {{- if $secret.refreshInterval }}
  refreshInterval: {{ $secret.refreshInterval }}
  {{- end }}
{{- end }}
