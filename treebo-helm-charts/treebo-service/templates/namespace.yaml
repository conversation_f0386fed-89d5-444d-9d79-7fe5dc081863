{{- if .Values.namespace.create }}
apiVersion: v1
kind: Namespace
metadata:
  name: {{ .Values.namespace.name | default .Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" . | nindent 4 }}
    app.kubernetes.io/component: namespace
    treebo.com/managed-by: treebo-service-helm-chart
    goldilocks.fairwinds.com/enabled: "true"
    {{- with .Values.commonLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    helm.sh/resource-policy: keep
    treebo.com/managed-by: treebo-service-helm-chart
    {{- with .Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
