{{- range .Values.tasks }}
{{- if .enabled | default true }}
{{- if eq .type "job" "migration" }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: task
    treebo.com/task-type: {{ .type }}
    treebo.com/workload-name: {{ .name }}
    treebo.com/workload-type: {{ .workloadCriticality | default "application-critical" }}
  {{- $_ := include "treebo-service.expandWorkers" $ }}
  {{- $allWorkloads := concat $.Values.services $.expandedWorkers $.Values.tasks }}
  annotations:
    {{- include "treebo-service.syncWaveAnnotations" (dict "workload" . "allWorkloads" $allWorkloads "baseWave" 5) | nindent 4 }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- if .jobConfig }}
  {{- if .jobConfig.backoffLimit }}
  backoffLimit: {{ .jobConfig.backoffLimit }}
  {{- end }}
  {{- if .jobConfig.activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ .jobConfig.activeDeadlineSeconds }}
  {{- end }}
  {{- if .jobConfig.ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ .jobConfig.ttlSecondsAfterFinished }}
  {{- end }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- include "treebo-service.labels" $ | nindent 8 }}
        treebo.com/component: task
        treebo.com/task-type: {{ .type }}
        treebo.com/workload-name: {{ .name }}
        treebo.com/workload-type: {{ .workloadCriticality | default "application-critical" }}
      {{- with $.Values.commonAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with $.Values.image.pullSecrets | default $.Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $.Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "treebo-service.serviceAccountName" (dict "workload" . "serviceName" $.Values.serviceName) }}
      restartPolicy: {{ .jobConfig.restartPolicy | default "Never" }}
      containers:
      - name: {{ .name }}
        image: {{ include "treebo-service.image" (dict "Values" $.Values "workload" . "image" .image) }}
        imagePullPolicy: {{ .image.pullPolicy | default $.Values.image.pullPolicy | default "IfNotPresent" }}
        {{- include "treebo-service.resources" . | nindent 8 }}
        {{- include "treebo-service.env" (dict "Values" $.Values "workload" .) | nindent 8 }}
        {{- include "treebo-service.envFrom" (dict "Values" $.Values "workload" .) | nindent 8 }}
        {{- if .command }}
        command:
          {{- toYaml .command | nindent 10 }}
        {{- end }}
        {{- if .args }}
        args:
          {{- toYaml .args | nindent 10 }}
        {{- end }}
        {{- with $.Values.podSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- include "treebo-service.volumeMounts" (dict "workload" .) | nindent 8 }}
      {{- with $.Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        {{- include "treebo-service.nodeAffinity" (dict "workloadCriticality" (.workloadCriticality | default "application-critical")) | nindent 8 }}
        {{- with $.Values.affinity }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      tolerations:
        {{- include "treebo-service.tolerations" (dict "workloadCriticality" (.workloadCriticality | default "application-critical")) | nindent 8 }}
        {{- with $.Values.tolerations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- include "treebo-service.volumes" (dict "workload" . "Values" $.Values) | nindent 6 }}
{{- else if eq .type "cronjob" }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: task
    treebo.com/task-type: {{ .type }}
    treebo.com/workload-name: {{ .name }}
    treebo.com/workload-type: {{ .workloadCriticality | default "application-critical" }}
  {{- $_ := include "treebo-service.expandWorkers" $ }}
  {{- $allWorkloads := concat $.Values.services $.expandedWorkers $.Values.tasks }}
  annotations:
    {{- include "treebo-service.syncWaveAnnotations" (dict "workload" . "allWorkloads" $allWorkloads "baseWave" 5) | nindent 4 }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  schedule: {{ .cronJobConfig.schedule | quote }}
  {{- if .cronJobConfig.timeZone }}
  timeZone: {{ .cronJobConfig.timeZone | quote }}
  {{- end }}
  {{- if .cronJobConfig.concurrencyPolicy }}
  concurrencyPolicy: {{ .cronJobConfig.concurrencyPolicy }}
  {{- end }}
  {{- if .cronJobConfig.successfulJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .cronJobConfig.successfulJobsHistoryLimit }}
  {{- end }}
  {{- if .cronJobConfig.failedJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ .cronJobConfig.failedJobsHistoryLimit }}
  {{- end }}
  {{- if .cronJobConfig.startingDeadlineSeconds }}
  startingDeadlineSeconds: {{ .cronJobConfig.startingDeadlineSeconds }}
  {{- end }}
  jobTemplate:
    spec:
      {{- if .jobConfig }}
      {{- if .jobConfig.backoffLimit }}
      backoffLimit: {{ .jobConfig.backoffLimit }}
      {{- end }}
      {{- if .jobConfig.activeDeadlineSeconds }}
      activeDeadlineSeconds: {{ .jobConfig.activeDeadlineSeconds }}
      {{- end }}
      {{- if .jobConfig.ttlSecondsAfterFinished }}
      ttlSecondsAfterFinished: {{ .jobConfig.ttlSecondsAfterFinished }}
      {{- end }}
      {{- end }}
      template:
        metadata:
          labels:
            {{- include "treebo-service.labels" $ | nindent 12 }}
            treebo.com/component: task
            treebo.com/task-type: {{ .type }}
            treebo.com/workload-name: {{ .name }}
            treebo.com/workload-type: {{ .workloadCriticality | default "application-critical" }}
          {{- with $.Values.commonAnnotations }}
          annotations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        spec:
          {{- with $.Values.image.pullSecrets | default $.Values.global.imagePullSecrets }}
          imagePullSecrets:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $.Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          serviceAccountName: {{ include "treebo-service.serviceAccountName" (dict "workload" . "serviceName" $.Values.serviceName) }}
          restartPolicy: {{ .jobConfig.restartPolicy | default "Never" }}
          containers:
          - name: {{ .name }}
            image: {{ include "treebo-service.image" (dict "Values" $.Values "workload" . "image" .image) }}
            imagePullPolicy: {{ .image.pullPolicy | default $.Values.image.pullPolicy | default "IfNotPresent" }}
            {{- include "treebo-service.resources" . | nindent 12 }}
            {{- include "treebo-service.env" (dict "Values" $.Values "workload" .) | nindent 12 }}
            {{- include "treebo-service.envFrom" (dict "Values" $.Values "workload" .) | nindent 12 }}
            {{- if .command }}
            command:
              {{- toYaml .command | nindent 14 }}
            {{- end }}
            {{- if .args }}
            args:
              {{- toYaml .args | nindent 14 }}
            {{- end }}
            {{- with $.Values.podSecurityContext }}
            securityContext:
              {{- toYaml . | nindent 14 }}
            {{- end }}
            {{- include "treebo-service.volumeMounts" (dict "workload" .) | nindent 12 }}
          {{- with $.Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          affinity:
            {{- include "treebo-service.nodeAffinity" (dict "workloadCriticality" (.workloadCriticality | default "application-critical")) | nindent 12 }}
            {{- with $.Values.affinity }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          tolerations:
            {{- include "treebo-service.tolerations" (dict "workloadCriticality" (.workloadCriticality | default "application-critical")) | nindent 12 }}
            {{- with $.Values.tolerations }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- include "treebo-service.volumes" (dict "workload" . "Values" $.Values) | nindent 10 }}
{{- end }}
{{- end }}
{{- end }}
