{{- range .Values.services }}
{{- if and (.enabled | default true) .vpa .vpa.enabled }}
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-vpa
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: service
    treebo.com/workload-name: {{ .name }}
    treebo.com/vpa-enabled: "true"
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $.Values.serviceName }}-{{ .name }}
  updatePolicy:
    updateMode: {{ .vpa.updateMode | default "Off" }}
  {{- if .vpa.resourcePolicy }}
  resourcePolicy:
    {{- if .vpa.resourcePolicy.containerPolicies }}
    containerPolicies:
    {{- range .vpa.resourcePolicy.containerPolicies }}
    - containerName: {{ .containerName | default $.name }}
      {{- if .minAllowed }}
      minAllowed:
        {{- toYaml .minAllowed | nindent 8 }}
      {{- end }}
      {{- if .maxAllowed }}
      maxAllowed:
        {{- toYaml .maxAllowed | nindent 8 }}
      {{- end }}
      {{- if .controlledResources }}
      controlledResources:
        {{- toYaml .controlledResources | nindent 8 }}
      {{- end }}
      {{- if .controlledValues }}
      controlledValues: {{ .controlledValues }}
      {{- end }}
    {{- end }}
    {{- else }}
    {{- /* Default container policy if none specified */}}
    containerPolicies:
    - containerName: {{ .name }}
      {{- if .vpa.minAllowed }}
      minAllowed:
        {{- toYaml .vpa.minAllowed | nindent 8 }}
      {{- end }}
      {{- if .vpa.maxAllowed }}
      maxAllowed:
        {{- toYaml .vpa.maxAllowed | nindent 8 }}
      {{- end }}
      controlledResources: ["cpu", "memory"]
      controlledValues: "RequestsAndLimits"
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}

{{- $_ := include "treebo-service.expandWorkers" . }}
{{- range .expandedWorkers }}
{{- if and (.enabled | default true) .vpa .vpa.enabled }}
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ $.Values.serviceName }}-{{ .name }}-vpa
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: worker
    treebo.com/workload-name: {{ .name }}
    treebo.com/vpa-enabled: "true"
  {{- with $.Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $.Values.serviceName }}-{{ .name }}
  updatePolicy:
    updateMode: {{ .vpa.updateMode | default "Off" }}
  {{- if .vpa.resourcePolicy }}
  resourcePolicy:
    {{- if .vpa.resourcePolicy.containerPolicies }}
    containerPolicies:
    {{- range .vpa.resourcePolicy.containerPolicies }}
    - containerName: {{ .containerName | default $.name }}
      {{- if .minAllowed }}
      minAllowed:
        {{- toYaml .minAllowed | nindent 8 }}
      {{- end }}
      {{- if .maxAllowed }}
      maxAllowed:
        {{- toYaml .maxAllowed | nindent 8 }}
      {{- end }}
      {{- if .controlledResources }}
      controlledResources:
        {{- toYaml .controlledResources | nindent 8 }}
      {{- end }}
      {{- if .controlledValues }}
      controlledValues: {{ .controlledValues }}
      {{- end }}
    {{- end }}
    {{- else }}
    {{- /* Default container policy if none specified */}}
    containerPolicies:
    - containerName: {{ .name }}
      {{- if .vpa.minAllowed }}
      minAllowed:
        {{- toYaml .vpa.minAllowed | nindent 8 }}
      {{- end }}
      {{- if .vpa.maxAllowed }}
      maxAllowed:
        {{- toYaml .vpa.maxAllowed | nindent 8 }}
      {{- end }}
      controlledResources: ["cpu", "memory"]
      controlledValues: "RequestsAndLimits"
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}
