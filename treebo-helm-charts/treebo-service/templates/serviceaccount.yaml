{{- range .Values.serviceAccounts }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: serviceaccount
    {{- with .labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if or .annotations $.Values.commonAnnotations }}
  annotations:
    {{- with .annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
automountServiceAccountToken: {{ .automountServiceAccountToken | default true }}
{{- end }}
