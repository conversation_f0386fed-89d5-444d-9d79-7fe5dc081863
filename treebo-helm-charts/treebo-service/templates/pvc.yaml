{{/*
Generate PVCs for volume definitions
*/}}

{{/* Services PVCs */}}
{{- range .Values.services }}
{{- $serviceName := .name }}
{{- if .volumes }}
{{- range .volumes }}
{{- if or (eq .type "pvc") (eq .type "ebs") }}
{{- if not .pvc.claimName }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "treebo-service.pvcName" (dict "serviceName" $.Values.serviceName "workloadName" $serviceName "volumeName" .name) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: storage
    treebo.com/workload-name: {{ $serviceName }}
    treebo.com/volume-name: {{ .name }}
    treebo.com/volume-type: {{ .type }}
  {{- if eq .type "pvc" }}
  {{- if and (eq (.pvc.storageClass | default "efs-sc") "efs-sc") (not .pvc.claimName) }}
  annotations:
    treebo.com/auto-access-point: {{ include "treebo-service.accessPointName" (dict "serviceName" $.Values.serviceName "volumeName" .name) }}
    {{- if .pvc.performance }}
    treebo.com/performance-mode: "true"
    {{- end }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- end }}
spec:
  accessModes:
    {{- if .pvc.accessModes }}
    {{- toYaml .pvc.accessModes | nindent 4 }}
    {{- else if eq .type "ebs" }}
    - ReadWriteOnce
    {{- else }}
    - ReadWriteMany
    {{- end }}
  storageClassName: {{ if eq .type "ebs" }}{{ .ebs.storageClass | default "ebs-sc" }}{{ else if .pvc.performance }}efs-performance-sc{{ else }}{{ .pvc.storageClass | default "efs-sc" }}{{ end }}
  resources:
    requests:
      storage: {{ .pvc.size | default "10Gi" }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/* Workers PVCs */}}
{{- range .Values.workers }}
{{- $workerName := .name }}
{{- if .volumes }}
{{- range .volumes }}
{{- if or (eq .type "pvc") (eq .type "ebs") }}
{{- if not .pvc.claimName }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "treebo-service.pvcName" (dict "serviceName" $.Values.serviceName "workloadName" $workerName "volumeName" .name) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: storage
    treebo.com/workload-name: {{ $workerName }}
    treebo.com/volume-name: {{ .name }}
    treebo.com/volume-type: {{ .type }}
  {{- if eq .type "pvc" }}
  {{- if and (eq (.pvc.storageClass | default "efs-sc") "efs-sc") (not .pvc.claimName) }}
  annotations:
    treebo.com/auto-access-point: {{ include "treebo-service.accessPointName" (dict "serviceName" $.Values.serviceName "volumeName" .name) }}
    {{- if .pvc.performance }}
    treebo.com/performance-mode: "true"
    {{- end }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- end }}
spec:
  accessModes:
    {{- if .pvc.accessModes }}
    {{- toYaml .pvc.accessModes | nindent 4 }}
    {{- else if eq .type "ebs" }}
    - ReadWriteOnce
    {{- else }}
    - ReadWriteMany
    {{- end }}
  storageClassName: {{ if eq .type "ebs" }}{{ .ebs.storageClass | default "ebs-sc" }}{{ else if .pvc.performance }}efs-performance-sc{{ else }}{{ .pvc.storageClass | default "efs-sc" }}{{ end }}
  resources:
    requests:
      storage: {{ .pvc.size | default "10Gi" }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/* Tasks PVCs */}}
{{- range .Values.tasks }}
{{- $taskName := .name }}
{{- if .volumes }}
{{- range .volumes }}
{{- if or (eq .type "pvc") (eq .type "ebs") }}
{{- if not .pvc.claimName }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "treebo-service.pvcName" (dict "serviceName" $.Values.serviceName "workloadName" $taskName "volumeName" .name) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "treebo-service.labels" $ | nindent 4 }}
    treebo.com/component: storage
    treebo.com/workload-name: {{ $taskName }}
    treebo.com/volume-name: {{ .name }}
    treebo.com/volume-type: {{ .type }}
  {{- if eq .type "pvc" }}
  {{- if and (eq (.pvc.storageClass | default "efs-sc") "efs-sc") (not .pvc.claimName) }}
  annotations:
    treebo.com/auto-access-point: {{ include "treebo-service.accessPointName" (dict "serviceName" $.Values.serviceName "volumeName" .name) }}
    {{- if .pvc.performance }}
    treebo.com/performance-mode: "true"
    {{- end }}
    {{- with $.Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- end }}
spec:
  accessModes:
    {{- if .pvc.accessModes }}
    {{- toYaml .pvc.accessModes | nindent 4 }}
    {{- else if eq .type "ebs" }}
    - ReadWriteOnce
    {{- else }}
    - ReadWriteMany
    {{- end }}
  storageClassName: {{ if eq .type "ebs" }}{{ .ebs.storageClass | default "ebs-sc" }}{{ else if .pvc.performance }}efs-performance-sc{{ else }}{{ .pvc.storageClass | default "efs-sc" }}{{ end }}
  resources:
    requests:
      storage: {{ .pvc.size | default "10Gi" }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
