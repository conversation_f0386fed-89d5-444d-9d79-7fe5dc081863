# Default values for treebo-service
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []

# Service configuration
serviceName: ""
environment: staging # staging | production

# Namespace configuration
namespace:
  create: false # Set to true to create namespace
  name: "" # Optional: override namespace name (defaults to Release.Namespace)

# Image configuration
image:
  registry: ""
  repository: ""
  tag: ""
  pullPolicy: IfNotPresent
  pullSecrets: []

# Services (required)
services: []
# - name: frontend
#   type: web
#   port: 3000
#   enabled: true
#   workloadCriticality: application-critical
#   image:
#     repository: nexus/frontend
#     tag: "v1.2.3"
#   env:
#   - name: NODE_ENV
#     value: production
#   envFrom:
#   - configMapRef:
#       name: frontend-config
#   - secretRef:
#       name: frontend-secrets
#   resources:
#     requests:
#       memory: "256Mi"
#       cpu: "100m"
#     limits:
#       memory: "512Mi"
#       cpu: "500m"
#   healthCheck:
#     enabled: true
#     type: httpGet          # httpGet, tcpSocket, or exec
#     path: /health          # For httpGet
#     port: 3000             # For httpGet and tcpSocket
#     scheme: HTTP           # For httpGet (HTTP or HTTPS)
#     httpHeaders:           # For httpGet (optional)
#     - name: Custom-Header
#       value: custom-value
#     # command: ["cat", "/tmp/healthy"]  # For exec
#     initialDelaySeconds: 30
#     periodSeconds: 10
#     timeoutSeconds: 5
#     failureThreshold: 3
#     successThreshold: 1
#   scaling:
#     enabled: true
#     type: hpa  # Options: hpa, keda, keda-http
#     replicas: 2  # Used when scaling.enabled is false
#     # HPA Configuration (when type: hpa)
#     hpaConfig:
#       minReplicas: 2
#       maxReplicas: 10
#       targetCPUUtilizationPercentage: 70
#       targetMemoryUtilizationPercentage: 80
#       behavior:  # Optional: scaling behavior configuration
#         scaleDown:
#           stabilizationWindowSeconds: 300
#   # VPA Configuration (optional)
#   vpa:
#     enabled: false  # Enable VPA for this workload
#     updateMode: "Off"  # Options: Off (recommendations only), Auto (automatic updates)
#     minAllowed:  # Optional: minimum resource constraints
#       cpu: 50m
#       memory: 64Mi
#     maxAllowed:  # Optional: maximum resource constraints
#       cpu: 2000m
#       memory: 4Gi
#           policies:
#           - type: Percent
#             value: 10
#             periodSeconds: 60
#         scaleUp:
#           stabilizationWindowSeconds: 0
#           policies:
#           - type: Percent
#             value: 100
#             periodSeconds: 15
#           - type: Pods
#             value: 4
#             periodSeconds: 15
#           selectPolicy: Max
#     # KEDA Configuration (when type: keda)
#     kedaConfig:
#       minReplicas: 0
#       maxReplicas: 20
#       pollingInterval: 30
#       cooldownPeriod: 300
#       idleReplicaCount: 0
#       triggers:
#       - type: cpu
#         metricType: Utilization
#         metadata:
#           value: "70"
#       - type: memory
#         metricType: Utilization
#         metadata:
#           value: "80"
#     # KEDA HTTP Configuration (when type: keda-http)
#     httpScaledObjectConfig:
#       minReplicas: 1
#       maxReplicas: 10
#       targetPendingRequests: 100
#       scaledownPeriod: 300
#       scaleupPeriod: 60
#       hosts:
#       - "api.example.com"
#       pathPrefixes:
#       - "/api"
#   serviceAccountName: frontend-sa  # Reference to ServiceAccount defined above
#
#   # Volume examples:
#   volumes:
#   - name: uploads
#     type: pvc
#     pvc:
#       size: 500Gi
#       storageClass: efs-sc  # Uses "shared" EFS alias by default
#     mountPath: /app/uploads
#   - name: high-perf-data
#     type: efs
#     efs:
#       alias: "high-performance"  # Override for specific volume
#     mountPath: /app/data
#   - name: config
#     type: configMap
#     configMap:
#       name: app-config
#     mountPath: /etc/config
#     readOnly: true
#   - name: temp
#     type: emptyDir
#     emptyDir:
#       sizeLimit: 1Gi
#     mountPath: /tmp

# Worker Templates (optional)
# Define reusable worker templates that can be instantiated with different variables
workerTemplates: []
# - templateName: tenant-worker-template
#   type: backend
#   enabled: true
#   workloadCriticality: application-semi-critical
#   image:
#     repository: nexus/worker
#     tag: "v1.2.3"
#   command: ["/app/start_worker.sh", "--tenant", "{{ .Vars.tenantId }}"]
#   env:
#   - name: TENANT_ID
#     value: "{{ .Vars.tenantId }}"
#   - name: IS_VIP
#     value: "{{ .Vars.isVip | default false }}"
#   resources:
#     requests:
#       memory: "256Mi"
#       cpu: "100m"
#     limits:
#       memory: "512Mi"
#       cpu: "300m"

# Workers (optional)
workers: []
# Template-based worker configuration:
# - templateName: tenant-worker-template
#   templateVars:
#   - tenantId: tenant-a
#     isVip: true
#   - tenantId: tenant-b
#   - tenantId: tenant-c
#
# Direct worker configuration (existing approach):
# - name: celery-worker
#   type: celery
#   enabled: true
#   workloadCriticality: application-semi-critical
#   image:
#     repository: nexus/worker
#     tag: "v1.2.3"
#   env:
#   - name: CELERY_BROKER_URL
#     value: redis://redis:6379/0
#   resources:
#     requests:
#       memory: "256Mi"
#       cpu: "100m"
#     limits:
#       memory: "512Mi"
#       cpu: "300m"
#   # VPA Configuration (optional)
#   vpa:
#     enabled: false  # Enable VPA for this workload
#     updateMode: "Auto"  # Options: Off (recommendations only), Auto (automatic updates)
#     minAllowed:  # Optional: minimum resource constraints
#       cpu: 25m
#       memory: 32Mi
#     maxAllowed:  # Optional: maximum resource constraints
#       cpu: 1000m
#       memory: 2Gi
#   scaling:
#     enabled: true
#     type: keda  # Options: hpa, keda, keda-http (workers typically use KEDA)
#     replicas: 1  # Used when scaling.enabled is false
#     # HPA Configuration (when type: hpa)
#     hpaConfig:
#       minReplicas: 1
#       maxReplicas: 5
#       targetCPUUtilizationPercentage: 70
#       targetMemoryUtilizationPercentage: 80
#     # KEDA Configuration (when type: keda)
#     kedaConfig:
#       minReplicas: 0
#       maxReplicas: 10
#       pollingInterval: 30
#       cooldownPeriod: 300
#       idleReplicaCount: 0
#       triggers:
#       - type: redis
#         metadata:
#           address: redis://redis:6379/0
#           listName: celery
#           listLength: "5"
#     # KEDA HTTP Configuration (when type: keda-http)
#     httpScaledObjectConfig:
#       minReplicas: 0
#       maxReplicas: 20
#       targetPendingRequests: 50
#       scaledownPeriod: 300
#       scaleupPeriod: 30
#       hosts:
#       - "worker-api.example.com"
#       pathPrefixes:
#       - "/worker"
#   serviceAccountName: worker-sa  # Reference to ServiceAccount defined above
#
#   # Volume examples for workers:
#   volumes:
#   - name: workspace
#     type: pvc
#     pvc:
#       size: 1Ti
#       storageClass: efs-sc
#       performance: true  # Use high-performance EFS
#     mountPath: /app/workspace
#   - name: temp
#     type: pvc
#     pvc:
#       size: 100Gi
#       storageClass: efs-sc
#     mountPath: /tmp

# Tasks (optional) - Jobs, CronJobs, and Migrations
tasks: []
# - name: migration
#   type: migration
#   enabled: true
#   workloadCriticality: application-critical
#   dependsOn: []  # Run before services start
#   image:
#     repository: nexus/migration
#     tag: "v1.2.3"
#   command: ["python", "manage.py", "migrate"]
#   jobConfig:
#     backoffLimit: 3
#     ttlSecondsAfterFinished: 300
#     restartPolicy: Never
#   serviceAccountName: api-sa  # Reference to ServiceAccount defined above
#
#   # Volume examples for tasks:
#   volumes:
#   - name: data
#     type: pvc
#     pvc:
#       size: 200Gi
#       storageClass: efs-sc
#     mountPath: /app/data
#   - name: logs
#     type: pvc
#     pvc:
#       size: 50Gi
#       storageClass: efs-sc
#     mountPath: /app/logs
#
# - name: data-sync
#   type: job
#   enabled: true
#   workloadCriticality: application-sub-critical
#   image:
#     repository: nexus/data-sync
#     tag: "v1.2.3"
#   jobConfig:
#     backoffLimit: 6
#     activeDeadlineSeconds: 3600
#     ttlSecondsAfterFinished: 86400
#
# - name: cleanup
#   type: cronjob
#   enabled: true
#   workloadCriticality: application-non-critical
#   image:
#     repository: nexus/cleanup
#     tag: "v1.2.3"
#   cronJobConfig:
#     schedule: "0 2 * * *"  # Daily at 2 AM
#     timeZone: "UTC"
#     concurrencyPolicy: Forbid
#     successfulJobsHistoryLimit: 3
#     failedJobsHistoryLimit: 1

# External Secrets configuration using External Secrets Operator
externalSecrets: []
# - name: app-config
#   secretStore: aws-secret-store
#   awsSecretPath: /nexus/production/config
#   refreshInterval: "1h"
#   secretName: ""  # Optional custom secret name

# Global environment variables
env: []
# - name: LOG_LEVEL
#   value: info

# Global environment variables from ConfigMaps and Secrets
envFrom: []
# - configMapRef:
#     name: app-config
#     optional: false
# - secretRef:
#     name: app-secrets
#     optional: false

# ServiceAccount definitions
serviceAccounts: []
# - name: frontend-sa
#   annotations:
#     eks.amazonaws.com/role-arn: arn:aws:iam::************:role/frontend-role
#     iam.gke.io/gcp-service-account: <EMAIL>
#   labels:
#     app.kubernetes.io/component: frontend
#   automountServiceAccountToken: true
# - name: api-sa
#   annotations:
#     eks.amazonaws.com/role-arn: arn:aws:iam::************:role/api-role
#   automountServiceAccountToken: true
# - name: worker-sa
#   annotations:
#     eks.amazonaws.com/role-arn: arn:aws:iam::************:role/worker-role
#   automountServiceAccountToken: true

# Ingress configuration
ingress: []
# - name: main
#   enabled: true
#   host: nexus.treebo.com
#   ingressClassName: nginx  # Default: nginx
#   annotations:
#     cert-manager.io/cluster-issuer: letsencrypt-prod
#     nginx.ingress.kubernetes.io/ssl-redirect: "true"
#     nginx.ingress.kubernetes.io/rate-limit: "100"
#   tls: true
#   tlsSecretName: nexus-tls  # Optional, defaults to auto-generated
#   paths:
#   - path: /
#     pathType: Prefix  # Exact, Prefix, ImplementationSpecific
#     serviceName: frontend
#     servicePort: 3000
#   - path: /api
#     pathType: Prefix
#     serviceName: backend-service
#     servicePort: 8000
#   - path: /mcp
#     pathType: Prefix
#     serviceName: mcp-service
#     servicePort: 8080
#
# - name: api-only
#   enabled: true
#   host: api.nexus.treebo.com
#   ingressClassName: nginx
#   annotations:
#     cert-manager.io/cluster-issuer: letsencrypt-prod
#     nginx.ingress.kubernetes.io/rate-limit: "200"
#   tls: true
#   paths:
#   - path: /
#     pathType: Prefix
#     serviceName: backend-service
#     servicePort: 8000

# Monitoring configuration
monitoring:
  enabled: false
  serviceMonitor:
    enabled: false
  prometheusRule:
    enabled: false

# Common labels applied to all resources
commonLabels: {}

# Common annotations applied to all resources
commonAnnotations: {}

# Node selector for all workloads
nodeSelector: {}

# Tolerations for all workloads
tolerations: []

# Affinity for all workloads
affinity: {}
# Security context
# securityContext:
#   runAsNonRoot: true
#   runAsUser: 1000
#   fsGroup: 2000

# Pod security context
# podSecurityContext:
#   fsGroup: 2000
