{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "title": "TreeboService Helm Chart Values Schema", "required": ["serviceName", "environment", "services"], "properties": {"serviceName": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Name of the Treebo service"}, "environment": {"type": "string", "enum": ["staging", "production"], "description": "Deployment environment"}, "namespace": {"type": "object", "description": "Namespace configuration", "properties": {"create": {"type": "boolean", "default": false, "description": "Whether to create the namespace"}, "name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Name of the namespace (defaults to Release.Namespace if not specified)"}}}, "serviceAccounts": {"type": "array", "description": "ServiceAccount definitions", "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Name of the ServiceAccount"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Annotations for the ServiceAccount"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Additional labels for the ServiceAccount"}, "automountServiceAccountToken": {"type": "boolean", "default": true, "description": "Whether to automount the ServiceAccount token"}}}}, "services": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["name", "type", "port"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"}, "type": {"type": "string"}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}, "enabled": {"type": "boolean", "default": true}, "workloadCriticality": {"type": "string", "enum": ["application-critical", "application-semi-critical", "application-sub-critical", "application-non-critical"], "default": "application-semi-critical"}, "image": {"$ref": "#/definitions/imageSpec"}, "command": {"type": "array", "items": {"type": "string"}}, "args": {"type": "array", "items": {"type": "string"}}, "env": {"$ref": "#/definitions/envSpec"}, "envFrom": {"$ref": "#/definitions/envFromSpec"}, "resources": {"type": "object", "properties": {"requests": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "limits": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}}}, "vpa": {"type": "object", "description": "Vertical Pod Autoscaler configuration", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Enable VPA for this workload"}, "updateMode": {"type": "string", "enum": ["Off", "Auto", "Initial"], "default": "Off", "description": "VPA update mode: Off (recommendations only), Auto (automatic updates), Initial (update on pod creation)"}, "minAllowed": {"type": "object", "description": "Minimum allowed resource constraints", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "maxAllowed": {"type": "object", "description": "Maximum allowed resource constraints", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "resourcePolicy": {"type": "object", "description": "Advanced VPA resource policy configuration", "properties": {"containerPolicies": {"type": "array", "items": {"type": "object", "properties": {"containerName": {"type": "string"}, "minAllowed": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "maxAllowed": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "controlledResources": {"type": "array", "items": {"type": "string"}}, "controlledValues": {"type": "string", "enum": ["RequestsAndLimits", "RequestsOnly"]}}}}}}}}, "healthCheck": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "type": {"type": "string", "enum": ["httpGet", "tcpSocket", "exec"], "default": "httpGet", "description": "Type of health check: httpGet, tcpSocket, or exec"}, "path": {"type": "string", "default": "/health", "description": "HTTP path for httpGet health checks"}, "port": {"type": "integer", "description": "Port for httpGet and tcpSocket health checks"}, "command": {"type": "array", "items": {"type": "string"}, "description": "Command to run for exec health checks"}, "scheme": {"type": "string", "enum": ["HTTP", "HTTPS"], "default": "HTTP", "description": "Scheme for httpGet health checks"}, "httpHeaders": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"]}, "description": "HTTP headers for httpGet health checks"}, "initialDelaySeconds": {"type": "integer", "default": 30}, "periodSeconds": {"type": "integer", "default": 10}, "timeoutSeconds": {"type": "integer", "default": 5}, "failureThreshold": {"type": "integer", "default": 3}, "successThreshold": {"type": "integer", "default": 1}}, "anyOf": [{"properties": {"enabled": {"const": false}}}, {"properties": {"enabled": {"const": true}, "type": {"const": "httpGet"}}, "required": ["path"]}, {"properties": {"enabled": {"const": true}, "type": {"const": "tcpSocket"}}, "required": ["port"]}, {"properties": {"enabled": {"const": true}, "type": {"const": "exec"}}, "required": ["command"]}]}, "scaling": {"type": "object", "properties": {"type": {"type": "string", "enum": ["plain", "hpa", "keda", "keda-http"], "default": "plain", "description": "Type of scaling to use: plain for static replicas, hpa for HorizontalPodAutoscaler, keda for KEDA ScaledObject, keda-http for KEDA HTTPScaledObject"}, "replicas": {"type": "integer", "default": 1, "description": "Number of replicas when no autoscaling is configured"}, "plainConfig": {"type": "object", "description": "Plain scaling configuration (used when type is 'plain')", "properties": {"replicas": {"type": "integer", "default": 1, "minimum": 0, "description": "Number of replicas for static scaling"}}}, "hpaConfig": {"type": "object", "description": "HPA configuration (used when type is 'hpa')", "properties": {"minReplicas": {"type": "integer", "default": 1, "minimum": 1}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "targetCPUUtilizationPercentage": {"type": "integer", "default": 70, "minimum": 1, "maximum": 100}, "targetMemoryUtilizationPercentage": {"type": "integer", "minimum": 1, "maximum": 100}, "behavior": {"type": "object", "description": "HPA scaling behavior configuration", "properties": {"scaleDown": {"type": "object"}, "scaleUp": {"type": "object"}}}}}, "kedaConfig": {"type": "object", "description": "KEDA configuration (used when type is 'keda')", "properties": {"minReplicas": {"type": "integer", "default": 0, "minimum": 0}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "pollingInterval": {"type": "integer", "default": 30, "minimum": 1}, "cooldownPeriod": {"type": "integer", "default": 300, "minimum": 0}, "idleReplicaCount": {"type": "integer", "default": 0, "minimum": 0}, "triggers": {"type": "array", "description": "KEDA scaling triggers", "items": {"type": "object", "properties": {"type": {"type": "string"}, "metadata": {"type": "object"}, "metricType": {"type": "string"}, "authenticationRef": {"type": "object"}}, "required": ["type"]}}}}, "httpScaledObjectConfig": {"type": "object", "description": "KEDA HTTP configuration (used when type is 'keda-http')", "properties": {"minReplicas": {"type": "integer", "default": 1, "minimum": 0}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "targetPendingRequests": {"type": "integer", "default": 100, "minimum": 1}, "scaledownPeriod": {"type": "integer", "default": 300, "minimum": 0}, "scaleupPeriod": {"type": "integer", "default": 60, "minimum": 0}, "hosts": {"type": "array", "description": "List of hosts to monitor for HTTP requests", "items": {"type": "string"}}, "pathPrefixes": {"type": "array", "description": "List of path prefixes to monitor", "items": {"type": "string"}}}}}}, "serviceAccountName": {"type": "string", "description": "Name of the ServiceAccount to use (must be defined in serviceAccounts array)"}, "volumes": {"type": "array", "description": "Volume specifications for the workload", "items": {"$ref": "#/definitions/volumeSpec"}}}}}, "workerTemplates": {"type": "array", "description": "Reusable worker templates that can be referenced by workers", "items": {"type": "object", "required": ["templateName", "type"], "properties": {"templateName": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Unique identifier for this worker template"}, "type": {"type": "string"}, "enabled": {"type": "boolean", "default": true}, "workloadCriticality": {"type": "string", "enum": ["application-critical", "application-semi-critical", "application-sub-critical", "application-non-critical"], "default": "application-semi-critical"}, "image": {"$ref": "#/definitions/imageSpec"}, "command": {"type": "array", "items": {"type": "string"}, "description": "Command array that can contain Go template variables like {{ .tenantId }}"}, "args": {"type": "array", "items": {"type": "string"}, "description": "Args array that can contain Go template variables like {{ .tenantId }}"}, "env": {"$ref": "#/definitions/envSpec"}, "envFrom": {"$ref": "#/definitions/envFromSpec"}, "resources": {"type": "object", "properties": {"requests": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "limits": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}}}, "vpa": {"$ref": "#/definitions/vpaSpec"}, "healthCheck": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "type": {"type": "string", "enum": ["httpGet", "tcpSocket", "exec"], "default": "httpGet", "description": "Type of health check: httpGet, tcpSocket, or exec"}, "path": {"type": "string", "default": "/health", "description": "HTTP path for httpGet health checks"}, "port": {"type": "integer", "description": "Port for httpGet and tcpSocket health checks"}, "command": {"type": "array", "items": {"type": "string"}, "description": "Command to run for exec health checks"}, "scheme": {"type": "string", "enum": ["HTTP", "HTTPS"], "default": "HTTP", "description": "Scheme for httpGet health checks"}, "httpHeaders": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"]}, "description": "HTTP headers for httpGet health checks"}, "initialDelaySeconds": {"type": "integer", "default": 30}, "periodSeconds": {"type": "integer", "default": 10}, "timeoutSeconds": {"type": "integer", "default": 5}, "failureThreshold": {"type": "integer", "default": 3}, "successThreshold": {"type": "integer", "default": 1}}, "anyOf": [{"properties": {"enabled": {"const": false}}}, {"properties": {"enabled": {"const": true}, "type": {"const": "httpGet"}}, "required": ["path"]}, {"properties": {"enabled": {"const": true}, "type": {"const": "tcpSocket"}}, "required": ["port"]}, {"properties": {"enabled": {"const": true}, "type": {"const": "exec"}}, "required": ["command"]}]}, "scaling": {"type": "object", "properties": {"type": {"type": "string", "enum": ["plain", "hpa", "keda", "keda-http"], "default": "plain", "description": "Type of scaling to use: plain for static replicas, hpa for HorizontalPodAutoscaler, keda for KEDA ScaledObject, keda-http for KEDA HTTPScaledObject"}, "replicas": {"type": "integer", "default": 1, "description": "Number of replicas when no autoscaling is configured"}, "plainConfig": {"type": "object", "description": "Plain scaling configuration (used when type is 'plain')", "properties": {"replicas": {"type": "integer", "default": 1, "minimum": 0, "description": "Number of replicas for static scaling"}}}, "hpaConfig": {"type": "object", "description": "HPA configuration (used when type is 'hpa')", "properties": {"minReplicas": {"type": "integer", "default": 1, "minimum": 1}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "targetCPUUtilizationPercentage": {"type": "integer", "default": 70, "minimum": 1, "maximum": 100}, "targetMemoryUtilizationPercentage": {"type": "integer", "minimum": 1, "maximum": 100}, "behavior": {"type": "object", "description": "HPA scaling behavior configuration", "properties": {"scaleDown": {"type": "object"}, "scaleUp": {"type": "object"}}}}}, "kedaConfig": {"type": "object", "description": "KEDA configuration (used when type is 'keda')", "properties": {"minReplicas": {"type": "integer", "default": 0, "minimum": 0}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "pollingInterval": {"type": "integer", "default": 30, "minimum": 1}, "cooldownPeriod": {"type": "integer", "default": 300, "minimum": 0}, "idleReplicaCount": {"type": "integer", "default": 0, "minimum": 0}, "triggers": {"type": "array", "description": "KEDA scaling triggers", "items": {"type": "object", "properties": {"type": {"type": "string"}, "metadata": {"type": "object"}, "metricType": {"type": "string"}, "authenticationRef": {"type": "object"}}, "required": ["type"]}}}}, "httpScaledObjectConfig": {"type": "object", "description": "KEDA HTTP configuration (used when type is 'keda-http')", "properties": {"minReplicas": {"type": "integer", "default": 0, "minimum": 0}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "targetPendingRequests": {"type": "integer", "default": 50, "minimum": 1}, "scaledownPeriod": {"type": "integer", "default": 300, "minimum": 0}, "scaleupPeriod": {"type": "integer", "default": 30, "minimum": 0}, "hosts": {"type": "array", "description": "List of hosts to monitor for HTTP requests", "items": {"type": "string"}}, "pathPrefixes": {"type": "array", "description": "List of path prefixes to monitor", "items": {"type": "string"}}}}}}, "serviceAccountName": {"type": "string", "description": "Name of the ServiceAccount to use (must be defined in serviceAccounts array)"}, "volumes": {"type": "array", "description": "Volume specifications for the workload", "items": {"$ref": "#/definitions/volumeSpec"}}}}}, "workers": {"type": "array", "items": {"type": "object", "anyOf": [{"description": "Template-based worker configuration", "required": ["templateName", "templateVars"], "properties": {"templateName": {"type": "string", "description": "Name of the worker template to use"}, "templateVars": {"type": "array", "description": "Array of template variable sets, each creating a separate worker instance", "minItems": 1, "items": {"type": "object", "description": "Template variables for this worker instance. These will be available as Go template variables in the template configuration.", "additionalProperties": true}}}, "additionalProperties": false}, {"description": "Direct worker configuration (existing approach)", "required": ["name", "type"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"}, "type": {"type": "string"}, "enabled": {"type": "boolean", "default": true}, "workloadCriticality": {"type": "string", "enum": ["application-critical", "application-semi-critical", "application-sub-critical", "application-non-critical"], "default": "application-semi-critical"}, "image": {"$ref": "#/definitions/imageSpec"}, "command": {"type": "array", "items": {"type": "string"}}, "args": {"type": "array", "items": {"type": "string"}}, "env": {"$ref": "#/definitions/envSpec"}, "envFrom": {"$ref": "#/definitions/envFromSpec"}, "resources": {"type": "object", "properties": {"requests": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "limits": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}}}, "vpa": {"$ref": "#/definitions/vpaSpec"}, "healthCheck": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "type": {"type": "string", "enum": ["httpGet", "tcpSocket", "exec"], "default": "httpGet", "description": "Type of health check: httpGet, tcpSocket, or exec"}, "path": {"type": "string", "default": "/health", "description": "HTTP path for httpGet health checks"}, "port": {"type": "integer", "description": "Port for httpGet and tcpSocket health checks"}, "command": {"type": "array", "items": {"type": "string"}, "description": "Command to run for exec health checks"}, "scheme": {"type": "string", "enum": ["HTTP", "HTTPS"], "default": "HTTP", "description": "Scheme for httpGet health checks"}, "httpHeaders": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"]}, "description": "HTTP headers for httpGet health checks"}, "initialDelaySeconds": {"type": "integer", "default": 30}, "periodSeconds": {"type": "integer", "default": 10}, "timeoutSeconds": {"type": "integer", "default": 5}, "failureThreshold": {"type": "integer", "default": 3}, "successThreshold": {"type": "integer", "default": 1}}, "anyOf": [{"properties": {"enabled": {"const": false}}}, {"properties": {"enabled": {"const": true}, "type": {"const": "httpGet"}}, "required": ["path"]}, {"properties": {"enabled": {"const": true}, "type": {"const": "tcpSocket"}}, "required": ["port"]}, {"properties": {"enabled": {"const": true}, "type": {"const": "exec"}}, "required": ["command"]}]}, "scaling": {"type": "object", "properties": {"type": {"type": "string", "enum": ["plain", "hpa", "keda", "keda-http"], "default": "plain", "description": "Type of scaling to use: plain for static replicas, hpa for HorizontalPodAutoscaler, keda for KEDA ScaledObject, keda-http for KEDA HTTPScaledObject"}, "replicas": {"type": "integer", "default": 1, "description": "Number of replicas when no autoscaling is configured"}, "plainConfig": {"type": "object", "description": "Plain scaling configuration (used when type is 'plain')", "properties": {"replicas": {"type": "integer", "default": 1, "minimum": 0, "description": "Number of replicas for static scaling"}}}, "hpaConfig": {"type": "object", "description": "HPA configuration (used when type is 'hpa')", "properties": {"minReplicas": {"type": "integer", "default": 1, "minimum": 1}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "targetCPUUtilizationPercentage": {"type": "integer", "default": 70, "minimum": 1, "maximum": 100}, "targetMemoryUtilizationPercentage": {"type": "integer", "minimum": 1, "maximum": 100}, "behavior": {"type": "object", "description": "HPA scaling behavior configuration", "properties": {"scaleDown": {"type": "object"}, "scaleUp": {"type": "object"}}}}}, "kedaConfig": {"type": "object", "description": "KEDA configuration (used when type is 'keda')", "properties": {"minReplicas": {"type": "integer", "default": 0, "minimum": 0}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "pollingInterval": {"type": "integer", "default": 30, "minimum": 1}, "cooldownPeriod": {"type": "integer", "default": 300, "minimum": 0}, "idleReplicaCount": {"type": "integer", "default": 0, "minimum": 0}, "triggers": {"type": "array", "description": "KEDA scaling triggers", "items": {"type": "object", "properties": {"type": {"type": "string"}, "metadata": {"type": "object"}, "metricType": {"type": "string"}, "authenticationRef": {"type": "object"}}, "required": ["type"]}}}}, "httpScaledObjectConfig": {"type": "object", "description": "KEDA HTTP configuration (used when type is 'keda-http')", "properties": {"minReplicas": {"type": "integer", "default": 0, "minimum": 0}, "maxReplicas": {"type": "integer", "default": 10, "minimum": 1}, "targetPendingRequests": {"type": "integer", "default": 50, "minimum": 1}, "scaledownPeriod": {"type": "integer", "default": 300, "minimum": 0}, "scaleupPeriod": {"type": "integer", "default": 30, "minimum": 0}, "hosts": {"type": "array", "description": "List of hosts to monitor for HTTP requests", "items": {"type": "string"}}, "pathPrefixes": {"type": "array", "description": "List of path prefixes to monitor", "items": {"type": "string"}}}}}}, "serviceAccountName": {"type": "string", "description": "Name of the ServiceAccount to use (must be defined in serviceAccounts array)"}, "volumes": {"type": "array", "description": "Volume specifications for the workload", "items": {"$ref": "#/definitions/volumeSpec"}}}}]}}, "tasks": {"type": "array", "items": {"type": "object", "required": ["name", "type"], "anyOf": [{"properties": {"type": {"const": "migration"}}, "required": ["jobConfig"], "not": {"required": ["cronJobConfig"]}}, {"properties": {"type": {"const": "job"}}, "required": ["jobConfig"], "not": {"required": ["cronJobConfig"]}}, {"properties": {"type": {"const": "cronjob"}}, "required": ["cronJobConfig"]}], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"}, "type": {"type": "string", "enum": ["migration", "job", "cronjob"], "description": "Type of task: migration/job (requires jobConfig, no cronJobConfig), cronjob (requires cronJobConfig, optional jobConfig for job template)"}, "enabled": {"type": "boolean", "default": true}, "workloadCriticality": {"type": "string", "enum": ["application-critical", "application-semi-critical", "application-sub-critical", "application-non-critical"], "default": "application-semi-critical"}, "dependsOn": {"type": "array", "items": {"type": "string"}, "description": "List of workload names this task depends on"}, "image": {"$ref": "#/definitions/imageSpec"}, "command": {"type": "array", "items": {"type": "string"}}, "args": {"type": "array", "items": {"type": "string"}}, "env": {"$ref": "#/definitions/envSpec"}, "envFrom": {"$ref": "#/definitions/envFromSpec"}, "resources": {"type": "object", "properties": {"requests": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "limits": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}}}, "jobConfig": {"type": "object", "description": "Configuration for Job tasks (required for migration/job types, optional for cronjob as job template config)", "properties": {"backoffLimit": {"type": "integer", "minimum": 0, "default": 6}, "activeDeadlineSeconds": {"type": "integer", "minimum": 1}, "ttlSecondsAfterFinished": {"type": "integer", "minimum": 0, "default": 100}, "restartPolicy": {"type": "string", "enum": ["Never", "OnFailure"], "default": "Never"}}}, "cronJobConfig": {"type": "object", "description": "Configuration for CronJob tasks (required for cronjob type, cannot be used with migration/job types)", "properties": {"schedule": {"type": "string", "pattern": "^(@(annually|yearly|monthly|weekly|daily|hourly|reboot))|(@every (\\d+(ns|us|µs|ms|s|m|h))+)|((((\\d+,)+\\d+|(\\d+([/\\-])\\d+)|\\d+|\\*) ?){5,7})$", "description": "Cron schedule expression"}, "timeZone": {"type": "string", "description": "Time zone for the cron schedule"}, "concurrencyPolicy": {"type": "string", "enum": ["Allow", "Forbid", "Replace"], "default": "Allow"}, "successfulJobsHistoryLimit": {"type": "integer", "minimum": 0, "default": 3}, "failedJobsHistoryLimit": {"type": "integer", "minimum": 0, "default": 1}, "startingDeadlineSeconds": {"type": "integer", "minimum": 0}}, "required": ["schedule"]}, "serviceAccountName": {"type": "string", "description": "Name of the ServiceAccount to use (must be defined in serviceAccounts array)"}, "volumes": {"type": "array", "description": "Volume specifications for the workload", "items": {"$ref": "#/definitions/volumeSpec"}}}}}, "externalSecrets": {"type": "array", "description": "External secrets configuration using External Secrets Operator", "items": {"type": "object", "required": ["secretName", "secretStore", "awsSecretPath"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Name of the external secret"}, "secretStore": {"type": "string", "description": "Name of the SecretStore to use"}, "awsSecretPath": {"type": "string", "description": "AWS Parameter Store path"}, "refreshInterval": {"type": "string", "pattern": "^[0-9]+(s|m|h)$", "default": "1h", "description": "How often to refresh the secret"}, "secretName": {"type": "string", "description": "Name of the Kubernetes secret to create (defaults to serviceName-name-secrets)"}}}}, "ingress": {"type": "array", "items": {"type": "object", "required": ["name", "host"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Name of the ingress resource"}, "enabled": {"type": "boolean", "default": true}, "host": {"type": "string", "description": "Hostname for the ingress"}, "ingressClassName": {"type": "string", "default": "nginx", "description": "Ingress class name"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Annotations for the ingress"}, "tls": {"type": "boolean", "default": false, "description": "Enable TLS for this ingress"}, "tlsSecretName": {"type": "string", "description": "Custom TLS secret name (optional, defaults to auto-generated)"}, "paths": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["path", "serviceName", "servicePort"], "properties": {"path": {"type": "string", "default": "/", "description": "Path for the ingress rule"}, "pathType": {"type": "string", "enum": ["Exact", "Prefix", "ImplementationSpecific"], "default": "Prefix", "description": "Path type for the ingress rule"}, "serviceName": {"type": "string", "description": "Name of the service (from services array)"}, "servicePort": {"type": "integer", "minimum": 1, "maximum": 65535, "description": "Port of the service"}}}, "description": "Array of path configurations for this host"}}}}}, "definitions": {"imageSpec": {"type": "object", "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"], "default": "IfNotPresent"}, "pullSecrets": {"type": "array", "items": {"type": "string"}}}}, "envSpec": {"type": "array", "description": "Environment variables", "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Name of the environment variable"}, "value": {"type": "string", "description": "Value of the environment variable"}, "valueFrom": {"type": "object", "description": "Source for the environment variable's value"}}}}, "envFromSpec": {"type": "array", "description": "Environment variables from ConfigMaps and Secrets", "items": {"type": "object", "properties": {"configMapRef": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the ConfigMap"}, "optional": {"type": "boolean", "default": false, "description": "Whether the ConfigMap must be defined"}}, "required": ["name"]}, "secretRef": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the Secret"}, "optional": {"type": "boolean", "default": false, "description": "Whether the Secret must be defined"}}, "required": ["name"]}}, "oneOf": [{"required": ["configMapRef"]}, {"required": ["secretRef"]}]}}, "volumeSpec": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "description": "Name of the volume"}, "type": {"type": "string", "enum": ["efs", "ebs", "pvc", "configMap", "secret", "emptyDir"], "description": "Type of volume"}, "mountPath": {"type": "string", "description": "Custom mount path (defaults to /app/{volumeName})"}, "efs": {"type": "object", "description": "EFS volume configuration (when type=efs)", "properties": {"fileSystemId": {"type": "string", "description": "EFS file system ID (defaults to cluster shared EFS)"}, "accessPoint": {"type": "string", "description": "EFS access point ID (auto-generated if not specified)"}, "path": {"type": "string", "default": "/", "description": "Path within the EFS file system"}, "performance": {"type": "boolean", "default": false, "description": "Use high-performance EFS configuration"}}}, "ebs": {"type": "object", "description": "EBS volume configuration (when type=ebs)", "properties": {"storageClass": {"type": "string", "default": "ebs-sc", "description": "EBS storage class"}, "accessModes": {"type": "array", "items": {"type": "string", "enum": ["ReadWriteOnce", "ReadOnlyMany"]}, "default": ["ReadWriteOnce"], "description": "Access modes for EBS volume"}}}, "pvc": {"type": "object", "description": "PVC volume configuration (when type=pvc)", "properties": {"claimName": {"type": "string", "description": "Name of existing PVC (auto-generated if not specified)"}, "storageClass": {"type": "string", "enum": ["efs-sc", "ebs-sc"], "default": "efs-sc", "description": "Storage class for dynamic provisioning"}, "size": {"type": "string", "pattern": "^[0-9]+[KMGT]i?$", "default": "100Gi", "description": "Size of the volume"}, "accessModes": {"type": "array", "items": {"type": "string", "enum": ["ReadWriteOnce", "ReadOnlyMany", "ReadWriteMany"]}, "default": ["ReadWriteMany"], "description": "Access modes for the PVC"}, "performance": {"type": "boolean", "default": false, "description": "Use high-performance storage configuration"}}}, "configMap": {"type": "object", "description": "ConfigMap volume configuration (when type=configMap)", "properties": {"name": {"type": "string", "description": "Name of the ConfigMap"}, "defaultMode": {"type": "integer", "default": 420, "description": "Default file mode"}}, "required": ["name"]}, "secret": {"type": "object", "description": "Secret volume configuration (when type=secret)", "properties": {"secretName": {"type": "string", "description": "Name of the Secret"}, "defaultMode": {"type": "integer", "default": 420, "description": "Default file mode"}}, "required": ["secretName"]}, "emptyDir": {"type": "object", "description": "EmptyDir volume configuration (when type=emptyDir)", "properties": {"sizeLimit": {"type": "string", "pattern": "^[0-9]+[KMGT]i?$", "description": "Size limit for the emptyDir volume"}, "medium": {"type": "string", "enum": ["", "Memory"], "description": "Storage medium (empty for disk, Memory for tmpfs)"}}}}, "anyOf": [{"properties": {"type": {"const": "efs"}}}, {"properties": {"type": {"const": "ebs"}}}, {"properties": {"type": {"const": "pvc"}}}, {"properties": {"type": {"const": "configMap"}}, "required": ["configMap"]}, {"properties": {"type": {"const": "secret"}}, "required": ["secret"]}, {"properties": {"type": {"const": "emptyDir"}}}]}, "vpaSpec": {"type": "object", "description": "Vertical Pod Autoscaler configuration", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Enable VPA for this workload"}, "updateMode": {"type": "string", "enum": ["Off", "Auto", "Initial"], "default": "Off", "description": "VPA update mode: Off (recommendations only), Auto (automatic updates), Initial (update on pod creation)"}, "minAllowed": {"type": "object", "description": "Minimum allowed resource constraints", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "maxAllowed": {"type": "object", "description": "Maximum allowed resource constraints", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "resourcePolicy": {"type": "object", "description": "Advanced VPA resource policy configuration", "properties": {"containerPolicies": {"type": "array", "items": {"type": "object", "properties": {"containerName": {"type": "string"}, "minAllowed": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "maxAllowed": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "controlledResources": {"type": "array", "items": {"type": "string"}}, "controlledValues": {"type": "string", "enum": ["RequestsAndLimits", "RequestsOnly"]}}}}}}}}}}