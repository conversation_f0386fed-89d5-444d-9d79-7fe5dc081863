{"$schema": "https://json-schema.org/draft/2019-09/schema", "type": "object", "properties": {"global": {"type": "object", "properties": {"cluster": {"type": "string", "description": "Name of the target cluster"}, "environment": {"type": "string", "enum": ["development", "staging", "production"], "description": "Environment name"}, "repoURL": {"type": "string", "format": "uri", "description": "Git repository URL"}, "targetRevision": {"type": "string", "default": "main", "description": "Git branch, tag, or commit to sync"}, "project": {"type": "string", "default": "treebo-default-project", "description": "ArgoCD project name"}, "destinationServer": {"type": "string", "default": "https://kubernetes.default.svc", "description": "Kubernetes API server URL"}, "namespace": {"type": "string", "default": "default", "description": "Default namespace for applications"}, "syncWave": {"type": "integer", "default": 5, "minimum": 0, "description": "Default sync wave"}, "category": {"type": "string", "enum": ["infrastructure", "platform", "application", "observability", "tools", "data", "operator", "utility"], "default": "application", "description": "Default application category"}, "syncPolicy": {"type": "object", "description": "Default sync policy for all applications"}, "annotations": {"type": "object", "description": "Default annotations for all applications"}, "labels": {"type": "object", "description": "Default labels for all applications"}}, "required": ["repoURL"]}, "enabled": {"type": "boolean", "default": true, "description": "Enable/disable creation of ArgoCD Applications"}, "applications": {"type": "object", "properties": {"directories": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Application name (optional, derived from path if not provided)"}, "path": {"type": "string", "description": "Path to the application directory in the repository"}, "namespace": {"type": "string", "description": "Target namespace for the application"}, "syncWave": {"type": "integer", "minimum": 0, "description": "Sync wave for dependency ordering"}, "category": {"type": "string", "enum": ["infrastructure", "platform", "application", "observability", "tools", "data", "operator", "utility"], "description": "Application category"}, "project": {"type": "string", "description": "ArgoCD project (overrides global setting)"}, "targetRevision": {"type": "string", "description": "Git revision (overrides global setting)"}, "destinationServer": {"type": "string", "description": "Kubernetes API server (overrides global setting)"}, "syncPolicy": {"type": "object", "description": "Custom sync policy for this application"}, "ignoreDifferences": {"type": "array", "items": {"type": "object", "properties": {"group": {"type": "string"}, "kind": {"type": "string"}, "jsonPointers": {"type": "array", "items": {"type": "string"}}}}, "description": "Resource differences to ignore"}, "info": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"]}, "description": "Additional information about the application"}, "annotations": {"type": "object", "description": "Additional annotations for this application"}, "labels": {"type": "object", "description": "Additional labels for this application"}, "helm": {"type": "object", "description": "Helm-specific configuration"}, "kustomize": {"type": "object", "description": "Kustomize-specific configuration"}, "plugin": {"type": "object", "description": "Plugin-specific configuration"}, "revisionHistoryLimit": {"type": "integer", "minimum": 0, "description": "Number of old sync results to keep"}}, "required": ["path"]}}}}}, "required": ["global", "applications"]}