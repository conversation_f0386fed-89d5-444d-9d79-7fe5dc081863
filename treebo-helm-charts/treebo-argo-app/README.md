# Treebo ArgoCD Application Chart

A specialized Helm chart for creating ArgoCD Applications from directory paths. This chart implements the Individual Application Pattern for ArgoCD, allowing you to manage multiple applications with granular control over sync policies, dependencies, and deployment strategies.

## Features

- **Directory-based Application Creation**: Automatically creates ArgoCD Applications from a list of directory paths
- **Flexible Sync Policies**: Per-application sync policy configuration with sensible defaults
- **Sync Wave Management**: Built-in dependency ordering through sync waves
- **Environment-specific Configuration**: Different values files for staging, production, and tools clusters
- **Category-based Organization**: Group applications by infrastructure, platform, applications, observability, etc.
- **Rich Metadata**: Support for application info, annotations, and labels
- **Ignore Differences**: Per-application configuration for ignoring specific resource differences
- **Default Project with Maintenance Window**: Uses TreeboDefaultProject with automated maintenance window at 2:00 AM UTC

## Quick Start

### 1. Deploy to Staging Cluster

```bash
helm upgrade --install argocd-apps-staging \
  ./treebo-helm-charts/treebo-argo-app \
  -f ./treebo-helm-charts/treebo-argo-app/values/cluster-treebo-staging.yaml \
  -n argocd --create-namespace
```

### 2. Deploy to Production Cluster

```bash
helm upgrade --install argocd-apps-production \
  ./treebo-helm-charts/treebo-argo-app \
  -f ./treebo-helm-charts/treebo-argo-app/values/cluster-treebo-production.yaml \
  -n argocd --create-namespace
```

### 3. Deploy to Tools Common Cluster

```bash
helm upgrade --install argocd-apps-tools-common \
  ./treebo-helm-charts/treebo-argo-app \
  -f ./treebo-helm-charts/treebo-argo-app/values/cluster-treebo-tools-common.yaml \
  -n argocd --create-namespace
```

## Configuration

### Basic Application Configuration

```yaml
applications:
  directories:
    - name: "my-app"                    # Application name (optional, derived from path if not provided)
      path: "apps/my-app/overlays/staging"  # Path to Kustomize overlay or Helm chart
      namespace: "default"              # Target namespace (optional - see namespace behavior below)
      syncWave: 10                     # Sync wave for dependency ordering
      category: "application"          # Application category for organization
```

### Namespace Behavior

The chart supports flexible namespace management:

#### **Option 1: Let Manifests Control Namespaces**
```yaml
applications:
  directories:
    - name: "external-secrets"
      path: "tools/external-secrets/overlays/staging"
      # No namespace specified = manifests control where resources are deployed
      # Good for: operators, multi-namespace applications, complex deployments
```

#### **Option 2: Force Specific Namespace**
```yaml
applications:
  directories:
    - name: "auth"
      path: "apps/auth/overlays/staging"
      namespace: "backend"  # Override any namespaces in manifests
      # Good for: simple apps, enforcing namespace policies
```

### Advanced Application Configuration

```yaml
applications:
  directories:
    - name: "auth"
      path: "apps/auth/overlays/treebo-staging"
      namespace: "default"
      syncWave: 10
      category: "application"

      # Flattened metadata fields (optional)
      description: "Authentication service"
      team: "backend-team"

      project: "backend-team"           # Override global ArgoCD project (default: treebo-default-project)

      # Custom sync policy
      syncPolicy:
        automated:
          prune: true
          selfHeal: false
        retry:
          limit: 5
          backoff:
            duration: 5s
            factor: 2
            maxDuration: 3m

      # Ignore specific differences
      ignoreDifferences:
        - group: apps
          kind: Deployment
          jsonPointers:
            - /spec/replicas

      # Additional info (for complex metadata)
      info:
        - name: "Repository"
          value: "https://github.com/treebo-noss/auth-service"
        - name: "Documentation"
          value: "https://docs.treebo.com/auth"

      # Additional annotations
      annotations:
        notifications.argoproj.io/subscribe.on-sync-failed.slack: "backend-alerts"

      # Additional labels
      labels:
        component: "authentication"
```

### Sync Wave Strategy

The chart uses sync waves to ensure proper deployment ordering:

- **Wave 0**: Critical infrastructure (External Secrets, CSI drivers, Metrics Server)
- **Wave 5**: Platform services (Load Balancer Controller, DNS, Ingress)
- **Wave 10**: Business applications (Auth, Wallet, Rewards, etc.)
- **Wave 15**: Observability tools (OpenTelemetry, Promtail, Typesense)
- **Wave 20+**: Development tools and utilities

### Environment-specific Configuration

#### Staging Environment
- **Automated sync**: Enabled with self-healing
- **Retry policy**: Aggressive (5 retries, short backoff)
- **Notifications**: Development alerts channel

#### Production Environment
- **Automated sync**: Enabled but no self-healing (manual intervention required)
- **Retry policy**: Conservative (3 retries, longer backoff)
- **Notifications**: Production alerts channel
- **Additional metadata**: Criticality levels

#### Tools Common Environment
- **Mixed policies**: Infrastructure auto-heals, monitoring tools are more conservative
- **Extended categories**: Includes data infrastructure, operators, and utilities
- **Comprehensive tooling**: Full observability stack, development tools, databases

## Application Categories

### Infrastructure
- External Secrets Operator
- CSI Drivers (EBS, EFS)
- Metrics Server

### Platform
- AWS Load Balancer Controller
- External DNS
- NGINX Ingress Controller

### Application
- Business services (Auth, Wallet, Rewards, etc.)
- API services
- Frontend applications

### Observability
- Prometheus, Grafana, Tempo, Loki, Mimir
- OpenTelemetry Collector
- Log aggregation tools

### Tools
- CI/CD systems (Drone)
- Development tools (SonarQube, Typesense)
- Security tools (Vaultwarden, Infisical)

### Data
- Databases (PostgreSQL, MariaDB, Redis)
- Message brokers
- Data processing tools

### Operator
- Kubernetes operators (Strimzi Kafka, ClickHouse)
- Custom resource definitions

### Utility
- Supporting tools (Reloader)
- Maintenance utilities

## Default Project and Maintenance Window

All applications created by this chart use the `TreeboDefaultProject` ArgoCD project by default, which includes:

### Maintenance Window
- **Schedule**: Every day at 2:00 AM UTC
- **Duration**: 2 hours (2:00 AM - 4:00 AM UTC)
- **Behavior**: Automatic syncing is disabled during this window
- **Manual Override**: Manual sync is still allowed during maintenance window

### Project Features
- **Comprehensive Resource Access**: Allows deployment of all common Kubernetes resources
- **Multi-Repository Support**: Access to Treebo repositories and common Helm chart repositories
- **Role-Based Access Control**:
  - `admin`: Full access (platform-team, devops-team)
  - `developer`: Read and sync access (developers, backend-team, frontend-team)
  - `readonly`: Read-only access (qa-team, support-team)
- **Orphaned Resource Monitoring**: Warns about orphaned resources with intelligent ignore patterns

### Overriding the Default Project

You can override the project for specific applications:

```yaml
applications:
  directories:
    - name: "special-app"
      path: "apps/special-app/overlays/production"
      project: "custom-project"  # Override default project
```

### Maintenance Window Impact

During the maintenance window (2:00 AM - 4:00 AM UTC):
- ✅ Applications continue running normally
- ❌ Automatic sync is disabled
- ✅ Manual sync is still available
- ✅ Emergency deployments can be performed manually
- ❌ Scheduled sync operations are postponed

## Values File Structure

```yaml
global:
  # Cluster identification
  cluster: "cluster-name"
  environment: "staging|production"

  # Git repository configuration
  repoURL: "https://github.com/treebo-noss/devops-gitops.git"
  targetRevision: "main"

  # ArgoCD configuration
  project: "treebo-default-project"
  destinationServer: "https://kubernetes.default.svc"

  # Default values for applications
  namespace: "default"
  syncWave: 5
  category: "application"

  # Default sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true

  # Default metadata
  annotations: {}
  labels: {}

enabled: true

applications:
  directories: []
```

## Best Practices

### 1. Naming Conventions
- Use descriptive application names
- Follow kebab-case naming
- Include environment context when needed

### 2. Sync Wave Planning
- Infrastructure: 0-4
- Platform: 5-9
- Applications: 10-14
- Observability: 15-19
- Tools: 20+

### 3. Sync Policies
- **Staging**: Aggressive automation for fast feedback
- **Production**: Conservative automation with manual oversight
- **Infrastructure**: Auto-healing enabled
- **Applications**: Consider disabling self-heal for critical apps

### 4. Metadata Management
- Always include team ownership information
- Add repository links for applications
- Specify criticality levels for production
- Use consistent labeling across environments

## Troubleshooting

### Application Not Created
1. Check if `enabled: true` is set
2. Verify the directory path exists in the repository
3. Check Helm template rendering: `helm template ./treebo-helm-charts/treebo-argo-app -f values.yaml`

### Sync Failures
1. Check application logs in ArgoCD UI
2. Verify target namespace exists or CreateNamespace is enabled
3. Check RBAC permissions for ArgoCD service account

### Dependency Issues
1. Review sync wave assignments
2. Check if dependent applications are healthy
3. Consider manual sync for troubleshooting

## Migration from Monolithic App-of-Apps

1. **Deploy the new chart** alongside existing app-of-apps
2. **Verify applications** are created and syncing correctly
3. **Update root app-of-apps** to point to individual applications
4. **Remove old monolithic** kustomization references

## Contributing

When adding new applications:

1. Add the directory configuration to appropriate values file
2. Set correct sync wave based on dependencies
3. Choose appropriate category
4. Add team and description information
5. Test in staging environment first
