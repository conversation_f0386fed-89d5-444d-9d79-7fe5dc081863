# Default values for treebo-argo-app
# This is a YAML-formatted file.

# Global configuration for all ArgoCD Applications
global:
  # Cluster identification
  cluster: ""
  environment: ""

  # Git repository configuration
  repoURL: "https://github.com/treebo-noss/devops-gitops.git"
  targetRevision: "main"

  # ArgoCD project to use for all applications
  project: "treebo-default-project"

  # Default destination server
  destinationServer: "https://kubernetes.default.svc"

  # Default namespace if not specified per application
  namespace: "default"

  # Default sync wave if not specified per application
  syncWave: 5

  # Default category if not specified per application
  category: "application"

  # Default sync policy for all applications
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true
    retry:
      limit: 3
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

  # Default annotations for all applications
  annotations: {}

  # Default labels for all applications
  labels: {}

# Applications configuration
applications:
  # List of directories to create ArgoCD Applications for
  directories:
    []
    # Example:
    # - name: "external-secrets"
    #   path: "tools/external-secrets/overlays/treebo-staging"
    #   namespace: "external-secrets"
    #   syncWave: 0
    #   category: "infrastructure"
    #   description: "External secrets management"  # Optional
    #   team: "platform-team"                      # Optional
    #   project: "default"  # Optional: override global project
    #   syncPolicy:  # Optional: override global sync policy
    #     automated:
    #       prune: true
    #       selfHeal: false
    #   ignoreDifferences:  # Optional: ignore specific differences
    #     - group: apps
    #       kind: Deployment
    #       jsonPointers:
    #         - /spec/replicas
    #   info:  # Optional: additional complex information
    #     - name: "Repository"
    #       value: "https://github.com/..."
    #   annotations:  # Optional: additional annotations
    #     notifications.argoproj.io/subscribe.on-sync-succeeded.slack: "devops-alerts"
    #   labels:  # Optional: additional labels
    #     component: "secrets"

# Enable/disable creation of ArgoCD Applications
enabled: true
