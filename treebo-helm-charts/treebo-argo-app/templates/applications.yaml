{{- if .Values.enabled }}
{{- range .Values.applications.directories }}
{{- $app := . }}
{{- $global := $ }}
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ include "treebo-argo-app.applicationName" $app }}
  namespace: argocd
  annotations:
    {{- include "treebo-argo-app.applicationAnnotations" (dict "app" $app "global" $global) | nindent 4 }}
  labels:
    {{- include "treebo-argo-app.applicationLabels" (dict "app" $app "global" $global) | nindent 4 }}
spec:
  project: {{ $app.project | default $global.Values.global.project }}
  source:
    repoURL: {{ $global.Values.global.repoURL }}
    targetRevision: {{ $app.targetRevision | default $global.Values.global.targetRevision }}
    path: {{ $app.path }}
    {{- if $app.helm }}
    helm:
      {{- toYaml $app.helm | nindent 6 }}
    {{- end }}
    {{- if $app.kustomize }}
    kustomize:
      {{- toYaml $app.kustomize | nindent 6 }}
    {{- end }}
    {{- if $app.plugin }}
    plugin:
      {{- toYaml $app.plugin | nindent 6 }}
    {{- end }}
  destination:
    server: {{ $app.destinationServer | default $global.Values.global.destinationServer }}
    {{- if $app.namespace }}
    namespace: {{ $app.namespace }}
    {{- end }}
  syncPolicy:
    {{- include "treebo-argo-app.syncPolicy" (dict "app" $app "global" $global) | nindent 4 }}
  {{- if $app.ignoreDifferences }}
  ignoreDifferences:
    {{- toYaml $app.ignoreDifferences | nindent 4 }}
  {{- end }}
  {{- if or $app.description $app.team $app.info }}
  info:
    {{- if $app.description }}
    - name: "Description"
      value: {{ $app.description | quote }}
    {{- end }}
    {{- if $app.team }}
    - name: "Team"
      value: {{ $app.team | quote }}
    {{- end }}
    {{- if $app.info }}
    {{- toYaml $app.info | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- if $app.revisionHistoryLimit }}
  revisionHistoryLimit: {{ $app.revisionHistoryLimit }}
  {{- end }}
{{- end }}
{{- end }}
