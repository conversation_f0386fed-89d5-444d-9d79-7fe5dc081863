{{/*
Expand the name of the chart.
*/}}
{{- define "treebo-argo-app.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "treebo-argo-app.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "treebo-argo-app.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "treebo-argo-app.labels" -}}
helm.sh/chart: {{ include "treebo-argo-app.chart" . }}
{{ include "treebo-argo-app.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- if .Values.global.cluster }}
treebo.com/cluster: {{ .Values.global.cluster }}
{{- end }}
{{- if .Values.global.environment }}
treebo.com/environment: {{ .Values.global.environment }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "treebo-argo-app.selectorLabels" -}}
app.kubernetes.io/name: {{ include "treebo-argo-app.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Generate application name from directory configuration
*/}}
{{- define "treebo-argo-app.applicationName" -}}
{{- $app := . -}}
{{- if $app.name -}}
{{- $app.name -}}
{{- else -}}
{{- $pathParts := splitList "/" $app.path -}}
{{- $lastPart := last $pathParts -}}
{{- $lastPart -}}
{{- end -}}
{{- end }}

{{/*
Generate sync policy for an application
*/}}
{{- define "treebo-argo-app.syncPolicy" -}}
{{- $app := .app -}}
{{- $global := .global -}}
{{- if $app.syncPolicy -}}
{{- toYaml $app.syncPolicy -}}
{{- else -}}
{{- toYaml $global.Values.global.syncPolicy -}}
{{- end -}}
{{- end }}

{{/*
Generate application labels
*/}}
{{- define "treebo-argo-app.applicationLabels" -}}
{{- $app := .app -}}
{{- $global := .global -}}
{{ include "treebo-argo-app.labels" $global }}
treebo.com/managed-by: treebo-argo-app
{{- if $app.category }}
treebo.com/category: {{ $app.category }}
{{- else }}
treebo.com/category: {{ $global.Values.global.category }}
{{- end }}
{{- if $global.Values.global.labels }}
{{- toYaml $global.Values.global.labels | nindent 0 }}
{{- end }}
{{- if $app.labels }}
{{- toYaml $app.labels | nindent 0 }}
{{- end }}
{{- end }}

{{/*
Generate application annotations
*/}}
{{- define "treebo-argo-app.applicationAnnotations" -}}
{{- $app := .app -}}
{{- $global := .global -}}
{{- $syncWave := $app.syncWave | default $global.Values.global.syncWave -}}
argocd.argoproj.io/sync-wave: "{{ $syncWave }}"
{{- if $global.Values.global.annotations }}
{{- toYaml $global.Values.global.annotations | nindent 0 }}
{{- end }}
{{- if $app.annotations }}
{{- toYaml $app.annotations | nindent 0 }}
{{- end }}
{{- end }}
