name: Kubernetes Manifest Validation

on:
  push:
    branches: [main, develop]
    paths:
      - "apps/**"
      - "tools/**"
      - "manifests/**"
      - "lib/**"
      - "conftest/policy/**"
      - ".github/workflows/k8s-validate.yml"
      - ".github/scripts/**"
  pull_request:
    paths:
      - "apps/**"
      - "tools/**"
      - "manifests/**"
      - "lib/**"
      - "conftest/policy/**"
      - ".github/workflows/k8s-validate.yml"
      - ".github/scripts/**"

jobs:
  detect-changes:
    name: Detect Changed Files
    runs-on: ubuntu-latest
    outputs:
      yaml-files: ${{ steps.changes.outputs.yaml-files }}
      added-modified-yaml-files: ${{ steps.changes.outputs.added-modified-yaml-files }}
      deleted-yaml-files: ${{ steps.changes.outputs.deleted-yaml-files }}
      affected-manifests: ${{ steps.changes.outputs.affected-manifests }}
      affected-overlays: ${{ steps.changes.outputs.affected-overlays }}
      has-changes: ${{ steps.changes.outputs.has-changes }}
      validation-mode: ${{ steps.changes.outputs.validation-mode }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Detect changes
        id: changes
        env:
          GITHUB_EVENT_NAME: ${{ github.event_name }}
          GITHUB_EVENT_BEFORE: ${{ github.event.before }}
          GITHUB_SHA: ${{ github.sha }}
          GITHUB_BASE_REF: ${{ github.base_ref }}
        run: ./.github/scripts/detect-changes.sh

  validate:
    name: ${{ matrix.tool }}
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.has-changes == 'true'
    strategy:
      matrix:
        tool: [yamllint, kubeconform, kube-linter, conftest]
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Install tools
        run: |
          if [ "${{ matrix.tool }}" = "kubeconform" ]; then
            ./.github/scripts/install-tools.sh ${{ matrix.tool }} kustomize jq
          else
            ./.github/scripts/install-tools.sh ${{ matrix.tool }} kustomize
          fi
      - name: Run validation
        run: |
          ./.github/scripts/run-validation.sh \
            "${{ matrix.tool }}" \
            "${{ needs.detect-changes.outputs.added-modified-yaml-files }}" \
            "${{ needs.detect-changes.outputs.affected-manifests }}" \
            "${{ needs.detect-changes.outputs.affected-overlays }}" \
            "${{ needs.detect-changes.outputs.validation-mode }}"
