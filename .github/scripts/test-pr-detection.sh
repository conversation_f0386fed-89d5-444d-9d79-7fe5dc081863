#!/bin/bash
set -euo pipefail

# Test script to demonstrate the difference between commit-based and PR-based change detection
# Usage: ./test-pr-detection.sh

echo "=== Testing Pull Request Change Detection ==="
echo

# Simulate different scenarios
echo "1. Testing commit-based detection (old approach):"
echo "   git diff --name-only HEAD~1..HEAD"
COMMIT_CHANGES=$(git diff --name-only HEAD~1..HEAD 2>/dev/null || true)
echo "   Files changed in last commit: $(echo "$COMMIT_CHANGES" | wc -l) files"
if [ -n "$COMMIT_CHANGES" ]; then
    echo "$COMMIT_CHANGES" | head -5
    if [ $(echo "$COMMIT_CHANGES" | wc -l) -gt 5 ]; then
        echo "   ... and $(( $(echo "$COMMIT_CHANGES" | wc -l) - 5 )) more files"
    fi
fi
echo

echo "2. Testing PR-based detection (new approach):"
echo "   git diff --name-only main...HEAD"
PR_CHANGES=$(git diff --name-only main...HEAD 2>/dev/null || true)
echo "   Files changed in entire PR: $(echo "$PR_CHANGES" | wc -l) files"
if [ -n "$PR_CHANGES" ]; then
    echo "$PR_CHANGES" | head -5
    if [ $(echo "$PR_CHANGES" | wc -l) -gt 5 ]; then
        echo "   ... and $(( $(echo "$PR_CHANGES" | wc -l) - 5 )) more files"
    fi
fi
echo

echo "3. Comparison:"
COMMIT_COUNT=$(echo "$COMMIT_CHANGES" | wc -l)
PR_COUNT=$(echo "$PR_CHANGES" | wc -l)

if [ "$COMMIT_COUNT" -eq "$PR_COUNT" ]; then
    echo "   ✓ Same number of files detected ($COMMIT_COUNT files)"
    echo "   This suggests we're on a single-commit PR or main branch"
else
    echo "   ⚠ Different number of files detected:"
    echo "     - Commit-based: $COMMIT_COUNT files"
    echo "     - PR-based: $PR_COUNT files"
    echo "   This demonstrates why PR-based detection is more comprehensive"
fi
echo

echo "4. Current branch info:"
echo "   Current branch: $(git branch --show-current 2>/dev/null || echo 'detached HEAD')"
echo "   Last commit: $(git log -1 --oneline)"
echo "   Commits ahead of main: $(git rev-list --count main..HEAD 2>/dev/null || echo 'N/A')"
echo

echo "=== Test Complete ==="
