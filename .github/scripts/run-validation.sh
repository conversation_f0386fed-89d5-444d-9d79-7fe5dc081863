#!/bin/bash
set -euo pipefail

# Run validation based on tool and inputs
# Usage: ./run-validation.sh <tool> <yaml-files> <manifests> <overlays> <validation-mode>

TOOL=$1
YAML_FILES=$2
MANIFESTS=$3
OVERLAYS=$4
VALIDATION_MODE=${5:-"incremental"}

echo "Running $TOOL validation in $VALIDATION_MODE mode"

run_yamllint() {
    if [ -z "$YAML_FILES" ]; then
        echo "No YAML files to lint"
        return 0
    fi

    echo "Running yamllint on: $YAML_FILES"
    for file in $YAML_FILES; do
        if [ -f "$file" ]; then
            echo "Linting: $file"
            yamllint -c .yamllint.yaml "$file"
        fi
    done
}

run_kubeconform() {
    if [ -z "$MANIFESTS" ]; then
        echo "No manifests to validate"
        return 0
    fi

    echo "Running kubeconform on manifests: $MANIFESTS"

    # Clone CRDs catalog if not already present
    if [ ! -d "CRDs-catalog" ]; then
        echo "Cloning CRDs catalog..."
        git clone --depth 1 https://github.com/datreeio/CRDs-catalog.git
    else
        echo "CRDs catalog already exists"
    fi

    mkdir -p built-manifests

    for manifest_name in $MANIFESTS; do
        manifest_dir="manifests/argocd/$manifest_name"
        echo "Building manifest: $manifest_name"

        if kustomize build --enable-helm --load-restrictor=LoadRestrictionsNone "$manifest_dir" > "built-manifests/${manifest_name}.yaml"; then
            echo "Successfully built $manifest_name"

            echo "Running kubeconform on built-manifests/${manifest_name}.yaml"
            KUBECONFORM_OUTPUT=$(kubeconform \
                -strict \
                -summary \
                -ignore-missing-schemas \
                -kubernetes-version 1.32.0 \
                -schema-location default \
                -schema-location 'CRDs-catalog/{{.Group}}/{{.ResourceKind}}_{{.ResourceAPIVersion}}.json' \
                -output json \
                "built-manifests/${manifest_name}.yaml")

            echo "Kubeconform output:"
            echo "$KUBECONFORM_OUTPUT"

            # Parse JSON output and check validation results
            VALID=$(echo "$KUBECONFORM_OUTPUT" | jq -r '.summary.valid // 0')
            INVALID=$(echo "$KUBECONFORM_OUTPUT" | jq -r '.summary.invalid // 0')
            ERRORS=$(echo "$KUBECONFORM_OUTPUT" | jq -r '.summary.errors // 0')
            SKIPPED=$(echo "$KUBECONFORM_OUTPUT" | jq -r '.summary.skipped // 0')

            echo "Validation summary for $manifest_name:"
            echo "  Valid: $VALID"
            echo "  Invalid: $INVALID"
            echo "  Errors: $ERRORS"
            echo "  Skipped: $SKIPPED"

            # Check failure conditions
            if [ "$INVALID" -gt 0 ]; then
                echo "❌ FAILURE: Found $INVALID invalid resources in $manifest_name"
                exit 1
            fi

            if [ "$ERRORS" -gt 0 ]; then
                echo "❌ FAILURE: Found $ERRORS validation errors in $manifest_name"
                exit 1
            fi

            SKIPPED_THRESHOLD=50 # feel free to change the number
            if [ "$SKIPPED" -gt $SKIPPED_THRESHOLD ]; then
                echo "❌ FAILURE: Too many skipped resources ($SKIPPED > $SKIPPED_THRESHOLD) in $manifest_name"
                echo "This may indicate missing schemas or configuration issues"
                exit 1
            fi

            echo "✅ Validation passed for $manifest_name"
        else
            echo "Failed to build $manifest_name"
            exit 1
        fi
    done
}

run_kube_linter() {
    if [ -z "$YAML_FILES" ]; then
        echo "No YAML files to lint"
        return 0
    fi

    echo "Running kube-linter on: $YAML_FILES"
    kube-linter lint --config .kube-linter.yaml $YAML_FILES
}

run_conftest() {
    if [ -z "$OVERLAYS" ]; then
        echo "No overlays to validate"
        return 0
    fi

    echo "Running conftest on overlays: $OVERLAYS"
    for overlay in $OVERLAYS; do
        echo "Running conftest on $overlay"
        conftest test "$overlay" --policy conftest/policy/envvars_generic.rego
    done
}

# Run the appropriate validation
case "$TOOL" in
    yamllint)
        run_yamllint
        ;;
    kubeconform)
        run_kubeconform
        ;;
    kube-linter)
        run_kube_linter
        ;;
    conftest)
        run_conftest
        ;;
    *)
        echo "Unknown validation tool: $TOOL"
        exit 1
        ;;
esac
