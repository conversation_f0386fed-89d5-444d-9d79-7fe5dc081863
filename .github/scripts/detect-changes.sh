#!/bin/bash
set -euo pipefail

# Detect changes in the current changeset and output affected files/directories
# Usage: ./detect-changes.sh [before_sha] [current_sha]

EVENT_NAME=${GITHUB_EVENT_NAME:-"pull_request"}
BEFORE_SHA=${1:-${GITHUB_EVENT_BEFORE:-""}}
CURRENT_SHA=${2:-${GITHUB_SHA:-"HEAD"}}

echo "Event: $EVENT_NAME"
echo "Detecting changes between $BEFORE_SHA and $CURRENT_SHA"

# Determine validation mode based on event type
if [ "$EVENT_NAME" = "push" ]; then
    VALIDATION_MODE="full-branch"
    echo "Push event detected - will validate entire branch"
else
    VALIDATION_MODE="incremental"
    echo "Pull request event detected - will validate only changes"
fi

# Get files to validate based on validation mode
if [ "$VALIDATION_MODE" = "full-branch" ]; then
    echo "Full branch validation - finding all YAML files and manifests"

    # Get all YAML files in the repository
    ALL_YAML_FILES=$(find . -name "*.yaml" -o -name "*.yml" | grep -v ".git" | sed 's|^\./||' || true)
    CHANGED_YAML_FILES="$ALL_YAML_FILES"
    ADDED_MODIFIED_YAML_FILES="$ALL_YAML_FILES"
    DELETED_YAML_FILES=""

    # Get all manifest directories
    AFFECTED_MANIFESTS=""
    if [ -d "manifests/argocd" ]; then
        for manifest_dir in manifests/argocd/*; do
            if [ -d "$manifest_dir" ]; then
                manifest_name=$(basename "$manifest_dir")
                AFFECTED_MANIFESTS="$AFFECTED_MANIFESTS $manifest_name"
            fi
        done
    fi

    # Get all app overlays
    AFFECTED_OVERLAYS=""
    if [ -d "apps" ]; then
        for app in apps/*; do
            if [ -d "$app" ] && [ -d "$app/overlays" ]; then
                for overlay in "$app/overlays"/*; do
                    if [ -d "$overlay" ]; then
                        AFFECTED_OVERLAYS="$AFFECTED_OVERLAYS $overlay"
                    fi
                done
            fi
        done
    fi

    echo "Full branch validation will process:"
    echo "  All YAML files: $(echo "$CHANGED_YAML_FILES" | wc -w) files"
    echo "  All manifests: $AFFECTED_MANIFESTS"
    echo "  All overlays: $AFFECTED_OVERLAYS"

else
    # Incremental validation - all changes in the pull request
    echo "Incremental validation - analyzing all changes in pull request"

    # For pull requests, get all changes from base branch to current HEAD
    # This ensures we validate all changes in the PR, not just the latest commit
    if [ "$EVENT_NAME" = "pull_request" ]; then
        # Use the base branch as comparison point for pull requests
        BASE_REF=${GITHUB_BASE_REF:-"main"}
        echo "Comparing against base branch: $BASE_REF"
        echo "Trying different git diff strategies for pull request..."

        # Try multiple strategies to get PR changes
        CHANGED_FILES=$(
            git diff --name-only "origin/$BASE_REF...HEAD" 2>/dev/null || \
            git diff --name-only "$BASE_REF...HEAD" 2>/dev/null || \
            git diff --name-only "origin/$BASE_REF..HEAD" 2>/dev/null || \
            git diff --name-only "$BASE_REF..HEAD" 2>/dev/null || \
            git diff --name-only HEAD~1..HEAD 2>/dev/null || \
            true
        )

        # Get added and modified files (exclude deleted)
        ADDED_MODIFIED_FILES=$(
            git diff --name-only --diff-filter=AM "origin/$BASE_REF...HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=AM "$BASE_REF...HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=AM "origin/$BASE_REF..HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=AM "$BASE_REF..HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=AM HEAD~1..HEAD 2>/dev/null || \
            true
        )

        # Get deleted files
        DELETED_FILES=$(
            git diff --name-only --diff-filter=D "origin/$BASE_REF...HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=D "$BASE_REF...HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=D "origin/$BASE_REF..HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=D "$BASE_REF..HEAD" 2>/dev/null || \
            git diff --name-only --diff-filter=D HEAD~1..HEAD 2>/dev/null || \
            true
        )

        echo "Git diff command used for PR: origin/$BASE_REF...HEAD (or fallbacks)"
    else
        # For other events, use the provided commit range
        echo "Using commit range: $BEFORE_SHA..$CURRENT_SHA"
        CHANGED_FILES=$(git diff --name-only "$BEFORE_SHA..$CURRENT_SHA" 2>/dev/null || git diff --name-only HEAD~1..HEAD 2>/dev/null || true)
        ADDED_MODIFIED_FILES=$(git diff --name-only --diff-filter=AM "$BEFORE_SHA..$CURRENT_SHA" 2>/dev/null || git diff --name-only --diff-filter=AM HEAD~1..HEAD 2>/dev/null || true)
        DELETED_FILES=$(git diff --name-only --diff-filter=D "$BEFORE_SHA..$CURRENT_SHA" 2>/dev/null || git diff --name-only --diff-filter=D HEAD~1..HEAD 2>/dev/null || true)
    fi

    if [ -z "$CHANGED_FILES" ]; then
        echo "No files changed in this changeset"
        if [ -n "${GITHUB_OUTPUT:-}" ]; then
            echo "yaml-files=" >> $GITHUB_OUTPUT
            echo "added-modified-yaml-files=" >> $GITHUB_OUTPUT
            echo "deleted-yaml-files=" >> $GITHUB_OUTPUT
            echo "affected-manifests=" >> $GITHUB_OUTPUT
            echo "affected-overlays=" >> $GITHUB_OUTPUT
            echo "has-changes=false" >> $GITHUB_OUTPUT
            echo "validation-mode=$VALIDATION_MODE" >> $GITHUB_OUTPUT
        fi
        exit 0
    fi

    if [ "$EVENT_NAME" = "pull_request" ]; then
        echo "All files changed in this pull request:"
    else
        echo "Files changed in this commit:"
    fi
    echo "$CHANGED_FILES"

    # Filter YAML files
    CHANGED_YAML_FILES=$(echo "$CHANGED_FILES" | grep -E '\.(yaml|yml)$' || true)
    ADDED_MODIFIED_YAML_FILES=$(echo "$ADDED_MODIFIED_FILES" | grep -E '\.(yaml|yml)$' || true)
    DELETED_YAML_FILES=$(echo "$DELETED_FILES" | grep -E '\.(yaml|yml)$' || true)

    # Show added/modified and deleted YAML files
    if [ -n "$ADDED_MODIFIED_YAML_FILES" ]; then
        echo "Added/Modified YAML files:"
        echo "$ADDED_MODIFIED_YAML_FILES"
    fi

    if [ -n "$DELETED_YAML_FILES" ]; then
        echo "Deleted YAML files:"
        echo "$DELETED_YAML_FILES"
    fi

    # Determine affected manifest directories
    AFFECTED_MANIFESTS=""
    if [ -d "manifests/argocd" ]; then
        for manifest_dir in manifests/argocd/*; do
            if [ -d "$manifest_dir" ]; then
                manifest_name=$(basename "$manifest_dir")
                # Check if changes affect this manifest
                if echo "$CHANGED_FILES" | grep -q "^$manifest_dir/" || \
                   echo "$CHANGED_FILES" | grep -q "^tools/" || \
                   echo "$CHANGED_FILES" | grep -q "^apps/" || \
                   echo "$CHANGED_FILES" | grep -q "^lib/"; then
                    AFFECTED_MANIFESTS="$AFFECTED_MANIFESTS $manifest_name"
                fi
            fi
        done
    fi

    # Determine affected app overlays
    AFFECTED_OVERLAYS=""
    if [ -d "apps" ]; then
        for app in apps/*; do
            if [ -d "$app" ] && [ -d "$app/overlays" ]; then
                for overlay in "$app/overlays"/*; do
                    if [ -d "$overlay" ]; then
                        if echo "$CHANGED_FILES" | grep -q "^$overlay/"; then
                            AFFECTED_OVERLAYS="$AFFECTED_OVERLAYS $overlay"
                        fi
                    fi
                done
            fi
        done
    fi
fi

# Clean up whitespace (handle empty strings gracefully)
CHANGED_YAML_FILES=$(echo "$CHANGED_YAML_FILES" | tr '\n' ' ' | xargs || true)
ADDED_MODIFIED_YAML_FILES=$(echo "$ADDED_MODIFIED_YAML_FILES" | tr '\n' ' ' | xargs || true)
DELETED_YAML_FILES=$(echo "$DELETED_YAML_FILES" | tr '\n' ' ' | xargs || true)
AFFECTED_MANIFESTS=$(echo "$AFFECTED_MANIFESTS" | xargs || true)
AFFECTED_OVERLAYS=$(echo "$AFFECTED_OVERLAYS" | xargs || true)

# Set GitHub outputs (only if GITHUB_OUTPUT is set, for CI environment)
if [ -n "${GITHUB_OUTPUT:-}" ]; then
    echo "yaml-files=$CHANGED_YAML_FILES" >> $GITHUB_OUTPUT
    echo "added-modified-yaml-files=$ADDED_MODIFIED_YAML_FILES" >> $GITHUB_OUTPUT
    echo "deleted-yaml-files=$DELETED_YAML_FILES" >> $GITHUB_OUTPUT
    echo "affected-manifests=$AFFECTED_MANIFESTS" >> $GITHUB_OUTPUT
    echo "affected-overlays=$AFFECTED_OVERLAYS" >> $GITHUB_OUTPUT
    echo "validation-mode=$VALIDATION_MODE" >> $GITHUB_OUTPUT

    # Determine if we have any changes worth validating
    if [ -n "$CHANGED_YAML_FILES" ] || [ -n "$AFFECTED_MANIFESTS" ] || [ -n "$AFFECTED_OVERLAYS" ]; then
        echo "has-changes=true" >> $GITHUB_OUTPUT
    else
        echo "has-changes=false" >> $GITHUB_OUTPUT
    fi
fi

echo "Summary:"
echo "  Validation mode: $VALIDATION_MODE"
echo "  YAML files to validate: $(echo "$CHANGED_YAML_FILES" | wc -w) files"
echo "  Added/Modified YAML files: $(echo "$ADDED_MODIFIED_YAML_FILES" | wc -w) files"
echo "  Deleted YAML files: $(echo "$DELETED_YAML_FILES" | wc -w) files"
echo "  Affected manifests: $AFFECTED_MANIFESTS"
echo "  Affected overlays: $AFFECTED_OVERLAYS"


if [ -z "${GITHUB_OUTPUT:-}" ]; then
  # We're in Drone CI
  echo "YAML_FILES=$CHANGED_YAML_FILES" > /tmp/changed-files.env
  echo "ADDED_MODIFIED_YAML_FILES=$ADDED_MODIFIED_YAML_FILES" >> /tmp/changed-files.env
  echo "DELETED_YAML_FILES=$DELETED_YAML_FILES" >> /tmp/changed-files.env
  echo "AFFECTED_MANIFESTS=$AFFECTED_MANIFESTS" >> /tmp/changed-files.env
  echo "AFFECTED_OVERLAYS=$AFFECTED_OVERLAYS" >> /tmp/changed-files.env
  echo "VALIDATION_MODE=$VALIDATION_MODE" >> /tmp/changed-files.env

  # Determine if we have any changes worth validating
  if [ -n "$CHANGED_YAML_FILES" ] || [ -n "$AFFECTED_MANIFESTS" ] || [ -n "$AFFECTED_OVERLAYS" ]; then
    echo "HAS_CHANGES=true" >> /tmp/changed-files.env
  else
    echo "HAS_CHANGES=false" >> /tmp/changed-files.env
  fi
fi
