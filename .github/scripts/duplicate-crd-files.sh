#!/bin/bash
set -euo pipefail

# This is a small hack to workaround limitation of kubeconform where it does not support pluralization of resource kinds
# This script duplicates CRD files with pluralized names to work around this limitation
# It should be executed before running kubeconform
# Usage: ./duplicate-crd-files.sh <base-directory> <directory1> <directory2> ...
# eg: uuid.external-secrets.io -> uuids.external-secrets.io

# Base directory
BASE_DIR=$1

# Array of directories to process
DIRECTORIES=("${@:2}")
# Add more directories as needed, e.g.:
# DIRECTORIES=("external-secrets.io" "cert-manager.io" "argoproj.io")


# Function to pluralize a word (simple version)
pluralize() {
  local word="$1"

  # Handle some special cases
  case "$word" in
    # Add special cases as needed
    Policy) echo "Policies" ;;
    # Add more special cases here

    # Default pluralization rules
    *)
      # Words ending in 'y' preceded by a consonant
      if [[ "$word" =~ [^aeiou]y$ ]]; then
        echo "${word%y}ies"
      # Words ending in 's', 'x', 'z', 'ch', 'sh'
      elif [[ "$word" =~ (s|x|z|ch|sh)$ ]]; then
        echo "${word}es"
      # Default: just add 's'
      else
        echo "${word}s"
      fi
      ;;
  esac
}

# Process each directory
for dir in "${DIRECTORIES[@]}"; do
  dir_path="$BASE_DIR/$dir"

  echo "Processing directory: $dir_path"

  # Check if directory exists
  if [ ! -d "$dir_path" ]; then
    echo "Directory $dir_path does not exist, skipping"
    continue
  fi

  # Find all JSON files in the directory
  for file in "$dir_path"/*_*.json; do
    # Check if file exists (in case no matches were found)
    if [ ! -f "$file" ]; then
      echo "No matching files found in $dir_path"
      continue
    fi

    # Extract the filename without path
    filename=$(basename "$file")

    # Split the filename by underscore
    resource_kind=$(echo "$filename" | cut -d'_' -f1)
    api_version=$(echo "$filename" | cut -d'_' -f2-)

    # Create pluralized resource kind
    plural_resource_kind=$(pluralize "$resource_kind")

    # Create new filename
    plural_filename="${plural_resource_kind}_${api_version}"

    # Skip if the file is already pluralized
    if [ "$resource_kind" == "$plural_resource_kind" ]; then
      echo "Skipping $filename - already appears to be pluralized"
      continue
    fi

    # Skip if the pluralized file already exists
    if [ -f "$dir_path/$plural_filename" ]; then
      echo "Skipping $filename - pluralized version already exists"
      continue
    fi

    # Copy the file with the pluralized name
    echo "Duplicating $filename to $plural_filename"
    cp "$file" "$dir_path/$plural_filename"
  done
done

echo "Duplication complete!"
