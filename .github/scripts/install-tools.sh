#!/bin/bash
set -euo pipefail

# Install validation tools based on the tool name
# Usage: ./install-tools.sh <tool1> <tool2> ...

# Create temporary directory for downloads and extractions
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

echo "Using temporary directory: $TEMP_DIR"

install_yamllint() {
    echo "Installing yamllint..."
    sudo apt-get update && sudo apt-get install -y yamllint
}

install_kubeconform() {
    echo "Installing kubeconform..."
    cd "$TEMP_DIR"
    wget -O kubeconform.tar.gz https://github.com/yannh/kubeconform/releases/download/v0.6.4/kubeconform-linux-amd64.tar.gz
    tar -xzf kubeconform.tar.gz
    sudo mv kubeconform /usr/local/bin/
    cd - > /dev/null
    echo "kubeconform installed successfully (CRDs catalog will be cloned when needed)"
}

install_kube_linter() {
    echo "Installing kube-linter..."
    cd "$TEMP_DIR"
    wget -O kube-linter-linux.tar.gz https://github.com/stackrox/kube-linter/releases/download/v0.6.8/kube-linter-linux.tar.gz
    tar -xzf kube-linter-linux.tar.gz
    sudo mv kube-linter /usr/local/bin/
    cd - > /dev/null
}

install_conftest() {
    echo "Installing conftest..."
    cd "$TEMP_DIR"
    wget https://github.com/open-policy-agent/conftest/releases/download/v0.51.0/conftest_0.51.0_Linux_x86_64.tar.gz
    tar -xzf conftest_0.51.0_Linux_x86_64.tar.gz --overwrite
    sudo mv conftest /usr/local/bin/
    cd - > /dev/null
}

install_kustomize() {
    echo "Installing kustomize..."
    cd "$TEMP_DIR"
    curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
    sudo mv kustomize /usr/local/bin/
    cd - > /dev/null
}

install_jq() {
    echo "Installing jq..."
    sudo apt-get update && sudo apt-get install -y jq
}

# Install requested tools
for tool in "$@"; do
    case "$tool" in
        yamllint)
            install_yamllint
            ;;
        kubeconform)
            install_kubeconform
            ;;
        kube-linter)
            install_kube_linter
            ;;
        conftest)
            install_conftest
            ;;
        kustomize)
            install_kustomize
            ;;
        jq)
            install_jq
            ;;
        *)
            echo "Unknown tool: $tool"
            exit 1
            ;;
    esac
done

echo "All requested tools installed successfully!"
