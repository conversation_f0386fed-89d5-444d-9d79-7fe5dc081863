kind: pipeline
type: docker
name: treebo-service-operator

# platform:
#   os: linux
#   arch: amd64

# trigger:
#   # branch:
#   #   - main
#   # event:
#   #   - push
#   paths:
#     include:
#       - tools/treebo-service-operator/**

# workspace:
#   path: /drone/src

steps:
  - name: build-image
    image: plugins/ecr
    settings:
      dockerfile: tools/woodpecker-toleration-webhook/Dockerfile
      context: tools/woodpecker-toleration-webhook
      registry: 605536185498.dkr.ecr.ap-south-1.amazonaws.com
      repo: eks-woodpecker-toleration-webhook
      tags:
        - latest
        - ${DRONE_COMMIT_SHA:0:8}
    environment:
      AWS_REGION: ap-south-1
# volumes:
#   - name: docker
#     host:
#       path: /var/run/docker.sock
