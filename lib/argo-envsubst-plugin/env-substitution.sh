#!/bin/bash
set -e

# Usage: ./env-substitution.sh <repository-root>
# This script finds all overlay directories with env.yaml files,
# loads the variables, and substitutes them in all YAML files in that overlay.

if [ $# -lt 1 ]; then
  echo "Usage: $0 <repository-root>"
  exit 1
fi

REPO_ROOT="$1"
echo "Processing repository: $REPO_ROOT"

# Find all overlay directories
find "$REPO_ROOT" -type d -name "overlays" | while read -r OVERLAYS_DIR; do
  echo "Found overlays directory: $OVERLAYS_DIR"
  
  # Process each subdirectory in the overlays directory
  find "$OVERLAYS_DIR" -mindepth 1 -maxdepth 1 -type d | while read -r OVERLAY_DIR; do
    ENV_YAML="$OVERLAY_DIR/env.yaml"
    
    # Check if env.yaml exists
    if [ ! -f "$ENV_YAML" ]; then
      echo "Skipping $OVERLAY_DIR: no env.yaml found"
      continue
    fi
    
    echo "Processing overlay: $OVERLAY_DIR with env.yaml"
    
    # Create a temporary .env file from env.yaml
    TMP_ENV_FILE=$(mktemp)
    yq eval '.[] | key + "=" + . | envsubst' "$ENV_YAML" > "$TMP_ENV_FILE" 2>/dev/null || {
      # If yq fails, try a simpler approach for flat YAML
      grep -v '^#' "$ENV_YAML" | sed 's/: */=/' > "$TMP_ENV_FILE"
    }
    
    # Source the environment variables
    set -a
    source "$TMP_ENV_FILE"
    set +a
    
    echo "Loaded environment variables from $ENV_YAML"
    
    # Process all YAML files in the overlay directory (except env.yaml)
    find "$OVERLAY_DIR" -type f \( -name "*.yaml" -o -name "*.yml" \) -not -name "env.yaml" | while read -r YAML_FILE; do
      echo "Processing file: $YAML_FILE"
      
      # Create a temporary file for the processed content
      TMP_FILE=$(mktemp)
      
      # Process the file with envsubst
      envsubst < "$YAML_FILE" > "$TMP_FILE"
      
      # Replace the original file with the processed one
      mv "$TMP_FILE" "$YAML_FILE"
    done
    
    # Clean up
    rm "$TMP_ENV_FILE"
  done
  
  # Also check if there's an env.yaml directly in the overlays directory
  ENV_YAML="$OVERLAYS_DIR/env.yaml"
  if [ -f "$ENV_YAML" ]; then
    echo "Processing overlay directory itself: $OVERLAYS_DIR with env.yaml"
    
    # Create a temporary .env file from env.yaml
    TMP_ENV_FILE=$(mktemp)
    yq eval '.[] | key + "=" + . | envsubst' "$ENV_YAML" > "$TMP_ENV_FILE" 2>/dev/null || {
      # If yq fails, try a simpler approach for flat YAML
      grep -v '^#' "$ENV_YAML" | sed 's/: */=/' > "$TMP_ENV_FILE"
    }
    
    # Source the environment variables
    set -a
    source "$TMP_ENV_FILE"
    set +a
    
    echo "Loaded environment variables from $ENV_YAML"
    
    # Process all YAML files in the overlay directory (except env.yaml)
    find "$OVERLAYS_DIR" -type f \( -name "*.yaml" -o -name "*.yml" \) -not -name "env.yaml" | while read -r YAML_FILE; do
      echo "Processing file: $YAML_FILE"
      
      # Create a temporary file for the processed content
      TMP_FILE=$(mktemp)
      
      # Process the file with envsubst
      envsubst < "$YAML_FILE" > "$TMP_FILE"
      
      # Replace the original file with the processed one
      mv "$TMP_FILE" "$YAML_FILE"
    done
    
    # Clean up
    rm "$TMP_ENV_FILE"
  fi
done

echo "Environment variable substitution completed"