# ArgoCD Environment Variable Substitution Plugin

This plugin substitutes environment variables in YAML files before kustomize runs in ArgoCD.

## How it works

1. The plugin looks for all `overlays` directories in your repository
2. For each overlay directory (or its subdirectories), it checks for an `env.yaml` file
3. If found, it loads the variables from `env.yaml` and substitutes them in all YAML files in that overlay
4. The substitution happens before kustomize runs

## Installation

### 1. Build and push the Docker image

```bash
cd lib/argo-envsubst-plugin
docker build -t your-registry/env-substitution-plugin:latest .
docker push your-registry/env-substitution-plugin:latest
```

### 2. Configure ArgoCD

Add the plugin to your ArgoCD ConfigMap:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  namespace: argocd
data:
  configManagementPlugins: |
    - name: env-substitution-plugin
      init:
        command: ["/bin/sh", "-c"]
        args: ["echo 'Plugin initialized'"]
      generate:
        command: ["/app/env-substitution.sh"]
        args: ["{{.RepoRoot}}"]
```

Patch the ArgoCD repo server to include the plugin sidecar:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: argocd-repo-server
  namespace: argocd
spec:
  template:
    spec:
      containers:
      - name: env-substitution-plugin
        image: ghcr.io/YOUR_GITHUB_USERNAME/argo-envsubst-plugin:latest
        volumeMounts:
        - mountPath: /tmp
          name: tmp
        - mountPath: /home/<USER>/cmp-server/plugins
          name: plugins
        securityContext:
          runAsNonRoot: true
          runAsUser: 999
      volumes:
      - name: plugins
        emptyDir: {}
```

## Usage

1. Create an `env.yaml` file in each overlay directory:

```yaml
# overlays/staging/env.yaml
DATABASE_URL: ****************************************/mydb
API_KEY: staging-key-123
ENVIRONMENT: staging
```

```yaml
# overlays/production/env.yaml
DATABASE_URL: *************************************/mydb
API_KEY: prod-key-456
ENVIRONMENT: production
```

2. Use these variables in your YAML files:

```yaml
# overlays/staging/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: DATABASE_URL
          value: ${DATABASE_URL}
        - name: API_KEY
          value: ${API_KEY}
        - name: ENV
          value: ${ENVIRONMENT}
```

3. In your Application manifest, specify the plugin:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: my-app
spec:
  source:
    plugin:
      name: env-substitution-plugin
```

The plugin will substitute the variables before kustomize runs.