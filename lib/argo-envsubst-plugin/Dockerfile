FROM alpine:3.16

# Install required tools
RUN apk add --no-cache bash gettext findutils yq kustomize

# Set up directories for ArgoCD plugin integration
RUN mkdir -p /custom-tools /home/<USER>/cmp-server/plugins/env-substitution-plugin

# Copy the script and plugin definition
COPY env-substitution.sh /custom-tools/
COPY plugin.yaml /custom-tools/
COPY plugin.yaml /home/<USER>/cmp-server/plugins/env-substitution-plugin/

# Set proper permissions
RUN chmod +x /custom-tools/env-substitution.sh

# Keep container running for sidecar usage
CMD ["sh", "-c", "while true; do sleep 10; done"]
