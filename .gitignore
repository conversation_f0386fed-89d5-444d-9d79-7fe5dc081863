# Node dependencies
node_modules/

# CDK/CDK8s outputs
dist/
cdk.out/

# Logs
*.log

# IDE/editor files
.idea/
.vscode/*
!.vscode/settings.json
*.swp

# OS files
.DS_Store
Thumbs.db

# Environment files
.env
.env.*
.envrc

.venv/

# Coverage
coverage/

# Optional: Python cache if any
__pycache__/

# Ignore synthesized manifests (if not wanting to commit)
# tools-management/dist/

# From the Kustomize documentation:
# The charts directory is created by Kustomize to store Helm charts locally. It should not be committed to version control.
/tools/**/charts/*

# exclude typesense charts from being ignored. we need them committed
!tools/typesense-distributed/charts/*

**/.ruff_cache/

# Go
bin/
