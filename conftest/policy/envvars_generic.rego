package envvars

# Extract all env var names from the base deployment
base_env_vars = {env.name | input[_].kind == "Deployment";
  env := input[_].spec.template.spec.containers[_].env[_]
  env.name != null
}

# Gather all keys from all Secrets and ConfigMaps in the overlay
all_secret_keys = {k | s := input[_]; s.kind == "Secret"; k := s.data[_]}
all_configmap_keys = {k | c := input[_]; c.kind == "ConfigMap"; k := c.data[_]}

# A required env var is missing if it's not in any Secret or ConfigMap
missing_envs[env] {
  env := base_env_vars[_]
  not all_secret_keys[env]
  not all_configmap_keys[env]
}

deny[msg] {
  missing_envs[env]
  msg := sprintf("Missing env var %s in overlay secrets/configmaps", [env])
}
