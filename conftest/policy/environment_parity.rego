package environment_parity

# Environment-specific validation rules

# Required resources for production environments
required_production_resources = {
    "external-secret.yaml",
    "env-configmap.yaml", 
    "hpa.yaml"
}

# Resources that should be consistent between environments
consistent_resources = {
    "service.yaml",
    "ingress.yaml",
    "deployment-patch.yaml"
}

# Check if this is a production overlay
is_production_overlay {
    input.metadata.labels.environment == "production"
}

# Check if this is a staging overlay  
is_staging_overlay {
    input.metadata.labels.environment == "staging"
}

# Deny if production overlay is missing critical resources
deny[msg] {
    is_production_overlay
    # This would need to be enhanced to check file existence
    # For now, we check if certain annotations/labels indicate missing resources
    input.kind == "Kustomization"
    not input.metadata.annotations["validated.production.resources"]
    msg := "Production overlay must include all required resources (external-secret, env-configmap, hpa)"
}

# Warn about environment-specific configurations
warn[msg] {
    input.kind == "Deployment"
    container := input.spec.template.spec.containers[_]
    env := container.env[_]
    
    # Check for hardcoded staging/production values
    contains(env.value, "staging")
    msg := sprintf("Hardcoded 'staging' value found in env var %s", [env.name])
}

warn[msg] {
    input.kind == "Deployment" 
    container := input.spec.template.spec.containers[_]
    env := container.env[_]
    
    # Check for hardcoded staging/production values
    contains(env.value, "production")
    msg := sprintf("Hardcoded 'production' value found in env var %s", [env.name])
}

# Validate image tags are not 'latest' in production
deny[msg] {
    is_production_overlay
    input.kind == "Deployment"
    container := input.spec.template.spec.containers[_]
    endswith(container.image, ":latest")
    msg := sprintf("Container %s uses 'latest' tag in production", [container.name])
}

# Validate resource limits are set in production
warn[msg] {
    is_production_overlay
    input.kind == "Deployment"
    container := input.spec.template.spec.containers[_]
    not container.resources.limits
    msg := sprintf("Container %s missing resource limits in production", [container.name])
}

# Validate replicas are appropriate for environment
warn[msg] {
    input.kind == "Deployment"
    is_staging_overlay
    input.spec.replicas > 3
    msg := sprintf("Staging deployment has %d replicas, consider reducing for cost optimization", [input.spec.replicas])
}

deny[msg] {
    input.kind == "Deployment"
    is_production_overlay  
    input.spec.replicas < 2
    msg := sprintf("Production deployment has only %d replica(s), minimum 2 recommended for HA", [input.spec.replicas])
}
