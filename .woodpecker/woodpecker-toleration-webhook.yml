variables:
  - &registry_url "605536185498.dkr.ecr.ap-south-1.amazonaws.com"
  - &repo_name "eks-woodpecker-toleration-webhook"
  - &app_path "tools/woodpecker-toleration-webhook"
  - &platforms "linux/amd64"

when:
  - event: [push, pull_request, tag, manual]
  - path:
      include:
        - "tools/woodpecker-toleration-webhook/**"
        - ".woodpecker/woodpecker-toleration-webhook.yml"

steps:
  # Lint Go code
  # lint:
  #   image: golang:1.24
  #   environment:
  #     APP_PATH: *app_path
  #   commands:
  #     - cd $APP_PATH
  #     - go mod download
  #     - go vet ./...
  #     - go fmt -l . | tee /tmp/fmt-output && test ! -s /tmp/fmt-output
  #     - |
  #       if command -v golangci-lint >/dev/null 2>&1; then
  #         golangci-lint run
  #       else
  #         echo "golangci-lint not available, skipping"
  #       fi

  # Test Go code
  # test:
  #   image: golang:1.24
  #   environment:
  #     APP_PATH: *app_path
  #   commands:
  #     - cd $APP_PATH
  #     - go mod download
  #     - go test -v ./...

  build:
    image: thulasi503/docker-buildx
    settings:
      repo: 605536185498.dkr.ecr.ap-south-1.amazonaws.com/eks-woodpecker-toleration-webhook
      registry: https://605536185498.dkr.ecr.ap-south-1.amazonaws.com
      platforms: "linux/amd64"
      context: *app_path
      aws_region: ap-south-1
      dockerfile: tools/woodpecker-toleration-webhook/Dockerfile
      debug: true
      # ecr_create_repository: true
      tags:
        - latest
        - ${CI_COMMIT_SHA:0:8}
        - ${CI_COMMIT_BRANCH}
      build_args:
        - BUILDKIT_INLINE_CACHE=1
        - BUILD_DATE=${CI_COMMIT_DATE}
        - VCS_REF=${CI_COMMIT_SHA}
        - VERSION=${CI_COMMIT_TAG:-${CI_COMMIT_BRANCH}-${CI_COMMIT_SHA:0:8}}
      # cache_from:
      #   - ${registry_url}/${repo_name}:latest
      #   - ${registry_url}/${image_name}:buildcache
      # cache_to:
      #   - ${registry_url}/${image_name}:buildcache
    when:
      - event: [push, pull_request, tag, manual]
